package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *
 * 同步ep 返利金额 （车系以及政策汇总金额）汇总数据表
 *
 *
 * <AUTHOR> @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sync_ep_rebate_amount")
@Schema(description = "数据:rebate同步ep金额数据")
public class RebateSyncEpSalTotalAmountDO extends Model<RebateSyncEpSalTotalAmountDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;


    @Schema(description = "数据-额外参数Json")
    @TableField("extended_param")
    private String extendedParams;


    @Schema(description = "数据-结构性车系金额jsonArray")
    @TableField("target_series_value")
    private String targetSeriesValue;

    @Schema(description = "数据-结构性政策返利金额jsonArray")
    @TableField("target_policy_value")
    private String targetPolicyValue;


    @Schema(description = "数据-数据分组type标识 1大区分组 2:经销商分组")
    @TableField("group_type")
    private Integer groupType;


    @Schema(description = "数据-分组名称 如某大区,某经销商")
    @TableField("target_group_name")
    private String targetGroupName;


    @Schema(description = "数据-业务数据时间")
    @TableField(value = "business_time")
    private String businessTime;


    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
