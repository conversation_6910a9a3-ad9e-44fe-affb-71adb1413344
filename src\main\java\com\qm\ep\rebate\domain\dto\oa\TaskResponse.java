package com.qm.ep.rebate.domain.dto.oa;

import lombok.Data;

import java.util.List;

@Data
public class TaskResponse {
    /**
     * 代理人
     */
    private Object agent;

    /**
     * 执行URL
     */
    private String executionUrl;

    /**
     * 活动信息
     */
    private Object activity;

    /**
     * 父任务ID
     */
    private Object parentTaskId;

    /**
     * 截止日期
     */
    private String dueDate;

    /**
     * 流程实例信息
     */
    private Object processInstance;

    /**
     * 是否超时
     */
    private boolean outtime;

    /**
     * 描述
     */
    private Object description;

    /**
     * 流程定义名称
     */
    private String processDefinitionName;

    /**
     * 来源
     */
    private Object source;

    /**
     * 委托状态
     */
    private Object delegationState;

    /**
     * 原始所有者
     */
    private Object originalOwner;

    /**
     * 操作
     */
    private Object operations;

    /**
     * 是否警告
     */
    private boolean warning;

    /**
     * 分配的参与者
     */
    private Object assigneeParticipant;

    /**
     * 任务ID
     */
    private String id;

    /**
     * 父任务URL
     */
    private Object parentTaskUrl;

    /**
     * 所有者
     */
    private Object owner;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 流程定义ID
     */
    private String processDefinitionId;

    /**
     * 类别响应
     */
    private Object categoryResponse;

    /**
     * 变量列表
     */
    private List<Object> variables;

    /**
     * 表单键
     */
    private Object formKey;

    /**
     * 任务评论
     */
    private Object taskComments;

    /**
     * 优先级
     */
    private int priority;

    /**
     * URL
     */
    private String url;

    /**
     * 是否挂起
     */
    private boolean suspended;

    /**
     * 任务定义键
     */
    private String taskDefinitionKey;

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 流程实例URL
     */
    private String processInstanceUrl;

    /**
     * 候选参与者
     */
    private Object candidateParticipants;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 名称
     */
    private String name;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 流程实例开始时间
     */
    private String procInsStartTime;

    /**
     * 分配人
     */
    private String assignee;

    /**
     * 类别
     */
    private String category;

    /**
     * 流程定义URL
     */
    private String processDefinitionUrl;
}
