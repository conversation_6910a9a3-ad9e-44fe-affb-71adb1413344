package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "数据:董事会政策回应")
@Data
public class BoardPolicyResponse {

    /**
     * 政策逾期数
     */
    @Schema(description = "政策逾期数")
    private int delayCount;
    /**
     * 新增数(较昨日)
     */
    @Schema(description = "新增数(较昨日)")
    private String changedCount;
    /**
     * 近30天逾期政策的数量
     */
    @Schema(description = "近30天逾期政策的数量")
    private Integer monthDelayCount;
    /**
     * 年度累积逾期政策的数量
     */
    @Schema(description = "年度累积逾期政策的数量")
    private Integer yearDelayCount;

}
