package com.qm.ep.rebate.domain.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目名称：yiqi-project
 * 类 名 称：RebateAmountDetailResponse
 * 类 描 述：TODO
 * 创建时间：2024/2/21 下午5:11
 * 创 建 人：cuihaochuan
 */

@Data
@Schema(description = "数据:返利简报")
public class RebateAmountDetailResponse {

    @Schema(description = "数据-名称")
    private String name;

    @Schema(description = "数据-金额")
    private BigDecimal amount;

    @Schema(description = "数据-大区名称")
    private  String regionName;

    @Schema(description = "数据-经销商代码")
    private String vdealerCode;

    @Schema(description = "数据-数据类型")
    private String dataType;

    @Schema(description = "数据-排序")
    private Integer sort;


}
