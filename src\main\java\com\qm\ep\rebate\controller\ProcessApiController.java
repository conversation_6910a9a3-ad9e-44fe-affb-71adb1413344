package com.qm.ep.rebate.controller;

import cn.hutool.json.JSONUtil;
import com.qm.ep.rebate.common.base.ProcessResult;
import com.qm.ep.rebate.domain.bean.BusinessDataDO;
import com.qm.ep.rebate.domain.dto.approve.ProcessHistoryQueryDTO;
import com.qm.ep.rebate.domain.dto.oa.BPMRequest;
import com.qm.ep.rebate.domain.request.CallBackDTO;
import com.qm.ep.rebate.service.AiCallBackService;
import com.qm.ep.rebate.service.approve.ProcessApiService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description：对接工作流api
 * @date ：Created in 2023/7/12 11:00
 * @version: $
 */

@Tag(description = "/processApi", name = "对接工作流api")
@RestController
@RequestMapping(value = "/processApi")

@Slf4j
@RefreshScope
public class ProcessApiController {

    @Resource
    private AiCallBackService aiCallBackService;
    @Value("${oa.getBusinessDetail:true}")
    private boolean isNewGetBusinessDetail;
    @Value("${oa.old:false}")
    private boolean openOldOA;

    @Resource
    private ProcessApiService processApiService;

    /**
     * 提供给工作流系统查询审批详情 (portal）
     *
     * @param entity 请求
     * @return
     */
    @Operation(summary = "审批详情页", description = "[author: 10200571]")
    @RequestMapping(value = "/getBusinessDetail", method = RequestMethod.POST)
    public String getBusinessDetail(@RequestBody @Validated Object entity) {
        try {
            String businessDetail ;
            if (isNewGetBusinessDetail) {
                businessDetail = processApiService.getBusinessDetailV2(entity);
            }else{
                businessDetail = processApiService.getBusinessDetail(entity);
            }
            if (StringUtils.isBlank(businessDetail)) {
                log.info("processApiService.getBusinessDetail(entity={}),return empty.", entity);
            }
            return businessDetail;
        } catch (RuntimeException ex) {
            log.info("processApiService.getBusinessDetail(entity={}) exception.", entity, ex);
            return "";
        }
    }

    /**
     * 监听工作流回调
     *
     * @param request 请求
     * @return
     */
    @Operation(summary = "监听工作流回调", description = "[author: 10200571]")
    @RequestMapping(value = "/listenerBPMCallBack", method = RequestMethod.POST)
    public Object listenerBPMCallBack(@RequestBody BPMRequest request) {
        // 新数据
        processApiService.listenerBPMCallBackV2(request);
        return ProcessResult.success("Success");
    }

    /**
     * 获取流程历史
     *
     * @param entity 请求
     * @return
     */
    @Operation(summary = "获取流程历史", description = "[author: 10200571]")
    @RequestMapping(value = "/getProcessHistory", method = RequestMethod.POST)
    public Object getProcessHistory(@RequestBody @Validated ProcessHistoryQueryDTO entity) {
        return processApiService.getProcessHistory(entity);
    }


    @Operation(summary = "ai回调", description = "[author: 10200571]")
    @PostMapping("/aiCallback")
    public JsonResultVo<String> aiCallback(@RequestBody CallBackDTO request) {
        log.info("ai回调参数：{}", JSONUtil.toJsonStr(request));
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(aiCallBackService.aiCallback(request));
        return result;
    }

    @Operation(summary = "表单结构", description = "[author: 10200571]")
    @PostMapping("/formStructure")
    public JsonResultVo<String> aiCallback(@RequestBody BusinessDataDO request) {

        return new JsonResultVo<String>();
    }


}
