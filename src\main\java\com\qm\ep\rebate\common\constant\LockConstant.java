package com.qm.ep.rebate.common.constant;

/**
 * 锁常量类
 * <AUTHOR>
 */
public class LockConstant {

    private LockConstant() {
    }


    /**
     * 返利入账申请锁
     */
    public static final String REBATE_ENTRY_APPLY_LOCK = "rebate:entry:apply:lock:";
    /**
     * 入账审批回调锁
     */
    public static final String REBATE_ENTRY_AUDIT_LOCK = "rebate:entry:audit:lock:";

    /**
     * 返利折让审批锁
     */
    public static final String REBATE_EXTRACT_AUDIT_LOCK = "rebate:extract:audit:lock:";
    /**
     * 审批流过程中存审核授权实体的key
     */
    public static final String SWZC_REBATE_BUSINESSKEY = "swzc:rebate:businessKey:";


    /**
     * 返利入账申请锁
     */
    public static final String REBATE_EXTRACT_SUBMIT_APPLY_LOCK = "rebate:extract:submit:apply:lock:";

    /**
     * 返利入账申请锁
     */
    public static final String REBATE_EXTRACT_START_CAL_LOCK = "rebate:extract:start:cal:lock:";
}
