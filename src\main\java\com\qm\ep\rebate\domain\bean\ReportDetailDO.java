package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REPORT_DETAIL")
@Schema(description = "数据:报表明细详情表对象")
public class ReportDetailDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-报表ID")
    @TableField("REPORT_ID")
    private String reportId;

    @Schema(description = "数据-商务政策标识")
    @TableField("POLICY_ID")
    private String policyId;

    @Schema(description = "数据-表标识")
    @TableField("TABLE_ID")
    private String tableId;

    @Schema(description = "数据-字段标识")
    @TableField("FIELD_ID")
    private String fieldId;

    /** 数据源类型（basic-业务底表,factor-计算因子,formula-计算公式,section-区间公式,premise-前提条件,plan-计算方案） */
    @Schema(description = "数据-数据源类型")
    @TableField("SOURCE_TYPE")
    private String sourceType;

    @Schema(description = "数据-字段类型")
    @TableField("TYPE")
    private String type;

    @Schema(description = "数据-重命名")
    @TableField("ALIAS_NAME")
    private String aliasName;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;



}
