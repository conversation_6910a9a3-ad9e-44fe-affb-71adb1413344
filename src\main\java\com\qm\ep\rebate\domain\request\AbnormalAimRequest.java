package com.qm.ep.rebate.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:异常瞄准请求")
@Data
@Builder
public class AbnormalAimRequest {
    @Schema(description = "中心代码")
    private String centerCode;
    @Schema(description = "创建日期")
    private String createdDate;
    @Schema(description = "消息源")
    private String messageSource;
    @Schema(description = "异常瞄准列表")
    private List<AbnormalAim> abnormalAimList;

    @Schema(description = "数据:目标异常")
    @Data
    public static class AbnormalAim {
        @Schema(description = "瞄准代码")
        private String aimCode;
        @Schema(description = "错误内容")
        private String errorContent;
    }
}
