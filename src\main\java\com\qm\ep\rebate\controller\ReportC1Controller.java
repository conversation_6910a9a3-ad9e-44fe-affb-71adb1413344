package com.qm.ep.rebate.controller;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.domain.bean.ReportMainDO;
import com.qm.ep.rebate.domain.dto.ReportMainDTO;
import com.qm.ep.rebate.domain.vo.ReportC1VO;
import com.qm.ep.rebate.mapper.ReportMainMapper;
import com.qm.ep.rebate.service.ReportMainService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 *  
 *Controller
 * 报表明细主表JsonResultVo
 *
 * <AUTHOR>
 * @since 2022-09-21
 */
@Tag(name ="报表明细主表")
@RestController
@RequestMapping("/reportC1")
public class ReportC1Controller extends BaseController {

    @Autowired
    private ReportMainService  reportMainService;

    @Autowired
    private ReportMainMapper reportMainMapper;
    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<ReportMainDO> deleteById(@RequestBody ReportMainDO tempDO){
        JsonResultVo<ReportMainDO> resultObj = new JsonResultVo<>();
        boolean flag = reportMainService.removeById(tempDO.getId());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }
    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/table")
    @DS(DataSourceType.W)
    public JsonResultVo<QmPage<ReportMainDO>> table(@RequestBody ReportMainDTO tempDTO){
        //定义查询构造器
        QmQueryWrapper<ReportMainDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<ReportMainDO> lambdaWrapper = queryWrapper.lambda();
        //报表名称
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(tempDTO.getReportName()), ReportMainDO::getReportName, tempDTO.getReportName());
        //报表类型：c1
        lambdaWrapper.eq( ReportMainDO::getReportType, "c1");
        //起始截止日期
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getStartTime())) {
            //操作日期
            if (RebateConstants.CREATE_TIME.equals(tempDTO.getDateType())) {
                lambdaWrapper.ge(ReportMainDO::getCreateOn, tempDTO.getStartTime());
            } else if (RebateConstants.UPDATE_TIME.equals(tempDTO.getDateType())) {
                lambdaWrapper.ge(ReportMainDO::getUpdateOn, tempDTO.getStartTime());
            }
        }
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getEndTime())) {
            //操作日期
            if (RebateConstants.CREATE_TIME.equals(tempDTO.getDateType())) {
                lambdaWrapper.le(ReportMainDO::getCreateOn, tempDTO.getEndTime());
            } else if (RebateConstants.UPDATE_TIME.equals(tempDTO.getDateType())) {
                lambdaWrapper.le(ReportMainDO::getUpdateOn, tempDTO.getEndTime());
            }
        }

        //查询数据，使用table函数。
        QmPage<ReportMainDO> list = reportMainService.table(queryWrapper,tempDTO);
        JsonResultVo<QmPage<ReportMainDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    // getCombination
    @Operation(summary = "查询已发布的政策中的合并方案", description = "[author: 10200571]")
    @PostMapping("/getCombination")
    public JsonResultVo<List<ReportC1VO>> getCombination(@RequestBody ReportMainDTO tempDTO){
        JsonResultVo<List<ReportC1VO>> ret = new JsonResultVo<>();
        List<ReportC1VO> list = reportMainMapper.getCombination();
        for(ReportC1VO report:list){
            report.setFieldname(reportMainMapper.getFieldname(report));
        }
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询报表关联的合并方案", description = "[author: 10200571]")
    @PostMapping("/getReportC1CashObject")
    public JsonResultVo<List<ReportC1VO>> getReportC1CashObject(@RequestBody ReportMainDTO tempDTO){
        JsonResultVo<List<ReportC1VO>> ret = new JsonResultVo<>();
        List<ReportC1VO> list = reportMainMapper.getReportC1CashObject(Arrays.asList(tempDTO.getCashObject().split(",")));
        for(ReportC1VO report:list){
            report.setFieldname(reportMainMapper.getFieldname(report));
        }
        ret.setData(list);
        return ret;
    }

    // 保存
    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/saveReport")
    public JsonResultVo saveReport(@RequestBody ReportMainDO tempDO){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        String reportId = reportMainService.saveReportC1(tempDO);
        ret.setData(reportId);
        return ret;
    }
}
