package com.qm.ep.rebate.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.ep.rebate.domain.bean.SaleArchivesDO;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "档案对象")
public class SaleArchivesDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-日期类型")
    private String datetype;

    @Schema(description = "数据-创建开始时间")
    private String dbegin;

    @Schema(description = "数据-创建结束时间")
    private String dend;

    @Schema(description = "数据-经销商代码")
    @TableField("dealer_code")
    private String dealerCode;

    @Schema(description = "数据-客户代码")
    @TableField("customer_code")
    private String customerCode;

    @Schema(description = "数据-客户名称")
    @TableField("customer_name")
    private String customerName;

    @Schema(description = "数据-客户类型")
    @TableField("customer_type")
    private String customerType;

    @Schema(description = "数据-移动电话")
    @TableField("mobile")
    private Integer mobile;

    @Schema(description = "数据-职业")
    @TableField("occupation")
    private String occupation;

    @Schema(description = "数据-车架号")
    @TableField("vin")
    private String vin;

    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    @Schema(description = "数据-车型")
    @TableField("model")
    private String model;

    @Schema(description = "数据-EV标识")
    @TableField("ev_flag")
    private Integer evFlag;

    @Schema(description = "数据-采购日期")
    @TableField("purchase_date")
    private Date purchaseDate;

    @Schema(description = "数据-采购价格")
    @TableField("purchase_price")
    private Double purchasePrice;

    @Schema(description = "数据-入库日期")
    @TableField("in_date")
    private Date inDate;

    @Schema(description = "数据-退库日期")
    @TableField("out_date")
    private Date outDate;

    @Schema(description = "数据-零售日期")
    @TableField("sale_date")
    private Date saleDate;

    @Schema(description = "数据-零售价格")
    @TableField("sale_price")
    private Double salePrice;

    @Schema(description = "数据-开票日期")
    @TableField("invoice_date")
    private Date invoiceDate;

    @Schema(description = "数据-开票价格")
    @TableField("invoice_price")
    private Double invoicePrice;

    @Schema(description = "数据-车牌号")
    @TableField("license")
    private String license;

    @Schema(description = "数据-上牌日期")
    @TableField("registration_date")
    private Date registrationDate;

    @Schema(description = "数据-上牌省份")
    @TableField("registration_province")
    private String registrationProvince;

    @Schema(description = "数据-上牌城市")
    @TableField("registration_city")
    private String registrationCity;

    @Schema(description = "数据-购车方式")
    @TableField("purchase_method")
    private String purchaseMethod;

    @Schema(description = "数据-车辆用途")
    @TableField("vehicle_usage")
    private String vehicleUsage;

    @Schema(description = "数据-保险日期")
    @TableField("insurance_date")
    private Date insuranceDate;

    @Schema(description = "数据-保险年限")
    @TableField("Insurance_month")
    private Integer insuranceMonth;

    @Schema(description = "数据-贷款年限")
    @TableField("loan_date")
    private Date loanDate;

    @Schema(description = "数据-金融机构")
    @TableField("financial_institution")
    private String financialInstitution;

    @Schema(description = "数据-创建日期")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    private Date createon;

    @Schema(description = "数据-创建人")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createby;

    @Schema(description = "数据-修改日期")
    @TableField(value ="updateon", fill = FieldFill.INSERT_UPDATE)
    private Date updateon;

    @Schema(description = "数据-修改人")
    @TableField(value ="updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateby;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "数据-品牌")
    @TableField(value = "BRAND", exist = false)
    private String brand;

    @Schema(description = "数据-经销商名称")
    @TableField(value = "DEALER_NAME", exist = false)
    private String dealerName;

    @Schema(description = "数据-经销商简称")
    @TableField(value = "DEALER_ABBREVIATION", exist = false)
    private String dealerAbbreviation;
    @Schema(description = "数据-导入列表")
    private List<SaleArchivesDO> list;

}
