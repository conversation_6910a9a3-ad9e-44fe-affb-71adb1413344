package com.qm.ep.rebate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebate.domain.bean.NotificationDO;
import com.qm.ep.rebate.domain.bean.NotificationUserDO;
import com.qm.ep.rebate.domain.dto.NotificationDTO;
import com.qm.ep.rebate.domain.dto.NotificationUserDTO;
import com.qm.ep.rebate.service.NotificationService;
import com.qm.ep.rebate.service.NotificationUserService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.TableUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/notification")
@Tag(name = "通知")
public class NotificationController extends BaseController {

    @Resource
    private NotificationService notificationService;

    @Resource
    private NotificationUserService notificationUserService;

    @Operation(summary = "通知列表", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<NotificationDO>> table(@RequestBody NotificationDTO notificationDTO){
        JsonResultVo<QmPage<NotificationDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<NotificationDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<NotificationDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.like(StrUtil.isNotBlank(notificationDTO.getNotifyNo()), NotificationDO::getNotifyNo, notificationDTO.getNotifyNo());
        lambdaWrapper.like(StrUtil.isNotBlank(notificationDTO.getTitle()), NotificationDO::getTitle, notificationDTO.getTitle());
        lambdaWrapper.like(StrUtil.isNotBlank(notificationDTO.getPrinciple()), NotificationDO::getPrinciple, notificationDTO.getPrinciple());
        lambdaWrapper.like(StrUtil.isNotBlank(notificationDTO.getFormalBg()), NotificationDO::getFormalBg, notificationDTO.getFormalBg());
        lambdaWrapper.in(CollUtil.isNotEmpty(notificationDTO.getOrgCodes()), NotificationDO::getOrgCode, notificationDTO.getOrgCodes());
        lambdaWrapper.in(CollUtil.isNotEmpty(notificationDTO.getStatuses()), NotificationDO::getStatus, notificationDTO.getStatuses());
        lambdaWrapper.ge(notificationDTO.getStartTime()!=null, NotificationDO::getCreateOn, notificationDTO.getStartTime());
        lambdaWrapper.ge(notificationDTO.getEndTime()!=null, NotificationDO::getCreateOn, notificationDTO.getEndTime());

        QmPage<NotificationDO> data = notificationService.table(queryWrapper, notificationDTO);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "获取我收到的通知", description = "[author: 10200571]")
    @PostMapping("/getReceivedNotifications")
    public JsonResultVo<QmPage<NotificationDO>> getReceivedNotifications(@RequestBody NotificationUserDTO notificationUserDTO){
        JsonResultVo<QmPage<NotificationDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<NotificationDO> queryWrapper = new QmQueryWrapper<>();
        IPage<NotificationDO> list = notificationService.getReceivedNotifications(queryWrapper, notificationUserDTO);

        QmPage<NotificationDO> qmPage = TableUtils.convertQmPageFromMpPage(list);
        jsonResultVo.setData(qmPage);
        return jsonResultVo;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<NotificationDO> save(@RequestBody NotificationDTO notificationDTO){
        JsonResultVo<NotificationDO> ret = new JsonResultVo<>();

        NotificationDO notificationDO = new NotificationDO();
        if(StrUtil.isNotBlank(notificationDTO.getId())) {
            notificationDO = notificationService.getById(notificationDTO.getId());
        }

        BeanUtil.copyProperties(notificationDTO, notificationDO, "receiverUsers");
        notificationService.saveOrUpdate(notificationDO);

        QmQueryWrapper<NotificationUserDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<NotificationUserDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(NotificationUserDO::getNotifyId, notificationDO.getId());
        notificationUserService.remove(lambdaWrapper);
        List<NotificationUserDTO> receiverUsers = notificationDTO.getReceiverUsers();
        List<NotificationUserDO> notificationUsers = new ArrayList<>();
        if(CollUtil.isNotEmpty(receiverUsers)) {
            for (NotificationUserDTO receiverUser : receiverUsers) {
                receiverUser.setNotifyId(notificationDO.getId());
                NotificationUserDO notificationUserDO = new NotificationUserDO();
                BeanUtil.copyProperties(receiverUser, notificationUserDO);
                notificationUsers.add(notificationUserDO);
            }
            notificationUserService.saveBatch(notificationUsers);
        }

        ret.setData(notificationDO);
        return ret;
    }


    @Operation(summary = "获取详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<NotificationDO> getDetail(@RequestBody NotificationDTO notificationDTO){
        JsonResultVo<NotificationDO> ret = new JsonResultVo<>();
        NotificationDO notificationDO = notificationService.getById(notificationDTO.getId());
        ret.setData(notificationDO);
        return ret;
    }

    @Operation(summary = "删除", description = "[author: 10200571]")
    @PostMapping("/deleteByIds")
    public JsonResultVo<Boolean> deleteByIds(@RequestBody List<String> ids){
        JsonResultVo<Boolean> ret = new JsonResultVo<>();
        if(CollUtil.isEmpty(ids)) {
            ret.setMsgErr("删除失败！ID列表不能为空！");
        } else {
            LambdaQueryWrapper<NotificationUserDO> userWrapper = new QmQueryWrapper<NotificationUserDO>().lambda();
            userWrapper.in(NotificationUserDO::getNotifyId, ids);
            notificationUserService.remove(userWrapper);

            LambdaQueryWrapper<NotificationDO> wrapper = new QmQueryWrapper<NotificationDO>().lambda();
            wrapper.in(NotificationDO::getId, ids);
            boolean remove = notificationService.remove(wrapper);
            ret.setData(remove);
        }
        return ret;
    }

    @Operation(summary = "下达", description = "[author: 10200571]")
    @PostMapping("/send")
    public JsonResultVo<Boolean> send(@RequestBody List<String> ids){
        JsonResultVo<Boolean> ret = new JsonResultVo<>();
        if(CollUtil.isEmpty(ids)) {
            ret.setMsgErr("下达失败！ID列表不能为空！");
        } else {
            List<NotificationDO> notifications = new ArrayList<>();
            for (String id : ids) {
                NotificationDO notification = notificationService.getById(id);
                notification.setSendDate(new Date());
                notification.setStatus("02");
                notification.setSender(getUserInfo().getOperatorId());
                notifications.add(notification);
            }
            boolean saveOk = notificationService.saveOrUpdateBatch(notifications);
            ret.setData(saveOk);
        }
        return ret;
    }
}
