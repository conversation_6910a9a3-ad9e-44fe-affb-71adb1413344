package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 批量处理结果返回对象
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Schema(description = "批量处理结果返回对象")
@Getter
@Setter
public class RebateExtractApplyHandleResultResponse implements Serializable {

    /**
     * 申请单号
     */
    @Schema(description = "申请单号")
    private String applyNumber;

    /**
     * 处理结果（0-失败，1-成功）
     */
    @Schema(description = "处理结果（0-失败，1-成功）")
    private String handleResult;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}
