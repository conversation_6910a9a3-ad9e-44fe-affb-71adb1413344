package com.qm.ep.rebate.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:董事会政策结算细节回应")
@Data
public class BoardPolicySettleDetailResponse {


    /**
     * 执行开始时间
     */
    @Schema(description = "执行开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;

    /**
     * 执行结束时间
     */
    @Schema(description = "执行结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    /**
     * 政策结算周期
     */
    @Schema(description = "政策结算周期")
    private int settleCycle;

    /**
     * 政策名称
     */
    @Schema(description = "政策名称")
    private String policyName;

    /**
     * 创建人部门
     */
    @Schema(description = "创建人部门")
    private String creatorPart;

    /**
     * 政策创建人
     */
    @Schema(description = "政策创建人")
    private String creator;

    /**
     * 财务兑付应完成时间
     */
    @Schema(description = "财务兑付应完成时间")
    private String planFinishTime;

}
