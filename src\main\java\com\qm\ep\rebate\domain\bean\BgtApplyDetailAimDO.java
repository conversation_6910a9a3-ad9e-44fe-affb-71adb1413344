package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *  
 * 预算申请单明细-达成目标（金额）
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_apply_detail_aim")
@Schema(description = "数据:预算申请单明细-达成目标（金额）")
public class BgtApplyDetailAimDO extends Model<BgtApplyDetailAimDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-预算申请单id")
    @TableField("bgt_apply_main_id")
    private Integer bgtApplyMainId;

    @Schema(description = "数据-达成目标类型（00-aak，01-std，02-EBT%）")
    @TableField("aim_type")
    private String aimType;

    @Schema(description = "数据-目标数值")
    @TableField("aim_value")
    private String aimValue;

    @Schema(description = "数据-目标单位")
    @TableField("unit")
    private String unit;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
