package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.dto.sys.UserDTO;
import com.qm.ep.rebate.remote.response.OrgTreeNodeResponse;
import com.qm.ep.rebate.remote.response.OrgUserResponse;
import com.qm.ep.rebate.service.UserCenterService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.service.IMultiLanguageTextService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@Tag(name = "用户信息数据接口")
public class UserCenterController extends BaseController {

    @Resource
    private IMultiLanguageTextService multiTextService;

    @Resource
    private UserCenterService userCenterService;

    @Operation(summary = "保存用户信息", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<Boolean> save(@RequestBody UserDTO user){
        JsonResultVo<Boolean> jsonResultVo = new JsonResultVo<>();
        boolean ok = multiTextService.saveMultiText(user.getUserId(), "zh", user.getUserCode(), user.getUserName(),
                user.getUserName(), "6000", "SYSC030");
        jsonResultVo.setData(ok);
        return jsonResultVo;
    }


    @Operation(summary = "查询部门组织树", description = "[author: 10200571]")
    @PostMapping("/getSalesCenterOrgTree")
    public JsonResultVo<List<OrgTreeNodeResponse>> getSalesCenterOrgTree(@RequestBody UserDTO user){
        JsonResultVo<List<OrgTreeNodeResponse>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(userCenterService.getSalesCenterOrgTree(user.getDeptId()));
        return jsonResultVo;
    }

    @Operation(summary = "查询部门下用户信息", description = "[author: 10200571]")
    @PostMapping("/getUserInfosByDeptId")
    public JsonResultVo<List<OrgUserResponse>> getUserInfosByDeptId(@Valid @RequestBody UserDTO user){
        JsonResultVo<List<OrgUserResponse>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(userCenterService.getUserInfosByDeptId(user.getDeptId()));
        return jsonResultVo;
    }

}
