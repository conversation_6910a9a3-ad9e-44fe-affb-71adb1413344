package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


@Schema(description = "数据:数据BGT 任务响应")
@Data
public class BgtTaskResponse implements Serializable {


    @Schema(description = "数据-政策任务码")
    private String policyTaskFlowInstanceCode;

    @Schema(description = "数据-政策待办流")
    private String policyTaskInstanceCode;


}
