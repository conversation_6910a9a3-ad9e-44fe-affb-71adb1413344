package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* 指标值得分详情表
* Created  by Mr.hp
* DateTime on 2024-02-29 14:42:44
*/
@Schema(description = "数据:实体类-指标值得分详情表 Created  by Mr.hp DateTime on 2024-02-29 14:42:44")
@Data
@NoArgsConstructor
public class IndicatorValueScoreDTO implements Serializable {

    /**
    * 指标代码(必须)
    */
    @Schema(description = "数据-指标代码(必须)")
    private String indicatorCode;

    /**
    * 指标名称(必须)
    */
    @Schema(description = "数据-指标名称(必须)")
    private String indicatorName;


    /**
     * 指标类型，0-工作台使用情况指标、1-灯塔指标、2-经销商赋能指标、3-数据使用指标、4-扣分项指标
     */
    @Schema(description = "数据-指标类型，0-工作台使用情况指标、1-灯塔指标、2-经销商赋能指标、3-数据使用指标、4-扣分项指标")
    private Integer type;


    /**
    * 指标实际值
    */
    @Schema(description = "数据-指标实际值")
    private BigDecimal practicalValue;


}
