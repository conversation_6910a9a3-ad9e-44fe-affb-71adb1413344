package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "数据:董事会政策 解决回应")
@Data
public class BoardPolicySettleResponse {

    /**
     * 政策结算周期
     */
    @Schema(description = "政策结算周期")
    private int settleAvgCycle;
    /**
     * 新增数(较昨日)
     */
    @Schema(description = "新增数(较昨日)")
    private String changedCount;

}
