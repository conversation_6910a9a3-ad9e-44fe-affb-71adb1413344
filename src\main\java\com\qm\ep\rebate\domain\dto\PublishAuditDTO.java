package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.domain.bean.PolicyDO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "发布审核")
public class PublishAuditDTO {
    @Schema(description = "数据-状态")
    private String vstatus;
    @Schema(description = "数据-政策")
    private PolicyDO policyDO;

//    审核意见
    @Schema(description = "数据-审核意见")
    private String fullMessage;
//    审核步骤
    @Schema(description = "数据-审核步骤")
    private String typeDesc;
    // 是否为取消审核
    @Schema(description = "数据-是否为取消审核")
    private String prefix;
}
