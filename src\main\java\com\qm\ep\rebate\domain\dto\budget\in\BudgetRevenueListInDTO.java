package com.qm.ep.rebate.domain.dto.budget.in;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "数据:收入明细")
@Data
public class BudgetRevenueListInDTO {

    @Schema(description = "数据-年份")
    private String year;

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-兑付类型 00AAK、01STD")
    private String cashType;

}
