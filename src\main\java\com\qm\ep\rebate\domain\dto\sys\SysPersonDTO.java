package com.qm.ep.rebate.domain.dto.sys;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:SysPersonDTO")
@ToString(callSuper = true)
public class SysPersonDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-人名")
    private String personName;

    @Schema(description = "数据-人员代码")
    private String personCode;

    @Schema(description = "数据-组织 ID")
    private String orgId;

    @Schema(description = "数据-部门 ID")
    private String deptId;

    @Schema(description = "数据-排除用户")
    private List<String> excludeUser;
}