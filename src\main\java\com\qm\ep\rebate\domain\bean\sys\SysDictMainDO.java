package com.qm.ep.rebate.domain.bean.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYSC009C")
@Schema(description = "数据:系统模块-字典主表")
public class SysDictMainDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-字典代码")
    @TableField("VDICT")
    private String dict;

    @Schema(description = "数据-字典名称")
    @TableField("VDICTNAME")
    private String dictName;

    @Schema(description = "数据-客户组")
    @TableField("NCUSGROUPID")
    private String cusGroupId;

    @Schema(description = "数据-扩展标识")
    @TableField("CEXTAND")
    private Integer extand;

    @Schema(description = "数据-显示标识")
    @TableField("CVISIBLE")
    private Integer visible;

    @Schema(description = "数据-长度")
    @TableField("NLEN")
    private Integer len;

    @Schema(description = "数据-模块")
    @TableField("VMODULE")
    private String module;

    @Schema(description = "数据-注释")
    @TableField("VCOMMENT")
    private String comment;

    @Schema(description = "数据-主键")
    @TableField("PKEY")
    private String pKey;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新时间")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "数据-父项字典代码")
    @TableField(value = "VPARENTDICT")
    private String parentDict;

    @Schema(description = "数据-备注")
    @TableField("VREMARKS")
    private String remarks;

    @Schema(description = "数据-停用标识")
    @TableField("VSTOP")
    private Integer stop;

    @Schema(description = "数据-未知")
    @TableField("CEXTANDONE")
    private String extandOne;


}
