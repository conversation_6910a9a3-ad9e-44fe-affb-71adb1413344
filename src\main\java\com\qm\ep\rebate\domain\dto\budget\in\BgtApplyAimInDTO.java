package com.qm.ep.rebate.domain.dto.budget.in;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "达成目标")
public class BgtApplyAimInDTO {

    @Schema(description = "数据-达成目标类型（00-aak，01-std，02-EBT%）")
    private String aimType;

    @Schema(description = "数据-目标数值")
    private String aimValue;

    @Schema(description = "数据-目标单位")
    private String unit;
}
