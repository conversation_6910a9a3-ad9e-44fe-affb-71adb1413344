package com.qm.ep.rebate.domain.dto.agent.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "数据:报账单文件对象")
public class SaldFileListOutDTO {

    @Schema(description = "数据-文件类型")
    private String fileFirstType;

    @Schema(description = "数据-文件id")
    private String fileId;

    @Schema(description = "数据-文件名")
    private String fileName;

    @Schema(description = "数据-文件url")
    private String fileUrl;

    @Schema(description = "数据-文件类型")
    private String fileType;

    @Schema(description = "数据-创建者")
    private String createBy;

    @Schema(description = "数据-创建日期")
    private Date createOn;
}
