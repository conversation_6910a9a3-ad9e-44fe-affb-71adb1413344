package com.qm.ep.rebate.domain.bean.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYSC033")
@Schema(description = "数据:系统用户关联组织机构表")
public class SysPersonOrgDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据-人员ID")
    @TableField("NPERSONID")
    private String npersonId;

    @Schema(description = "数据-组织类型")
    @TableField("VORGTYPE")
    private String vorgType;

    @Schema(description = "数据-人员所属单位ID")
    @TableField("NORGID")
    private String norgId;

    @Schema(description = "数据-单位代码")
    @TableField("VORGCODE")
    private String vorgCode;

    @Schema(description = "数据-单位名称")
    @TableField("VORGNAME")
    private String vorgName;

    @Schema(description = "数据-人员所对应的营业网点")
    @TableField("NBRANCHID")
    private String nbranchId;

    @Schema(description = "数据-网点代码")
    @TableField("VBRANCH")
    private String vbranch;

    @Schema(description = "数据-网点名称")
    @TableField("VBRANCHNAME")
    private String vbranchName;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "数据-机构类型名称")
    @TableField(exist = false)
    private String vorgtypename;

    @Schema(description = "数据-搜索帮助名称")
    @TableField(exist = false)
    private String vtipname;
}
