package com.qm.ep.rebate.domain.dto.budget.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Schema(description = "数据:关联政策")
@Data
public class BudgetPolicyListOutDTO {

    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-政策id")
    private String policyId;

    @Schema(description = "数据-政策代码")
    private String vpolicycode;

    @Schema(description = "数据-政策名称")
    private String vpolicyname;

    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    private String bgtType;

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-申请预算金额 - 用于计算，不用显示")
    private BigDecimal totalAmount;

    @Schema(description = "数据-冻结金额（占用）- 用于计算，不用显示")
    private BigDecimal freezeAmount;


    @Schema(description = "数据-已使用的预算金额 - 实际结算金额")
    private BigDecimal usedAmount;

    @Schema(description = "数据-预算余额 - 预算占用金额")
    private BigDecimal amountBalance;

    @Schema(description = "数据-政策开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @Schema(description = "数据-政策结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @Schema(description = "数据-创建者")
    private String createBy;

    @Schema(description = "数据-创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createOn;

    @Schema(description = "数据-用户中文名")
    private String username;

    @Schema(description = "数据-部门")
    private String department;

    @Schema(description = "数据-任务实例")
    private String taskinsanceCode;

    @Schema(description = "数据-任务流实例")
    private String taskFlowinstanceCode;

    @Schema(description = "超出预算-使用金额")
    private BigDecimal uAmount;

    @Schema(description = "超出预算-占用金额")
    private BigDecimal fAmount;

}
