package com.qm.ep.rebate.domain.dto.oa;

import com.qm.ep.rebate.domain.dto.approve.StartProcessDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CompleteTaskRequest {
    /**
     * 应用码
     */
    private String appsource;
    /**
     * 租户ID
     */
    private String tenantLimitId;
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 流程实例ID
     */
    private String procInstId;

    /**
     * 动作类型
     */
    private String action;

    /**
     * 代理人
     */
    private String assignee;

    /**
     * 审批描述
     */
    private String approverDesc;


    /**
     * 分配的参与者
     */
    private Map<String, String> assignactors;

    /**
     * 条件列表
     */
    private List<Map<String, String>> conditions;


    /**
     * 启动子流程列表
     */
    private List<Map<String, String>> startSubProcess;

    /**
     * 子流程信息
     */
    private StartProcessDTO subProcessInfo;
}
