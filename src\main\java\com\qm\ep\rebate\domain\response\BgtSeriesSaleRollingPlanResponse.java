package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 *  
 * 车系销量预测表
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Data
@Schema(description = "数据:车系销量预测表")
public class BgtSeriesSaleRollingPlanResponse implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-滚动类型，1：零售-AAK，2：批发-STD")
    private Integer type;

    @Schema(description = "数据-车系")
    private String vsaltype;

    @Schema(description = "数据-1月")
    private long month1;

    @Schema(description = "数据-2月")
    private long month2;

    @Schema(description = "数据-3月")
    private long month3;

    @Schema(description = "数据-4月")
    private long month4;

    @Schema(description = "数据-5月")
    private long month5;

    @Schema(description = "数据-6月")
    private long month6;

    @Schema(description = "数据-7月")
    private long month7;

    @Schema(description = "数据-8月")
    private long month8;

    @Schema(description = "数据-9月")
    private long month9;

    @Schema(description = "数据-10月")
    private long month10;

    @Schema(description = "数据-11月")
    private long month11;

    @Schema(description = "数据-12月")
    private long month12;

    @Schema(description = "数据-滚动数据年月")
    private String yearMonths;


}