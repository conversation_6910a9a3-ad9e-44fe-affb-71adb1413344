package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * 计算公式主表
 *
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("formulamain")
@Schema(description = "计算公式主表")
public class FormulaMainDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策主键")
    @TableField("policyid")
    private String policyid;

    @Schema(description = "数据-公式名称")
    @TableField("formulaName")
    private String formulaName;

    @Schema(description = "数据-主数据源ID")
    @TableField("MAIN_TABLE")
    private String mainTable;

    @Schema(description = "数据-主数据源对象")
    @TableField(exist = false)
    private List<CalcObjectSourceDO> mainTableSource;

    @Schema(description = "数据-公式描述")
    @TableField("formuladic")
    private String formuladic;

    @Schema(description = "数据-完成状态")
    @TableField("vfinishstate")
    private String vfinishstate;

    @Schema(description = "数据-创建时间")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-创建者")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-更新")
    @TableField(value = "updateon", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;


    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createbyname;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updatebyname;

    @Schema(description = "数据-类型 1公式 2区间")
    @TableField("formulaType")
    private Integer formulaType;


    @Schema(description = "数据-公式取值 表名")
    @TableField(exist = false)
    private String tablename;

    @Schema(description = "数据-公式取值 表名")
    @TableField(exist = false)
    private String fieldname;

    @Schema(description = "数据-政策名称")
    @TableField(exist = false)
    private String policyName;


    @Schema(description = "是否政策结果")
    @TableField("is_policy_result")
    private String isPolicyResult;

    @Schema(description = "是否经销商可见")
    @TableField("is_visible")
    private String isVisible;

    @Schema(description = "是否可编辑")
    @TableField(exist = false)
    private String isEdit;
}
