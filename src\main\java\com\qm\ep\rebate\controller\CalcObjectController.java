package com.qm.ep.rebate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.qm.ep.rebate.domain.bean.BusinessConstructionTwoDO;
import com.qm.ep.rebate.domain.bean.LadderMainDO;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.dto.CalcObjectDTO;
import com.qm.ep.rebate.domain.dto.ExecCalcDTO;
import com.qm.ep.rebate.domain.vo.CalcObjectVO;
import com.qm.ep.rebate.domain.vo.SqlStructureVO;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.PolicyStatusEnum;
import com.qm.ep.rebate.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/calcObject")
@Tag(name = "计算对象", description = "[author: 10200571]")
public class CalcObjectController extends BaseController {

    @Resource
    CalcObjectService calcObjectService;

    @Resource
    CalcObjectService2 calcObjectService2;

    @Resource
    CalcObjectService3 calcObjectService3;

    @Resource
    PolicyService policyService;

    @Resource
    LadderMainService ladderMainService;

    @Resource
    BusinessConstructionTwoService businessConstructionTwoService;



    @Resource
    SystemConfigService systemConfigService;



    @Resource
    CalFactorMainService calFactorMainService;

    @Resource
    FormulaMainService formulaMainService;

    @Resource
    PremiseMainService premiseMainService;

    @Resource
    PlanMainService planMainService;

    @Operation(summary = "获取计算对象SQL", description = "[author: 10200571]")
    @PostMapping("/getSqlStructure")
    public JsonResultVo<SqlStructureVO> getSqlStructure(@RequestBody CalcObjectDTO calcObjectDTO) {
        JsonResultVo<SqlStructureVO> jsonResultVo = new JsonResultVo<>();
        SqlStructureVO res = calcObjectService.getSqlStructure(calcObjectDTO);
        jsonResultVo.setData(res);
        return jsonResultVo;
    }

    @Operation(summary = "获取计算对象SQL-试算器", description = "[author: 10200571]")
    @PostMapping("/getSqlStructure2")
    public JsonResultVo<SqlStructureVO> getSqlStructure2(@RequestBody CalcObjectDTO calcObjectDTO) {
        JsonResultVo<SqlStructureVO> jsonResultVo = new JsonResultVo<>();
        SqlStructureVO res = calcObjectService2.getSqlStructure(calcObjectDTO);
        jsonResultVo.setData(res);
        return jsonResultVo;
    }

    @Operation(summary = "获取计算对象SQL-试算器优化", description = "[author: 10200571]")
    @PostMapping("/getSqlStructure3")
    public JsonResultVo<SqlStructureVO> getSqlStructure3(@RequestBody CalcObjectDTO calcObjectDTO) {
        JsonResultVo<SqlStructureVO> jsonResultVo = new JsonResultVo<>();
        SqlStructureVO res = calcObjectService3.getSqlStructure(calcObjectDTO);
        jsonResultVo.setData(res);
        return jsonResultVo;
    }

    @Operation(summary = "获取计算对象字段", description = "[author: 10200571]")
    @PostMapping("/getFlatCalcObjects")
    public JsonResultVo<QmPage<BusinessConstructionTwoDO>> getFlatCalcObjects(@RequestBody CalcObjectDTO calcObjectDTO) {
        QueryWrapper<BusinessConstructionTwoDO> bcWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<BusinessConstructionTwoDO> bcLambdaWrapper = bcWrapper.lambda();
        bcLambdaWrapper.eq(BusinessConstructionTwoDO::getPolicyId, calcObjectDTO.getPolicyId())
                .eq(!BootAppUtil.isNullOrEmpty(calcObjectDTO.getObjectType()), BusinessConstructionTwoDO::getObjectType, calcObjectDTO.getObjectType())
                .like(BootAppUtil.isnotNullOrEmpty(calcObjectDTO.getObjectName()), BusinessConstructionTwoDO::getTableName, calcObjectDTO.getObjectName());
        QmPage<BusinessConstructionTwoDO> list = businessConstructionTwoService.table(bcWrapper, calcObjectDTO);
        JsonResultVo<QmPage<BusinessConstructionTwoDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "获取计算对象", description = "[author: 10200571]")
    @PostMapping("/getCalcObject")
    public JsonResultVo<CalcObjectVO> getCalcObject(@RequestBody CalcObjectDTO calcObjectDTO) {
        JsonResultVo<CalcObjectVO> ret = new JsonResultVo<>();
        ret.setData(calcObjectService.getCalcObject(calcObjectDTO));
        return ret;
    }

    @Operation(summary = "获取计算对象列表", description = "[author: 10200571]")
    @PostMapping("/getCalcObjects")
    public JsonResultVo<List<CalcObjectVO>> getCalcObjects(@RequestBody CalcObjectDTO calcObjectDTO) {
        JsonResultVo<List<CalcObjectVO>> ret = new JsonResultVo<>();
        ret.setData(calcObjectService.getCalcObjectList(calcObjectDTO));
        return ret;
    }

    @Operation(summary = "获取可正式计算的阶梯列表", description = "[author: 10200571]")
    @PostMapping("/getLadderExecCalcList")
    public JsonResultVo<List<ExecCalcDTO>> getLadderExecCalcList() {
        JsonResultVo<List<ExecCalcDTO>> ret = new JsonResultVo<>();

        QueryWrapper<PolicyDO> policyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyDO> policyLambdaWrapper = policyWrapper.lambda();
        policyLambdaWrapper.eq(PolicyDO::getVfinishstate, PolicyStatusEnum.PUBLISH.getCode());
        List<PolicyDO> policyList = policyService.list(policyWrapper);
        List<String> policyIds = policyList.stream().map(PolicyDO::getId).collect(Collectors.toList());

        QueryWrapper<LadderMainDO> ladderWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<LadderMainDO> ladderLambdaWrapper = ladderWrapper.lambda();
        ladderLambdaWrapper.in(LadderMainDO::getPolicyId, policyIds);
        List<LadderMainDO> ladderList = ladderMainService.list(ladderLambdaWrapper);
        List<ExecCalcDTO> execCalcList = ladderList.stream().map(l -> {
            ExecCalcDTO execCalcDTO = new ExecCalcDTO();
            execCalcDTO.setPolicyId(l.getPolicyId());
            execCalcDTO.setObjectId(l.getId());
            execCalcDTO.setObjectType(CalcObjectTypeEnum.LADDER.getCode());
            return execCalcDTO;
        }).collect(Collectors.toList());

        ret.setData(execCalcList);
        return ret;
    }

    @Operation(summary = "通过政策ID，获取计算对象个数信息", description = "[author: 10200571]")
    @PostMapping("/getCountByPolicyId")
    public JsonResultVo<Map<CalcObjectTypeEnum, Long>> getCountByPolicyId(@RequestBody CalcObjectDTO calcObjectDTO) {
        JsonResultVo<Map<CalcObjectTypeEnum, Long>> ret = new JsonResultVo<>();
        Map<CalcObjectTypeEnum, Long> countByPolicyId = calcObjectService.getCountByPolicyId(calcObjectDTO.getPolicyId());
        ret.setData(countByPolicyId);
        return ret;
    }

}
