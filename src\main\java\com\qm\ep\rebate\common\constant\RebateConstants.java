package com.qm.ep.rebate.common.constant;

/**
 *
 * 常量类
 *
 *
 * <AUTHOR>
 * @since 2020-07-15
 */
public final class RebateConstants {
    private RebateConstants() {
    }

    /**
     * 数据字典在Redis中的Key前缀
     */
    public static final String REDIS_KEY_PRE_DICT = "rebate:com.qm.ep.rebate.controller.DictChildController:";

    /**
     * 字段长度校验
     */
    public static final int ID_LENGTH_36 = 36;
    public static final int FIELD_LENGTH_255 = 255;
    public static final int FIELD_LENGTH_65535 = 65535;

    public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String MYSQL_DATE_FORMAT_PATTERN = "%Y-%m-%d %H:%i:%s";

    /**
     * 分隔符
     */
    public static final String COMMA_SEPARATOR = ",";
    public static final String SHORT_HORIZONTAL_LINE_SEPARATOR = "-";
    public static final String SPOT_SEPARATOR = ".";
    public static final String SPOT_SEPARATOR_REGEX = "\\.";
    /**
     * 字段类型常量
     */
    public static final String FETCH_FIELD = "取值字段";
    public static final String OUTPUT_FIELD = "输出字段";
    public static final String SUMMARY_FIELD = "汇总字段";
    public static final String DEFAULT_FIELD = "默认字段";
    public static final String AGGREGATED_FIELD = "聚合字段";

    /**
     * 筛选方式
     */
    public static final String FIELD = "field";
    public static final String VALUE = "value";
    public static final String NUMBER = "number";
    public static final String DATE = "date";
    public static final String PARAM = "param";

    /**
     * 公式节点类型
     */
    public static final String CALC_ELEMENT_TYPE_NUMBER = "number";
    public static final String CALC_ELEMENT_TYPE_OPERATOR = "operator";
    public static final String CALC_ELEMENT_TYPE_PUNCTUATION = "punctuation";
    public static final String CALC_ELEMENT_TYPE_FACTOR = "factor";
    public static final String CALC_ELEMENT_TYPE_PLAN = "plan";
    public static final String CALC_ELEMENT_TYPE_DATE = "date";

    /**
     * 公式类型
     */
    public static final Integer FORMULA_TYPE_FORMULA = 1;
    public static final Integer FORMULA_TYPE_SECTION = 2;

    /** 查询条件-时间类型-创建时间 */
    public static final String CREATE_TIME = "01";
    /** 查询条件-时间类型-更新时间 */
    public static final String UPDATE_TIME = "02";

    /** 计算状态-待计算 */
    public static final Integer WAIT_CALCULATE = 0;
    /** 计算状态-计算完毕 */
    public static final Integer FINISH_CALCULATE = 1;
    /** 计算状态-计算中 */
    public static final Integer CALCULATING = 2;

    /** 数据源类型-业务底表 */
    public static final String BASIC = "basic";
    /** 数据源类型-计算因子 */
    public static final String FACTOR = "factor";
    /** 数据源类型-计算公式 */
    public static final String FORMULA = "formula";
    /** 数据源类型-区间公式 */
    public static final String SECTION = "section";
    /** 数据源类型-前提条件 */
    public static final String PREMISE = "premise";
    /** 数据源类型-计算方案 */
    public static final String PLAN = "plan";

    public static final String POLICY_SET_PUBLISH_FLOW_KEY = "POLICY_SET_PUBLISH";


    public static final String POLICY_PUBLISH_AUDIT_MODEL_CODE = "policy_publish_audit_model";
    public static final String POLICY_PUBLISH_AUDIT_MODEL_WF = "wf";
    public static final String COMPANY = "company";
    public static final String BUSINESS_TABLE_AUTH = "business_table_auth";

    /** 2003版后缀 */
    public static final String SUFFIX_2003 = ".xls";
    /** 2007版后缀 */
    public static final String SUFFIX_2007 = ".xlsx";
}
