package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("TARGET_TREE_DIMENSION")
@Schema(description = "指标维度表")
public class TargetTreeDimensionDO implements Serializable {
    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-主表主键")
    @TableField("mainId")
    private String mainId;

    @Schema(description = "数据-维度分类")
    @TableField("dimensionClass")
    private String dimensionClass;

    @Schema(description = "数据-维度")
    @TableField("dimension")
    private String dimension;

    @Schema(description = "数据-指标值")
    @TableField("targetValue")
    private String targetValue;

    @Schema(description = "数据-单位")
    @TableField("unit")
    private String unit;

}
