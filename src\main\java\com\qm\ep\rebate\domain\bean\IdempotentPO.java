package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * 幂等表
 *
 *
 * <AUTHOR>
 * @since 2024-01-30
 */
@Schema(description = "数据:实体类-  幂等表  ")
@Getter
@Setter
@TableName("idempotent")
public class IdempotentPO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务主键
     */
    @Schema(description = "数据-业务主键")
    private String bizId;

    /**
     * 业务主键含义
     */
    @Schema(description = "数据-业务主键含义")
    private String bizDesc;

    /**
     * 业务类型
     */
    @Schema(description = "数据-业务类型")
    private String bizCode;


}
