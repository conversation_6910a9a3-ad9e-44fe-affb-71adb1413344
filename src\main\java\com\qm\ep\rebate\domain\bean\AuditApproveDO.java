package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;

/**
 *
 * 
 *
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audit_approve")
@Schema(description = "数据实体")
public class AuditApproveDO implements Serializable {


    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据-业务主键")
    @TableField("biz_id")
    private String bizId;

    @Schema(description = "数据-任务号")
    @TableField("task_flow_instance_code")
    private String taskFlowInstanceCode;

    @Schema(description = "数据-审批的实体")
    @TableField("class_name")
    private String className;

    @Schema(description = "数据-审批流主键")
    @TableField("process_instance_id")
    private String processInstanceId;

    @Schema(description = "数据-完成状态（0：初始化，1：进行中，2已完成）")
    @TableField("v_finish_status")
    private Integer vFinishStatus;

    @Schema(description = "数据-完成时间")
    @TableField("v_finish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date vFinishTime;

    @Schema(description = "数据-提交人登录账户")
    @TableField("submit_code")
    private String submitCode;

    @Schema(description = "数据-提交人主键")
    @TableField("submit_id")
    private String submitId;

    @Schema(description = "数据-创建者")
    @TableField("createBy")
    private String createBy;

    @Schema(description = "数据-创建时间")
    @TableField("createOn")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField("updateBy")
    private String updateBy;

    @Schema(description = "数据-更新")
    @TableField("updateOn")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-数据DTSTAMP")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    private Timestamp dtstamp;

    @Schema(description = "数据-受让人地图")
    @TableField(exist = false )
    private Map<String, String> assigneeMap;


    @Schema(description = "数据-开始提交代码")
    @TableField(exist = false )
    private String  startSubmitCode;


}
