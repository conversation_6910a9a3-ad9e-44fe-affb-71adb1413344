package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *  
 * 产品视图
 *
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_product")
@Schema(description = "数据:产品视图")
public class VproductDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-技术项组合ID")
    @TableField("NCONFIGITEMID")
    private String nconfigitemid;

    @Schema(description = "数据-技术项组合实例ID（产品ID）")
    @TableId(value = "NPRODUCTID", type = IdType.ASSIGN_ID)
    private String nproductid;

    @Schema(description = "数据-技术项组合实例代码（产品代码）")
    @TableField("VPRODUCTCODE")
    private String vproductcode;

    @Schema(description = "数据-产品代码简称")
    @TableField("VPRODUCTTEXT")
    private String vproducttext;

    @Schema(description = "数据-产品代码名称")
    @TableField("VPRODUCTLONGTEXT")
    private String vproductlongtext;

    @Schema(description = "数据-停用标识")
    @TableField("VSTOP")
    private String vstop;

    @Schema(description = "数据-停用日期")
    @TableField("DSTOP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dstop;

    @Schema(description = "数据-产品内码")
    @TableField("VINNERPDTCODE")
    private String vinnerpdtcode;

    @Schema(description = "数据-内部产品码描述")
    @TableField("VINNERPDTCODETEXT")
    private String vinnerpdtcodetext;

    @Schema(description = "数据-时间戳: ")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

    @Schema(description = "数据-其它")
    @TableField("VOTHERS")
    private String vothers;

    @Schema(description = "数据-平台ID   ")
    @TableField("NFLAT")
    private String nflat;

    @Schema(description = "数据-平台代码")
    @TableField("VFLAT")
    private String vflat;

    @Schema(description = "数据-平台名称")
    @TableField("VFLATTEXT")
    private String vflattext;

    @Schema(description = "数据-品牌")
    @TableField("NBRAND")
    private String nbrand;

    @Schema(description = "数据-品牌代码 ")
    @TableField("VBRAND")
    private String vbrand;

    @Schema(description = "数据-品牌名称")
    @TableField("VBRANDTEXT")
    private String vbrandtext;

    @Schema(description = "数据-产品系列ID    ")
    @TableField("NPDTSERIES")
    private String npdtseries;

    @Schema(description = "数据-产品系列代码")
    @TableField("VPDTSERIES")
    private String vpdtseries;

    @Schema(description = "数据-产品系列名称")
    @TableField("VPDTSERIESTEXT")
    private String vpdtseriestext;

    @Schema(description = "数据-车型大类/车辆型号代码")
    @TableField("VVEHICLETYPE")
    private String vvehicletype;

    @Schema(description = "数据-车辆型号ID")
    @TableField("NVEHICLETYPE")
    private String nvehicletype;

    @Schema(description = "数据-车辆型号名称")
    @TableField("VVEHICLETYPETEXT")
    private String vvehicletypetext;

    @Schema(description = "数据-销售车型ID  ")
    @TableField("NSALTYPE")
    private String nsaltype;

    @Schema(description = "数据-销售车型")
    @TableField("VSALTYPE")
    private String vsaltype;

    @Schema(description = "数据-销售车型名称")
    @TableField("VSALTYPETEXT")
    private String vsaltypetext;

    @Schema(description = "数据-车身颜色ID    ")
    @TableField("NCOLOR")
    private String ncolor;

    @Schema(description = "数据-车身颜色代码")
    @TableField("VCOLOR")
    private String vcolor;

    @Schema(description = "数据-车身颜色名称")
    @TableField("VCOLORTEXT")
    private String vcolortext;

    @Schema(description = "数据-车顶颜色ID    ")
    @TableField("NCOLOR1")
    private String ncolor1;

    @Schema(description = "数据-车顶颜色代码")
    @TableField("VCOLOR1")
    private String vcolor1;

    @Schema(description = "数据-车顶颜色名称")
    @TableField("VCOLORTEXT1")
    private String vcolortext1;

    @Schema(description = "数据-内饰+颜色ID")
    @TableField("NCABTYPE")
    private String ncabtype;

    @Schema(description = "数据-内饰+颜色代码")
    @TableField("VCABTYPE")
    private String vcabtype;

    @Schema(description = "数据-内饰+颜色名称")
    @TableField("VCABTYPETEXT")
    private String vcabtypetext;

    @Schema(description = "数据-服务车系ID")
    @TableField("NSERVICEVEHICLEPDT")
    private String nservicevehiclepdt;

    @Schema(description = "数据-服务车系代码")
    @TableField("VSERVICEVEHICLEPDT")
    private String vservicevehiclepdt;

    @Schema(description = "数据-服务车系名称")
    @TableField("VSERVICEVEHICLEPDTTEXT")
    private String vservicevehiclepdttext;

    @Schema(description = "数据-畅销类别号ID")
    @TableField("NREADYSALETYPE")
    private String nreadysaletype;

    @Schema(description = "数据-畅销类别代码")
    @TableField("VREADYSALETYPE")
    private String vreadysaletype;

    @Schema(description = "数据-畅销类别描述")
    @TableField("VREADYSALETYPETEXT")
    private String vreadysaletypetext;


    @Schema(description = "数据-RP组号")
    @TableField("NOPTIONS")
    private String noptions;

    @Schema(description = "数据-RP组号代码")
    @TableField("VOPTIONS")
    private String voptions;

    @Schema(description = "数据-RP组号描述")
    @TableField("VOPTIONSTEXT")
    private String voptionstext;

    @Schema(description = "数据-排量")
    @TableField("NEMISSION")
    private String nemission;

    @Schema(description = "数据-排量代码")
    @TableField("VEMISSION")
    private String vemission;

    @Schema(description = "数据-排量描述")
    @TableField("VEMISSIONTEXT")
    private String vemissiontext;

    @Schema(description = "数据-智能网联ID")
    @TableField("NICV")
    private String nicv;

    @Schema(description = "数据-智能网联代码")
    @TableField("VICV")
    private String vicv;

    @Schema(description = "数据-智能网联名称")
    @TableField("VICVTEXT")
    private String vicvtext;


}
