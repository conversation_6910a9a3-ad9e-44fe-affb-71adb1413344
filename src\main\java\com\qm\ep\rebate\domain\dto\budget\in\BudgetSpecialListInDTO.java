package com.qm.ep.rebate.domain.dto.budget.in;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 预算查询 - 金额
 */
@Schema(description = "预算查询 - 金额")
@Data
public class BudgetSpecialListInDTO extends JsonParamDto {

    @Schema(description = "数据-年份")
    private String year;

    @Schema(description = "数据-时间类型")
    private String timeType;

    @Schema(description = "数据-预算类型")
    private String bgtType;

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-返利项目")
    private String classItem;
}
