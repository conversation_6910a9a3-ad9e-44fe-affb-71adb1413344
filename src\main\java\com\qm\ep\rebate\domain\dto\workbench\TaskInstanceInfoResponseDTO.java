package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:实体类-任务实例信息响应 DTO")
@Data
public class TaskInstanceInfoResponseDTO {

    @Schema(description = "数据-业务单元编码")
    private String bizUnitCode;

    @Schema(description = "数据-任务流编码")
    private String taskFlowCode;

    @Schema(description = "数据-任务流版本编码")
    private String taskFlowVersionCode;

    @Schema(description = "数据-任务流实例编码")
    private String taskFlowInstanceCode;

    @Schema(description = "数据-能力中心侧业务ID")
    private String bizId;

    @Schema(description = "数据-任务实例编码")
    private String taskInstanceCode;

    @Schema(description = "数据-被分发人用户code")
    private String distributeUserCode;

    @Schema(description = "数据-被分发人用户名称")
    private String distributeUserName;

    /**
     * 任务实例状态（0：待办，1：完成）
     */
    @Schema(description = "数据-任务实例状态")
    private Integer status;

    @Schema(description = "数据-事件日志记录ID")
    private Integer eventLogId;

    @Schema(description = "数据-备注")
    private String remark;

}
