package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "ReportJoinOnDTO对象")
public class ReportJoinOnDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-报表ID")
    private String joinId;

    @Schema(description = "数据-开始括号")
    private String bracketBefore;

    @Schema(description = "数据-条件中左边的字段名")
    private String leftFieldName;

    @Schema(description = "数据-条件中左边的字段所在的表名")
    private String leftFieldFrom;

    @Schema(description = "数据-条件中左边的表所在的政策名")
    private String leftTableSource;

    @Schema(description = "数据-计算逻辑")
    private String calculationLogic;

    @Schema(description = "数据-条件类型")
    private String conditionType;

    @Schema(description = "数据-条件中右边的值")
    private String rightValue;

    @Schema(description = "数据-条件中右边的字段所在的表名")
    private String rightFieldFrom;

    @Schema(description = "数据-条件中右边的表所在的政策名")
    private String rightTableSource;

    @Schema(description = "数据-结束括号")
    private String bracketAfter;

    @Schema(description = "数据-关联逻辑")
    private String associationLogic;

    @Schema(description = "数据-排序")
    private Integer rank;
}