package com.qm.ep.rebate.domain.dto.mqworklist;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-工作清单 添加 DTO")
@Data
public class WorklistAddDTO {

    @Schema(description = "数据-应用 URL")
    private String appUrl;


    @Schema(description = "数据-审批者")
    private String approver;


    @Schema(description = "数据-审批者电子邮件")
    private String approverEmail;


    @Schema(description = "数据-审批者名称")
    private String approverName;


    @Schema(description = "数据-审批人电话")
    private String approverTel;


    @Schema(description = "数据-延迟时间")
    private String delayTime;


    @Schema(description = "数据-完成应用 URL")
    private String doneAppUrl;


    @Schema(description = "数据-完成 PC 网址")
    private String donePcUrl;


    @Schema(description = "数据-是快速批准")
    private Integer isFastApprove;


    @Schema(description = "数据-快速批准 API URL")
    private String fastApproveApiUrl;


    @Schema(description = "数据-日志 ID")
    private String logId;


    @Schema(description = "数据-节点 ID")
    private String nodeId;


    @Schema(description = "数据-节点名称")
    private String nodeName;


    @Schema(description = "数据-操作 ID")
    private String operationId;


    @Schema(description = "数据-操作时间")
    private String operationTime;


    @Schema(description = "数据-电脑网址")
    private String pcUrl;


    @Schema(description = "数据-程序代码")
    private String procCode;


    @Schema(description = "数据-Proc inst ID")
    private String procInstId;


    @Schema(description = "数据-拒绝待办事项")
    private Integer rejectTodo;


    @Schema(description = "数据-源枚举")
    private Integer sourceEnum;


    @Schema(description = "数据-源类型")
    private String sourceType;

    @Schema(description = "数据-开始时间")
    private String startTime;


    @Schema(description = "数据-系统代码")
    private String sysCode;

    @Schema(description = "数据-任务 ID")
    private String taskId;


    @Schema(description = "数据-坦普尔特代码")
    private String templetCode;

    @Schema(description = "数据-待办事项详细信息")
    private JSONObject todoDetail;
    @Schema(description = "数据-模板参数")
    private List<JSONObject> templateParam;



}