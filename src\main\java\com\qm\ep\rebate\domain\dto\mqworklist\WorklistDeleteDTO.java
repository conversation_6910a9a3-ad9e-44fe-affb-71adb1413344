package com.qm.ep.rebate.domain.dto.mqworklist;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-工作清单删除 DTO")
@Data
public class WorklistDeleteDTO {

    @Schema(description = "数据-系统代码")
    private String sysCode;


    @Schema(description = "数据-程序代码")
    private String procCode;


    @Schema(description = "数据-Proc inst ID")
    private String procInstId;


    @Schema(description = "数据-任务 ID")
    private String taskId;


    @Schema(description = "数据-操作 ID")
    private String operationId ;


    @Schema(description = "数据-操作时间")
    private String operationTime ;







}