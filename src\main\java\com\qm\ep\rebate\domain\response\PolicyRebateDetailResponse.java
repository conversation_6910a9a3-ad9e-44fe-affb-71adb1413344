package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "返利查询的返回对象，包括按 VIN 和按系列的查询方式")
public class PolicyRebateDetailResponse {

    @Schema(description = "政策编码")
    private String policyCode;

    @Schema(description = "政策名称")
    private String policyName;

    @Schema(description = "系列")
    private String series;

    @Schema(description = "车底盘号，仅在按 VIN 查询时有效")
    private String vin;

    @Schema(description = "返利金额（元），按 VIN 查询时为单个返利金额，按系列查询时为汇总金额")
    private String rebateAmount;

    @Schema(description = "返利金额合计（元）")
    private String totalRebateAmount;

}





