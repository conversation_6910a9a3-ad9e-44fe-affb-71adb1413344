package com.qm.ep.rebate.domain.dto.oa;

import com.qm.ep.rebate.common.config.ProcessConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WFHistoryRequest {
    private String appsource;

    private String tenantLimitId;

    private String procInstId;

    private boolean returnNoPreviewNodes;

    public static WFHistoryRequest buildGetWtFHistoryRequest(ProcessConfig processConfig, String procInstId) {
        WFHistoryRequest bpmRequest = new WFHistoryRequest();
        bpmRequest.setAppsource(processConfig.getAppsource());
        bpmRequest.setTenantLimitId(processConfig.getTenantLimitId());
        bpmRequest.setReturnNoPreviewNodes(true);
        bpmRequest.setProcInstId(procInstId);
        return bpmRequest;
    }
}
