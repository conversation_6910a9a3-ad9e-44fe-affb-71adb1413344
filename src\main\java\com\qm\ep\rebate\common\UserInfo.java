package com.qm.ep.rebate.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "iwork登录用户信息")
@NoArgsConstructor
@Data
public class UserInfo {

    @Schema(description = "iwork用户名称")
    private String loginAccount;

    @Schema(description = "创建时间戳")
    private Long created;

    @Schema(description = "IDM主键（自有人员）")
    @JsonProperty("idmid")
    private String idmId;

    @Schema(description = "UPK 标识")
    @JsonProperty("upkid")
    private String upkId;

    public UserInfo(String loginAccount, String idmId) {
        this.loginAccount = loginAccount;
        this.idmId = idmId;
    }

}
