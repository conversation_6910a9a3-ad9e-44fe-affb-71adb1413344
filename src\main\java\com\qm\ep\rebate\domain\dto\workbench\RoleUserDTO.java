package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 云原生用户及子角色用户信息
 */
@Schema(description = "数据:实体类-云原生用户及子角色用户信息")
@Data
public class RoleUserDTO {

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-法典")
    private String code;

    @Schema(description = "数据-名字")
    private String name;

    @Schema(description = "数据-多语言描述")
    private String multilingualDesc;

    @Schema(description = "数据-角色组 ID")
    private String roleGroupId;

    @Schema(description = "数据-角色组名称")
    private String roleGroupName;

    @Schema(description = "数据-系统 ID")
    private String systemId;

    @Schema(description = "数据-父角色 ID")
    private String parentRoleId;

    @Schema(description = "数据-父角色 ID")
    private String parentRoleIds;

    @Schema(description = "数据-用户")
    private List<CloudNativeUserDTO> users;

    @Schema(description = "数据-孩子")
    private List<RoleUserDTO> children;

}
