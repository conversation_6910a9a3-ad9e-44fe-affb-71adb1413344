package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.GlobalBudgetOtherProfitDO;
import com.qm.ep.rebate.domain.dto.GlobalBudgetDTO;
import com.qm.ep.rebate.domain.dto.GlobalBudgetResultDTO;
import com.qm.ep.rebate.service.GlobalBudgetOtherProfitService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/globalBudgetOtherProfit")
@Tag(name = "总体预算编制其他收益")
public class GlobalBudgetOtherProfitController extends BaseController {

    @Resource
    private GlobalBudgetOtherProfitService globalBudgetOtherProfitService;

    @Operation(summary = "查询总体预算编制其他收益列表", description = "[author: 10200571]")
    @PostMapping("/list")
    public JsonResultVo<List<GlobalBudgetOtherProfitDO>> list(@RequestBody GlobalBudgetResultDTO globalBudgetResultDTO){
        JsonResultVo<List<GlobalBudgetOtherProfitDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<GlobalBudgetOtherProfitDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<GlobalBudgetOtherProfitDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(GlobalBudgetOtherProfitDO::getBudgetId, globalBudgetResultDTO.getBudgetId());
        List<GlobalBudgetOtherProfitDO> data = globalBudgetOtherProfitService.list(queryWrapper);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "查询总体预算编制其他收益每月合计", description = "[author: 10200571]")
    @PostMapping("/sum")
    public JsonResultVo<GlobalBudgetOtherProfitDO> sum(@RequestBody GlobalBudgetDTO globalBudgetDTO){
        JsonResultVo<GlobalBudgetOtherProfitDO> jsonResultVo = new JsonResultVo<>();
        GlobalBudgetOtherProfitDO data = globalBudgetOtherProfitService.otherProfitSum(globalBudgetDTO);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo save(@RequestBody List<GlobalBudgetOtherProfitDO> globalBudgetOtherProfits){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        boolean isOk = globalBudgetOtherProfitService.saveOrUpdateBatch(globalBudgetOtherProfits);
        ret.setData(isOk);
        return ret;
    }

    @Operation(summary = "删除", description = "[author: 10200571]")
    @PostMapping("/deleteByIds")
    public JsonResultVo deleteByIds(@RequestBody List<String> ids){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        if(CollUtil.isNotEmpty(ids)) {
            boolean isOk = globalBudgetOtherProfitService.removeByIds(ids);
            ret.setData(isOk);
        }
        return ret;
    }

}
