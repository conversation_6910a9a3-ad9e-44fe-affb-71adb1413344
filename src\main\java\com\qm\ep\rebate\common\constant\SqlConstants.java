package com.qm.ep.rebate.common.constant;

/**
 * <AUTHOR>
 */
public final class SqlConstants {
    public static final CharSequence BUSINESS_DATA_QUERY_UNREAL_BASE_TEMPLATE_AAK_PARTITION_AAK = "SELECT * FROM (SELECT {fieldNames}, ROW_NUMBER() OVER (PARTITION BY field81, field1, field10) AS row_num FROM businessdata_unreal WHERE tablename = '{tableName}' AND field4 = '零售' AND field81 = '{policyId}') AS ranked WHERE row_num <= {limitCount} UNION SELECT {fieldNames}, {limitCount} AS row_num FROM businessdata WHERE tablename = '经销商提车统计' AND field4 = '批发'";
    public static final CharSequence BUSINESS_DATA_QUERY_UNREAL_BASE_TEMPLATE_AAK_PARTITION_STD = "SELECT * FROM (SELECT {fieldNames}, ROW_NUMBER() OVER (PARTITION BY field81, field1, field10) AS row_num FROM businessdata_unreal WHERE tablename = '{tableName}' AND field4 = '批发' AND field81 = '{policyId}') AS ranked WHERE row_num <= {limitCount} UNION SELECT {fieldNames}, {limitCount} AS row_num FROM businessdata WHERE tablename = '经销商提车统计' AND field4 = '零售'";
    public static final CharSequence BUSINESS_DATA_QUERY_UNREAL_BASE_TEMPLATE_INVOICE_PARTITION = "SELECT * FROM (SELECT {fieldNames}, ROW_NUMBER() OVER (PARTITION BY field81, field27, field22) AS row_num FROM businessdata_unreal WHERE tablename = '{tableName}'  AND field81 = '{policyId}') AS ranked WHERE row_num <= {limitCount} ";

    private SqlConstants() {
    }

    public static final String SELECT = " select ";
    public static final String WHERE = " where ";
    public static final String FROM = " from ";
    public static final String JOIN = " join ";
    public static final String AS = " as ";
    public static final String AND = " and ";
    public static final String OR = " or ";
    public static final String ON = " on ";
    public static final String DISTINCT = " distinct ";
    public static final String GROUP_BY = " group by ";
    public static final String HAVING = " having ";
    public static final String COUNT = " count ";
    public static final String ORDER_BY = " order by ";
    public static final String UNION_ALL = " union all ";
    public static final String LEFT_JOIN = " left join ";
    public static final String INDEX = " index ";
    public static final String FUNC_IF_NULL = " ifnull ";

    public static final String LIKE_OPERATOR = "like";
    public static final String NOT_LIKE_OPERATOR = "not like";
    public static final String IN_OPERATOR = "in";
    public static final String NOT_IN_OPERATOR = "not in";

    public static final String NOT_EQUAL_OPERATOR = "<>";
    public static final String EQUAL_OPERATOR = "=";
    public static final String LESS_THAN_OPERATOR = "<";
    public static final String GREATER_THAN_OPERATOR = ">";

    /**
     * sql 模板
     */
    public static final String BUSINESS_DATA_QUERY_TEMPLATE = SELECT + "{fieldNames}" + FROM + "businessdata" + WHERE + " tablename='{tableName}' ";

    public static final String SQL_MODE_TEMPLATE = " SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION'; ";
    public static final String IFNULL_TEMPLATE = " SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION'; ";

    public static final String BUSINESS_DATA_QUERY_UNREAL_TEMPLATE = " ("+SELECT + "{fieldNames}" + FROM + "businessdata_unreal" + WHERE + " tablename='{tableName}'"+AND+"field4='零售'"+AND+"field81='{policyId}'"+"{dealerField}"+"{seriesField}"+" limit {limitCount}"+" ) ";
    public static final String BUSINESS_DATA_QUERY_UNREAL_BASE_TEMPLATE = SELECT + "{fieldNames}" + FROM + "businessdata" + WHERE + " tablename='{tableName}' "+"{dealerField}";
    public static final String BUSINESS_DATA_QUERY_UNREAL_BASE_TEMPLATE_AAK = " ( "+SELECT + "{fieldNames}" + FROM + "businessdata_unreal" + WHERE + " tablename='{tableName}' "+"{dealerField}"+"{seriesField}"+AND+"field81='{policyId}'"+" limit {limitCount}"+" ) ";

    /**
     * 这个sql进行了分区处理，目的在于从虚拟车表中选出该经销商全部车系的N台零售车，并且也取出对应的批发车。字段不能改，否则分区会出现问题
     */
    public static final String BUSINESS_DATA_QUERY_UNREAL_BASE_TEMPLATE_AAK_PARTITION = "SELECT * FROM ( SELECT "+"{fieldNames}"+", ROW_NUMBER() OVER (PARTITION BY field81, field1, field10 ) AS row_num FROM businessdata_unreal WHERE tablename = '{tableName}' AND field4 = '零售' AND field81= '{policyId}') AS ranked WHERE row_num <= {limitCount}" +
            " UNION " + "SELECT "+"{fieldNames}, {limitCount} AS row_num "+" FROM businessdata WHERE tablename = '经销商提车统计' AND field4= '批发'";

}
