package com.qm.ep.rebate.domain.dto.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2023/7/1 16:58
 * @version: $
 */
@Schema(description = "数据:实体类-：")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StartProcessDTO {

    @Schema(description = "数据-租户限制 ID")
    private String tenantLimitId;
    @Schema(description = "数据-造物主")
    private String creator;
    @Schema(description = "数据-应用源")
    private String appsource;
    @Schema(description = "数据-模板名称")
    private String templateName;
    @Schema(description = "数据-类别")
    private String category;
    @Schema(description = "数据-业务密钥")
    private String businessKey;
    @Schema(description = "数据-系统代码")
    private String sysCode;
    @Schema(description = "数据-程序代码")
    private String procCode;
    @Schema(description = "数据-Proc Inst 名称")
    private String procInstName;
    @Schema(description = "数据-条件")
    private Object conditions;
    @Schema(description = "数据-分配者")
    private Object assignactors;
    @Schema(description = "数据-商业模型")
    private BizModel bizModel;

    @Schema(description = "数据:实体类-商业模型")
    public static class BizModel{
        @Schema(description = "数据-应用项目")
        private Object applyItem;
        @Schema(description = "数据-应用标头")
        private Map<String, String> applyHeader;

        public BizModel() {

        }

        public Object getApplyItem() {
            return applyItem;
        }

        public void setApplyItem(Object applyItem) {
            this.applyItem = applyItem;
        }

        public Object getApplyHeader() {
            return applyHeader;
        }

        public void setApplyHeader(Map<String, String> applyHeader) {
            this.applyHeader = applyHeader;
        }
    }
}
