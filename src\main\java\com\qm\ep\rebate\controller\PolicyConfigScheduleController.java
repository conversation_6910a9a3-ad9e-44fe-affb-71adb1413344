package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.service.PolicyConfigScheduleService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 政策配置工作计划
 */
@Tag(name = "政策配置计划任务")
@RestController
@RequestMapping("/policyConfigSchedule")
@Slf4j
public class PolicyConfigScheduleController {

    @Resource
    private PolicyConfigScheduleService policyConfigScheduleService;

    /**
     * 配置发布固定提醒
     */
    @Operation(summary = "配置发布固定提醒", description = "[author: 10200571]")
    @PostMapping("/fixRemind")
    public JsonResultVo<String> fixRemind() {
        JsonResultVo<String> res = new JsonResultVo<>();
        policyConfigScheduleService.fixRemind();
        return res;
    }

    /**
     * 配置发布逾期提醒
     */
    @Operation(summary = "配置发布逾期提醒", description = "[author: 10200571]")
    @PostMapping("/overdueRemind")
    public JsonResultVo<String> overdueRemind() {
        JsonResultVo<String> res = new JsonResultVo<>();
        policyConfigScheduleService.overdueRemind();
        return res;
    }

    /**
     * 政策兑付固定提醒
     */
    @Operation(summary = "政策兑付固定提醒", description = "[author: 10200571]")
    @PostMapping("/applyFixRemind")
    public JsonResultVo<String> applyFixRemind() {
        JsonResultVo<String> res = new JsonResultVo<>();
        policyConfigScheduleService.applyFixRemind();
        return res;
    }

    /**
     * 政策兑付逾期提醒
     */
    @Operation(summary = "政策兑付逾期提醒", description = "[author: 10200571]")
    @PostMapping("/applyOverdueRemind")
    public JsonResultVo<String> applyOverdueRemind() {
        JsonResultVo<String> res = new JsonResultVo<>();
        policyConfigScheduleService.applyOverdueRemind("schedule");
        return res;
    }

}
