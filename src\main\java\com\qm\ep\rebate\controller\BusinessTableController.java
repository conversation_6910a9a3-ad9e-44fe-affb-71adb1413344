package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.BusinessTableDO;
import com.qm.ep.rebate.service.BusinessTableService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/businessData")
@Tag(name = "业务数据", description = "[author: 10200571]")
public class BusinessTableController {

    @Resource
    BusinessTableService businessTableService;

    @Operation(summary = "数据因子确认查询", description = "[author: 10200571]")
    @GetMapping("/getBusinessTable")
    public JsonResultVo<BusinessTableDO> getBusinessTable(String taskInstanceCode) {
        JsonResultVo<BusinessTableDO> resultVo = new JsonResultVo<>();
        BusinessTableDO businessTableDO = businessTableService.getBusinessTable(taskInstanceCode);
        resultVo.setData(businessTableDO);
        return resultVo;
    }

    @Operation(summary = "数据因子预警确认", description = "[author: 10200571]")
    @GetMapping("/confirm")
    public JsonResultVo<String> confirm(String taskInstanceCode) {
        JsonResultVo<String> resultVo = new JsonResultVo<>();
        businessTableService.confirm(taskInstanceCode);
        return resultVo;
    }

    @Operation(summary = "数据因子停用开关", description = "[author: 10200571]")
    @GetMapping("/stopFlagBusinessTable")
    public JsonResultVo<String> stopFlagBusinessTable(String id, Integer flag) {
        JsonResultVo<String> resultVo = new JsonResultVo<>();
        businessTableService.stopFlagBusinessTable(id, flag);
        return resultVo;
    }

}
