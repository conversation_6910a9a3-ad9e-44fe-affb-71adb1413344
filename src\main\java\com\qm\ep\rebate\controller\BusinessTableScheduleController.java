package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.service.BusinessTaskService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "数据因子定时任务")
@RestController
@RequestMapping("/businessTableSchedule")
@Slf4j
public class BusinessTableScheduleController {

    @Resource
    private BusinessTaskService businessTaskService;

    @Operation(summary = "发布预警提醒", description = "[author: 10200571]")
    @PostMapping("/sendWarning")
    public JsonResultVo<String> sendWarning() {
        JsonResultVo<String> result = new JsonResultVo<>();
        businessTaskService.sendWarning();
        return result;
    }

    @Operation(summary = "发布停用提醒", description = "[author: 10200571]")
    @PostMapping("/sendDeactivate")
    public JsonResultVo<String> sendDeactivate() {
        JsonResultVo<String> result = new JsonResultVo<>();
        businessTaskService.sendDeactivate();
        return result;
    }

    @Operation(summary = "重置预警停用标识位", description = "[author: 10200571]")
    @PostMapping("/reset")
    public JsonResultVo<String> reset() {
        JsonResultVo<String> result = new JsonResultVo<>();
        businessTaskService.reset();
        return result;
    }

}
