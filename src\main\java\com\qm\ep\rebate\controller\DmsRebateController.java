package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.request.RebatePredictQueryRequest;
import com.qm.ep.rebate.domain.request.RebateQueryRequest;
import com.qm.ep.rebate.domain.response.ExtraRebateCalResult;
import com.qm.ep.rebate.domain.response.RebateCalResponse;
import com.qm.ep.rebate.domain.response.RebateQueryResponse;
import com.qm.ep.rebate.service.DmsRebateService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Tag(name = "DMS返利查询")
@RestController
@RequestMapping("/dms")
@Slf4j
@RefreshScope
public class DmsRebateController {

    @Autowired
    private DmsRebateService dmsRebateService;

    @Value("${rebate.cal.return.empty:false}")
    private boolean isReturnedEmptyJson;

    @Value("${mock.dealer.code:}")
    private String mockDealerCode;

    @Operation(summary = "接口DMS返利查询", description = "[author: 10200571]")
    @PostMapping("/queryRebate")
    public JsonResultVo<QmPage<RebateQueryResponse>> queryRebate(@Valid @RequestBody RebateQueryRequest request) {
        JsonResultVo<QmPage<RebateQueryResponse>> result = new JsonResultVo<>();
        result.setData(dmsRebateService.queryRebate(request));
        return result;
    }

    @Operation(summary = "接口DMS返利试算", description = "[author: 10200571]")
    @PostMapping("/queryRebateCal")
    public JsonResultVo<RebateCalResponse> queryRebateCal(@RequestBody RebatePredictQueryRequest request) {

        // 返回空json
        if (isReturnedEmptyJson) {
            RebateCalResponse calResponse = new RebateCalResponse();
            RebateCalResponse.SeriesRebateCalResult allSeriesRebateCalResult = new RebateCalResponse.SeriesRebateCalResult();
            allSeriesRebateCalResult.setSeriesName("全系");
            allSeriesRebateCalResult.setAak(0);
            allSeriesRebateCalResult.setEstimatedRebate(0);
            allSeriesRebateCalResult.setAverageRebate(0);

            List<ExtraRebateCalResult> extraRebateCa1lResults = new ArrayList<>();
            allSeriesRebateCalResult.setExtraRebateCalResults(extraRebateCa1lResults);

            calResponse.setAllSeriesRebateCalResult(allSeriesRebateCalResult);
            List<RebateCalResponse.SeriesRebateCalResult> seriesRebateCalResults = new ArrayList<>();
            calResponse.setSeriesRebateCalResults(seriesRebateCalResults);

            JsonResultVo<RebateCalResponse> result = new JsonResultVo<>();
            result.setData(calResponse);
            return result;
        }
        JsonResultVo<RebateCalResponse> result = new JsonResultVo<>();
        result.setData(dmsRebateService.queryRebateCal(request));
        return result;
    }

    @Operation(summary = "接口DMS政策查询", description = "[author: 10200571]")
    @PostMapping("/queryPolicy")
    public JsonResultVo<QmPage<RebateQueryResponse>> queryPolicy(@RequestBody RebateQueryRequest request) {
        JsonResultVo<QmPage<RebateQueryResponse>> result = new JsonResultVo<>();
        if (StringUtils.isNotBlank(mockDealerCode)) {
            request.setDealerCode(mockDealerCode);
        }
        result.setData(dmsRebateService.queryPolicy(request));
        return result;
    }
}
