package com.qm.ep.rebate.domain.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.SourceTypeEnum;
import com.qm.ep.rebate.enumerate.SubCalcObjectTypeEnum;
import com.qm.ep.rebate.common.config.mp.CommonEnumCodeDeserializer;
import com.qm.ep.rebate.common.config.mp.CommonEnumCodeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CALC_OBJECT_SOURCE")
@Schema(description = "数据:数据源记录表")
public class CalcObjectSourceDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-使用者-政策ID")
    @TableField("forPolicyId")
    private String forPolicyId;

    @Schema(description = "数据-使用者-计算对象ID")
    @TableField("forObjectId")
    private String forObjectId;

    @Schema(description = "数据-使用者-计算对象类型")
    @TableField("forType")
    private CalcObjectTypeEnum forType;

    @Schema(description = "数据-使用者-用作哪种类型")
    @TableField("forSourceType")
    private SourceTypeEnum forSourceType;

    @Schema(description = "数据-被使用者-政策ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "数据-被使用者-政策名称")
    @TableField("policyName")
    private String policyName;

    @Schema(description = "数据-被使用者-计算对象ID")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "数据-被使用者-计算对象名称")
    @TableField("objectName")
    private String objectName;

    @Schema(description = "数据-被使用者-计算对象类型")
    @TableField("objectType")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "数据-被使用者-计算对象类型的子类型，主要针对basic，有二级分类")
    @TableField("subObjectType")
    @JSONField(serializeUsing = CommonEnumCodeSerializer.class,
            deserializeUsing = CommonEnumCodeDeserializer.class)
    private SubCalcObjectTypeEnum subObjectType;

    @Schema(description = "数据-包含字段")
    @TableField(exist=false)
    private List<String> fields;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;
}
