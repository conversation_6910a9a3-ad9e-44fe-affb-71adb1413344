package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("FORMULA_JOIN_ON")
@Schema(description = "数据:计算公式关联条件对象")
public class FormulaJoinOnDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策主键")
    @TableField("POLICY_ID")
    private String policyId;

    @Schema(description = "数据-计算因子主键")
    @TableField("FORMULA_ID")
    private String formulaId;

    @Schema(description = "数据-计算因子关联表主键")
    @TableField("JOIN_ID")
    private String joinId;

    @Schema(description = "数据-前括号")
    @TableField("BRACKET_BEFORE")
    private String bracketBefore;

    @Schema(description = "数据-字段名")
    @TableField("FIELD_NAME")
    private String fieldName;

    @Schema(description = "数据-字段来源表")
    @TableField("FIELD_NAME_FROM")
    private String fieldNameFrom;

    @Schema(description = "数据-运算方式")
    @TableField("CAL_TYPE")
    private String calType;

    @Schema(description = "数据-筛选方式")
    @TableField("FILTRATE_TYPE")
    private String filtrateType;

    @Schema(description = "数据-筛选条件字段")
    @TableField("FILTRATE")
    private String filtrate;

    @Schema(description = "数据-筛选条件字段来源表")
    @TableField("FILTRATE_FROM")
    private String filtrateFrom;

    @Schema(description = "数据-后括号")
    @TableField("BRACKET_AFTER")
    private String bracketAfter;

    @Schema(description = "数据-关联逻辑")
    @TableField("CON_TYPE")
    private String conType;

    @Schema(description = "数据-排序")
    @TableField("sort")
    private Integer sort;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;

}
