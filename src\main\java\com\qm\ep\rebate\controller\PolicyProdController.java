package com.qm.ep.rebate.controller;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.PolicyProdDO;
import com.qm.ep.rebate.domain.dto.PolicyProdDTO;
import com.qm.ep.rebate.service.PolicyProdService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * Controller
 * 政策对应产品JsonResultVo
 *  
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Tag(name = "政策对应产品")
@RestController
@RequestMapping("/policyProd")
public class PolicyProdController extends BaseController {

    @Autowired
    private PolicyProdService policyProdService;


    /**
     * 使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<List<PolicyProdDO>> save(@RequestBody List<PolicyProdDO> tempDOs) {
        JsonResultVo<List<PolicyProdDO>> resultObj = new JsonResultVo<>();
        boolean flag = policyProdService.saveOrUpdateBatch(tempDOs);
        if (flag) {
            resultObj.setData(tempDOs);
            resultObj.setMsg("保存成功！");
        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<PolicyProdDO> deleteById(@RequestBody PolicyProdDO tempDO) {
        JsonResultVo<PolicyProdDO> resultObj = new JsonResultVo<>();
        boolean flag = policyProdService.removeById(tempDO.getId());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/getSubList1")
    @DS(DataSourceType.W)
    public JsonResultVo<QmPage<PolicyProdDO>> getSubList1(@RequestBody PolicyProdDTO tempDTO) {
        JsonResultVo<QmPage<PolicyProdDO>> ret = new JsonResultVo<>();
        ret.setData(policyProdService.getSubList1(tempDTO));
        return ret;
    }

    @Operation(summary = "导入", description = "[author: 10200571]")
    @PostMapping("/commit")
    public JsonResultVo<List<PolicyProdDO>> commit(@RequestParam MultipartFile file) {
        return policyProdService.commit(file);
    }

    @Operation(summary = "导入后数据处理", description = "[author: 10200571]")
    @PostMapping("/commitAfter")
    public JsonResultVo<List<PolicyProdDO>> commitAfter(@RequestBody PolicyProdDTO tempDTO) {
        LoginKeyDO loginKey = getUserInfo();
        return policyProdService.commitAfter(tempDTO, loginKey);
    }


    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/getSubList3")
    public JsonResultVo getSubList3(@RequestBody PolicyProdDTO tempDTO) {
        JsonResultVo ret = new JsonResultVo<>();
        //定义查询构造器
        QmQueryWrapper<PolicyProdDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<PolicyProdDO> lambdaWrapper = queryWrapper.lambda();
        //ID
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getId()), PolicyProdDO::getId, tempDTO.getId());
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getNpolicyid())) {
            lambdaWrapper.eq(PolicyProdDO::getPolicyId, tempDTO.getNpolicyid());
        } else {
            ret.setMsgErr("政策主表ID不能为空！");
            return ret;
        }
        //产品id
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNproductid()), PolicyProdDO::getProductCode, tempDTO.getNproductid());

        //查询数据，使用table函数。
        QmPage<PolicyProdDO> list = policyProdService.table(queryWrapper, tempDTO);
        ret.setData(list);
        //根据主表id，查询出关联表中信息
        ret.setDataList(policyProdService.getPolicyProdDOList(tempDTO.getNpolicyid()));
        return ret;
    }



    /**
     * 产品子表批量删除
     */
    @Operation(summary = "产品子表批量删除", description = "[author: 10200571]")
    @PostMapping("/delete")
    public JsonResultVo<List<PolicyProdDO>> delete(@RequestBody List<PolicyProdDO> tempDOs) {
        JsonResultVo<List<PolicyProdDO>> resultObj = new JsonResultVo<>();
        List<String> ids=new ArrayList<>();
        for (PolicyProdDO policyProdDO : tempDOs){
            ids.add(policyProdDO.getId());
        }
        boolean flag = policyProdService.removeByIds(ids);
        if (flag) {
            resultObj.setData(tempDOs);
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }
}
