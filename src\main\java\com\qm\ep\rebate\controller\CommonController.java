package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.common.constant.CommonConstants;
import com.qm.ep.rebate.domain.dto.EnumDicDTO;
import com.qm.ep.rebate.domain.dto.workbench.CloudNativeUserDTO;
import com.qm.ep.rebate.domain.dto.workbench.UserInfoResponseDTO;
import com.qm.ep.rebate.infrastructure.util.JsonUtil;
import com.qm.ep.rebate.infrastructure.util.UserThreadLocalUtil;
import com.qm.ep.rebate.remote.http.SendMessageHttpClient;
import com.qm.ep.rebate.remote.request.MessageRequest;
import com.qm.ep.rebate.service.CommonService;
import com.qm.ep.rebate.service.workbench.PlatformMessageService;
import com.qm.ep.rebate.service.workbench.PlatformUserService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/common")
@Tag(name = "公共服务", description = "[author: 10200571]")
@Slf4j
public class CommonController {


    @Resource
    private CommonService commonService;

    @Resource
    private PlatformUserService platformUserService;

    @Resource
    private PlatformMessageService platformMessageService;

    @Operation(summary = "通过角色代码获取用户", description = "[author: 10200571]")
    @RequestMapping(value = "/getUserByRoleCodeList", method = RequestMethod.GET)
    public JsonResultVo<List<CloudNativeUserDTO>> getUserByRoleCodeList(@RequestParam List<String> roleCodeList) {
        JsonResultVo<List<CloudNativeUserDTO>> result = new JsonResultVo<>();
        List<CloudNativeUserDTO> list = platformUserService.getUserByRoleCodeList(roleCodeList);
        result.setData(list);
        return result;
    }

    @Operation(summary = "获取用户", description = "[author: 10200571]")
    @RequestMapping(value = "/getUserInfo", method = RequestMethod.GET)
    public JsonResultVo<List<UserInfoResponseDTO>> getUserInfo(@RequestParam Map<String, String> paramMap) {
        JsonResultVo<List<UserInfoResponseDTO>> result = new JsonResultVo<>();
        String userCode=UserThreadLocalUtil.getCurrentUserCode();
        paramMap.put("loginName",userCode);
        log.info("getUserInfo.userCode: {}", JsonUtil.toJsonString(userCode));
        List<UserInfoResponseDTO> list = platformUserService.getUserInfo(paramMap);
        result.setData(list);
        return result;
    }


    @Operation(summary = "获取字典", description = "[author: 10200571]")
    @GetMapping("/enumDic")
    public JsonResultVo<List<EnumDicDTO>> queryEnumDic() {
        JsonResultVo<List<EnumDicDTO>> result = new JsonResultVo<>();
        result.setData(commonService.getEnumDic());
        return result;
    }


    @Operation(summary = "发送钉钉工作通知", description = "[author: 10200571]")
    @PostMapping("/sendDingTalkWorkNotice")
    public JsonResultVo<String> sendDingTalkWorkNotice(MessageRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(platformMessageService.sendDingTalkWorkNotice(request));
        return result;
    }





}
