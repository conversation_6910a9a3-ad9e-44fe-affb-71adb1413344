package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 政策配置-预算分配表
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("policy_budget")
@Schema(description = "数据:政策配置-预算分配表")
public class PolicyBudgetDO extends Model<PolicyBudgetDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-政策id")
    @TableField("policy_id")
    private String policyId;

    @Schema(description = "数据-是否保存（0-未保存，1-已保存）")
    @TableField("pb_status")
    private String pbStatus;

    @Schema(description = "数据-车系预算缺口金额")
    @TableField("extra_amount")
    private BigDecimal extraAmount;

    @Schema(description = "数据-车系预算缺口点数")
    @TableField("extra_point")
    private BigDecimal extraPoint;

    @Schema(description = "数据-年")
    @TableField("breakdown_year")
    private String breakdownYear;

    @Schema(description = "数据-季度")
    @TableField("breakdown_quarter")
    private String breakdownQuarter;

    @Schema(description = "数据-返利项目代码")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    @TableField("bgt_type")
    private String bgtType;

    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    @Schema(description = "数据-申请预算金额")
    @TableField("total_amount")
    private BigDecimal totalAmount;

    @Schema(description = "数据-车系申请的点数")
    @TableField("total_point")
    private BigDecimal totalPoint;

    @Schema(description = "数据-冻结金额（占用）")
    @TableField("freeze_amount")
    private BigDecimal freezeAmount;


    @Schema(description = "数据-已使用的预算金额")
    @TableField("used_amount")
    private BigDecimal usedAmount;

    @Schema(description = "数据-预算余额")
    @TableField("amount_balance")
    private BigDecimal amountBalance;

    @Schema(description = "数据-车系申请点数余额")
    @TableField("point_balance")
    private BigDecimal pointBalance;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Schema(description = "数据-提交人域账号")
    @TableField("submit_code")
    private String submitCode;

    @Schema(description = "数据-用户中文名")
    @TableField("username")
    private String username;

    @Schema(description = "数据-部门")
    @TableField("department")
    private String department;

    @Schema(description = "实际结算 -占用")
    @TableField(exist = false)
    private BigDecimal fAmount;

    @Schema(description = "实际结算 - 已使用")
    @TableField(exist = false)
    private BigDecimal uAmount;

    @Override
    public Serializable pkVal() {
        return this.id;
    }


}
