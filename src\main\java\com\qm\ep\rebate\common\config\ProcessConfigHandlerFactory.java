package com.qm.ep.rebate.common.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * @className: FormDataHandlerFactory
 * @description: TODO 类描述
 * @author: sunrenlong
 * @date: 2023/05/25 16:26
 **/
@Component
public class ProcessConfigHandlerFactory implements InitializingBean, ApplicationContextAware {

    private static final Map<String, ProcessConfig> FORM_DATA_HANDLER_MAP = new HashMap<>(16);

//  通过setApplicationContext获得上下文，从而操作bean
    private ApplicationContext appContext;

    /**
     * 根据表单标识，获取对应的 Handler
     *
     * @param formCode 表单标识
     * @return 表单对应的 Handler
     */
    public ProcessConfig getHandler(String formCode) {
        return FORM_DATA_HANDLER_MAP.get(formCode);
    }


    /**
     * 将符合条件的bean直接从容器中取出放入map，这个是策略模式的核心
     *
     * @throws Exception 异常
     * <AUTHOR>
     */
    @Override
    public void afterPropertiesSet() throws Exception {

        // 获取 Spring 容器中，所有 FormDataHandler 类型的 Bean，put到map中
        Collection<ProcessConfig> formDataHandlers = appContext.getBeansOfType(ProcessConfig.class).values();
        for (final ProcessConfig formDataHandler : formDataHandlers) {
            FORM_DATA_HANDLER_MAP.put(formDataHandler.getCategory(), formDataHandler);
        }
    }

    /**
     * 设置应用程序上下文，取到上下文
     *
     * @param applicationContext 应用程序上下文
     * @throws BeansException 豆子例外
     * <AUTHOR>
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.appContext = applicationContext;
    }

}