package com.qm.ep.rebate.domain.dto.structure;

import com.qm.ep.rebate.domain.bean.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-报告免费 DTO")
@Data
public class ReportFreeDTO {

    @Schema(description = "数据-主要")
    private ReportMainDO main;
    @Schema(description = "数据-详")
    private List<ReportDetailDO> details;
    @Schema(description = "数据-内容")
    private List<FormulaDO> contents;
    @Schema(description = "数据-加入")
    private List<ReportJoinDO> joins;
    @Schema(description = "数据-条件")
    private List<ReportConditionDO> conditions;


}