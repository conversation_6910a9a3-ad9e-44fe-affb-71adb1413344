package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@Schema(description = "数据:RedFlagTrialConditionsDTO对象")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RedFlagTrialConditionsDTO extends FilterCondition {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

}

