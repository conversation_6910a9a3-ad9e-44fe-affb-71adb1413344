package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.ClassItemBindUserDTO;
import com.qm.ep.rebate.domain.dto.ClassItemDTO;
import com.qm.ep.rebate.domain.dto.FawDepartmentDTO;
import com.qm.ep.rebate.domain.request.BgtEomRequest;
import com.qm.ep.rebate.domain.request.ClassItemRequest;
import com.qm.ep.rebate.domain.vo.FawAgentVO;
import com.qm.ep.rebate.domain.vo.FawDepartmentVO;
import com.qm.ep.rebate.enumerate.AccountEntryMethodEnum;
import com.qm.ep.rebate.enumerate.BgtTypeEnum;
import com.qm.ep.rebate.enumerate.PolicyCycleEnum;
import com.qm.ep.rebate.enumerate.PolicyStageEnum;
import com.qm.ep.rebate.mapper.ClassItemMapper;
import com.qm.ep.rebate.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebate.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/classItem")
@Tag(name = "返利项目", description = "[author: ********]")
@RefreshScope
public class ClassItemController extends BaseController {

    @Autowired
    private ClassItemLogService classItemLogService;
    @Value("${classItem.valid:true}")
    private boolean validClassItem;
    @Autowired
    private ClassItemApproveService classItemApproveService;
    @Autowired
    private PolicyPublishedService policyPublishedService;
    @Resource
    private ClassItemMapper classItemMapper;
    @Resource
    private ClassItemService classItemService;
    @Resource
    private PolicyService policyService;
    @Resource
    private SystemConfigService systemConfigService;
    @Resource
    private SysPersonOrgService sysPersonOrgService;
    @Resource
    private BgtEomService bgtEomService;
    @Autowired
    private BgtBreakdownMainService bgtBreakdownMainService;


    @Resource
    private ExecFormalCalcHistoryMapper execFormalCalcHistoryMapper;


    /**
     * 查询返利项目更新日志
     * @param classItemDTO
     * @return
     */
    @Operation(summary = "查询返利项目更改日志", description = "[author: ********]")
    @PostMapping("/listClassItemLog")
    public JsonResultVo<List<ClassItemLogPO>> listClassItemLog(@RequestBody ClassItemDTO classItemDTO) {
        JsonResultVo<List<ClassItemLogPO>> ret = new JsonResultVo<>();

        LambdaQueryWrapper<ClassItemLogPO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.like(StrUtil.isNotBlank(classItemDTO.getClassitemCode()), ClassItemLogPO::getClassitemCode, classItemDTO.getClassitemCode());
        lambdaWrapper.like(StrUtil.isNotBlank(classItemDTO.getColumnName()), ClassItemLogPO::getColumnName, classItemDTO.getColumnName());
        lambdaWrapper.orderByDesc(ClassItemLogPO::getId);
        List<ClassItemLogPO> list = classItemLogService.list(lambdaWrapper);
        ret.setData(list);
        return ret;
    }

    /**
     * 查询返利项目列表
     * @param classItemDTO
     * @return
     */
    @Operation(summary = "查询返利项目列表-分页", description = "[author: ********]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ClassItemDO>> table(@RequestBody ClassItemDTO classItemDTO) {
        // 定义查询构造器
        QmQueryWrapper<ClassItemDO> queryWrapper = new QmQueryWrapper<>();
        // 拼装实体属性查询条件
        LambdaQueryWrapper<ClassItemDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(StrUtil.isNotBlank(classItemDTO.getId()), ClassItemDO::getId, classItemDTO.getId());
        // 公司ID
        lambdaWrapper.eq(ClassItemDO::getCompanyId, getUserInfo().getCompanyId());
        // 返利项目代码
        lambdaWrapper.like(StrUtil.isNotBlank(classItemDTO.getClassItem()), ClassItemDO::getClassItem, classItemDTO.getClassItem());
        // 返利项目名称
        lambdaWrapper.like(StrUtil.isNotBlank(classItemDTO.getClassItemName()), ClassItemDO::getClassItemName, classItemDTO.getClassItemName());
        // 对应信用账户
        lambdaWrapper.eq(StrUtil.isNotBlank(classItemDTO.getCreditAcct()), ClassItemDO::getCreditAcct, classItemDTO.getCreditAcct());
        // 停用标识
        lambdaWrapper.eq(classItemDTO.getStop() != null, ClassItemDO::getStop, classItemDTO.getStop());
        // 停用日期
        lambdaWrapper.eq(classItemDTO.getStopDate() != null, ClassItemDO::getStopDate, classItemDTO.getStopDate());

        QmPage<ClassItemDO> list = classItemService.table(queryWrapper, classItemDTO);
        JsonResultVo<QmPage<ClassItemDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询返利项目列表", description = "[author: ********]")
    @PostMapping("/getClassItem")
    public JsonResultVo getClassItem(@RequestBody ClassItemDTO classItemDTO) {
        String name = classItemDTO.getName();

        ClassItemDTO.Condition condition = classItemDTO.getCondition();
        ClassItemDTO.FixCondition fixCondition = classItemDTO.getFixCondition();

        QmPage<ClassItemDO> list = new QmPage<>();

        // 因为前端使用了通用的组件，而由于返利项目收口到政策中心，所以这里进行了兼容处理（政策复制时查询政策编码时使用的是/getClassItem接口）
        if ("UI_REBATE_POLICY_INFO".equals(name)) {
            QmPage<PolicyPublishedDO> policyPublishPages = new QmPage<>();

            // 定义查询构造器
            QmQueryWrapper<PolicyPublishedDO> queryWrapper = new QmQueryWrapper<>();
            // 拼装实体属性查询条件
            LambdaQueryWrapper<PolicyPublishedDO> lambdaWrapper = queryWrapper.lambda();
            if(Objects.nonNull(condition) && StringUtils.isNotBlank(condition.getVpolicycode())) {
                String policyCode = condition.getVpolicycode();
                lambdaWrapper.like(StrUtil.isNotBlank(policyCode), PolicyPublishedDO::getVpolicycode, policyCode);
            }

            // 返利项目名称
            if(Objects.nonNull(condition) && StringUtils.isNotBlank(condition.getVpolicyname())) {
                String policyName = condition.getVpolicyname();
                lambdaWrapper.like(StrUtil.isNotBlank(policyName), PolicyPublishedDO::getVpolicyname, policyName);
            }
            if(CollUtil.isNotEmpty(classItemDTO.getStage())){
                lambdaWrapper.in(PolicyPublishedDO::getStage, classItemDTO.getStage());
            }
            lambdaWrapper.in(PolicyPublishedDO::getStage, Arrays.asList(PolicyStageEnum.ASSIGNED.getCode(), PolicyStageEnum.CONFIGURED.getCode(), PolicyStageEnum.NO_CONFIGURATION_CONFIGURATION_REQUIRED.getCode()));
            lambdaWrapper.eq(PolicyPublishedDO::getStop, "0");

            policyPublishPages = policyPublishedService.table(queryWrapper, classItemDTO);

            policyPublishPages.getItems().forEach(ele -> {
                String stage = ele.getStage();
                if (StringUtils.isNoneBlank(stage)) {
                    ele.setStage(PolicyStageEnum.getDescByCode(Integer.valueOf(stage)));
                    if(StringUtils.isNotBlank(ele.getPolicycycle())) {
                        ele.setPolicycycle(PolicyCycleEnum.getDescByCode(ele.getPolicycycle()));
                    }
                }
            });
            JsonResultVo<Object> ret = new JsonResultVo<>();
            ret.setData(policyPublishPages);
            return ret;
        }

        // 定义查询构造器
        QmQueryWrapper<ClassItemDO> queryWrapper = new QmQueryWrapper<>();
        // 拼装实体属性查询条件
        LambdaQueryWrapper<ClassItemDO> lambdaWrapper = queryWrapper.lambda();
        // 公司ID
        // lambdaWrapper.eq(ClassItemDO::getCompanyId, getUserInfo().getCompanyId());
        // 返利项目代码
        if (condition == null && StringUtils.isNoneBlank(classItemDTO.getTwhere())) {
            String twhere = classItemDTO.getTwhere();
            String classItemOrName = StringUtils.substringBetween(twhere, "'", "'");
            LambdaQueryWrapper<ClassItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();

            lambdaQueryWrapper.like(ClassItemDO::getClassItem, classItemOrName)
                    .or().like(ClassItemDO::getClassItemName, classItemOrName);
            List<ClassItemDO> classItemDOS = classItemMapper.selectList(lambdaQueryWrapper);
            list.setItems(classItemDOS);
        } else {
            lambdaWrapper.like(StrUtil.isNotBlank(condition.getClassItemCode()), ClassItemDO::getClassItem, condition.getClassItemCode());
            // 返利项目名称
            lambdaWrapper.like(StrUtil.isNotBlank(condition.getClassItemName()), ClassItemDO::getClassItemName, condition.getClassItemName());
            // 对应信用账户
            // lambdaWrapper.eq(StrUtil.isNotBlank(fixCondition.getCreditAcct()), ClassItemDO::getCreditAcct, fixCondition.getCreditAcct());
            // 停用标识
            lambdaWrapper.eq(ClassItemDO::getStop, "0");
            list = classItemService.table(queryWrapper, classItemDTO);
        }


        list.getItems().forEach(ele -> {
            ele.setClassItemCode(ele.getClassItem());
            ele.setClassItemCodeName(ele.getClassItem() + "," + ele.getClassItemName());
        });
        JsonResultVo<Object> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询返利项目列表-不分页", description = "[author: ********]")
    @PostMapping("/list")
    public JsonResultVo<List<ClassItemDO>> list(@RequestBody ClassItemDTO classItemDTO) {
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);
        // 定义查询构造器
        QmQueryWrapper<ClassItemDO> queryWrapper = new QmQueryWrapper<>();
        // 拼装实体属性查询条件
        LambdaQueryWrapper<ClassItemDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(StrUtil.isNotBlank(classItemDTO.getId()), ClassItemDO::getId, classItemDTO.getId());
        // 公司ID
        lambdaWrapper.eq(ClassItemDO::getCompanyId, getUserInfo().getCompanyId());
        // 返利项目代码
        lambdaWrapper.like(StrUtil.isNotBlank(classItemDTO.getClassItem()), ClassItemDO::getClassItem, classItemDTO.getClassItem());
        // 返利项目名称
        lambdaWrapper.like(StrUtil.isNotBlank(classItemDTO.getClassItemName()), ClassItemDO::getClassItemName, classItemDTO.getClassItemName());
        // 对应信用账户
        lambdaWrapper.eq(StrUtil.isNotBlank(classItemDTO.getCreditAcct()), ClassItemDO::getCreditAcct, classItemDTO.getCreditAcct());
        // 停用标识
        String stop = classItemDTO.getStop();
        if ("1".equals(stop)){
            lambdaWrapper.in(ClassItemDO::getStop, Arrays.asList("1", "0"));
        }else{
            lambdaWrapper.eq(ClassItemDO::getStop, "0");
        }
        // 停用日期
        lambdaWrapper.eq(classItemDTO.getStopDate() != null, ClassItemDO::getStopDate, classItemDTO.getStopDate());
        // 入账方式
        lambdaWrapper.eq(StrUtil.isNotBlank(classItemDTO.getAccountMode()), ClassItemDO::getAccountMode, classItemDTO.getAccountMode());
        lambdaWrapper.eq(StrUtil.isNotBlank(classItemDTO.getRebateType()), ClassItemDO::getRebateType, classItemDTO.getRebateType());
        lambdaWrapper.eq(StrUtil.isNotBlank(classItemDTO.getBgtType()), ClassItemDO::getBgtType, classItemDTO.getBgtType());

        lambdaWrapper.orderByDesc(ClassItemDO::getCreateOn);

        JsonResultVo<List<ClassItemDO>> ret = new JsonResultVo<>();
        if ("bx".equals(company)) {
            LoginKeyDO userInfo = getUserInfo();
            List<String> personIds = sysPersonOrgService.queryPersonIdAtRelatedLevel(userInfo.getOperatorId());
            if (CollUtil.isNotEmpty(personIds)) {
                lambdaWrapper.in(ClassItemDO::getCreateBy, personIds);
            } else {
                ret.setData(Collections.emptyList());
                return ret;
            }
        }

        List<ClassItemDO> list = classItemService.list(lambdaWrapper);
        list = classItemApproveService.classItemDeptInfo(list);
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "只有新建", description = "[author: ********]")
    @PostMapping("/saveOrUpdate")
    public JsonResultVo<ClassItemDO> saveOrUpdate(@Validated @RequestBody ClassItemDO tempDO) {
        JsonResultVo<ClassItemDO> resultObj = new JsonResultVo<>();
        LoginKeyDO loginKey = getUserInfo();
        tempDO.setUpdateByName(loginKey.getOperatorName());
        tempDO.setCreateByName(loginKey.getOperatorName());
        tempDO.setCreateBy(loginKey.getOperatorName());
        tempDO.setUpdateBy(loginKey.getOperatorName());
        if (!checkName(resultObj, tempDO)) {
            return resultObj;
        }
        // 更新
        if (!BootAppUtil.isNullOrEmpty(tempDO.getId())) {
            ClassItemDO oldClassItemInfo = classItemService.getById(tempDO.getId());
            if (oldClassItemInfo == null) {
                resultObj.setMsgErr(String.format("此ID:%s 返利项目不存在,请检查!", tempDO.getId()));
                return resultObj;
            }
            classItemService.updateClass(tempDO);

            resultObj.setData(oldClassItemInfo);
            return resultObj;
        }
        // 新建
        else {
            // tempDO.setAccountMode(AccountEntryMethodEnum.AUDIT);
            tempDO.setCreditAcct("F01");

            // tempDO.setStopDate(tempDO.getStopDate() == null ? new Date() : tempDO.getStopDate());
            classItemService.saveClassItem(tempDO);


            // 专项同步到eom
            if (Objects.equals(BgtTypeEnum.SPECIAL.getCode(), tempDO.getBgtType())) {
                String eomDeliverId = IdWorker.get32UUID();
                BgtEomRequest bgtEomRequest = new BgtEomRequest();
                bgtEomRequest.setBgtType(BgtTypeEnum.SPECIAL.getCode());
                bgtEomRequest.setClassItem(tempDO.getClassItem());
                bgtEomRequest.setEomTotalAmount(new BigDecimal("0"));
                String eomYear = LocalDate.now().getYear() + "";
                bgtEomRequest.setEomYear(eomYear);
                bgtEomRequest.setVersion("V1");

                QueryWrapper<BgtEomDO> maxWrapper = new QueryWrapper<>();
                maxWrapper.select("max(id) as id");
                BgtEomDO eomMaxVerDo = bgtEomService.getOne(maxWrapper);
                eomMaxVerDo = bgtEomService.getById(eomMaxVerDo.getId());
                if (eomMaxVerDo != null) {
                    eomDeliverId = eomMaxVerDo.getEomDeliverId();
                    bgtEomRequest.setEomYear(eomMaxVerDo.getEomYear());
                    bgtEomRequest.setVersion(eomMaxVerDo.getVersion());
                }
                bgtEomService.saveBgtEom(bgtEomRequest, eomDeliverId);
            }

            // 查询可用的版本数据
            QueryWrapper<BgtBreakdownMainDO> maxWrapper = new QueryWrapper<>();
            maxWrapper.eq("use_status", "1");
            List<BgtBreakdownMainDO> queryList = bgtBreakdownMainService.list(maxWrapper);
            ClassItemDTO classItem = new ClassItemDTO();
            classItem.setBgtType(tempDO.getBgtType());
            classItem.setClassItem(tempDO.getClassItem());
            classItem.setClassItemName(tempDO.getClassItemName());
            bgtBreakdownMainService.bgtBreakdownInitWhenCreateClassItem(classItem, queryList);
            resultObj.setData(tempDO);
        }
        return resultObj;
    }

    @Operation(summary = "控制器接口", description = "[author: ********]")
    public boolean checkName(JsonResultVo<ClassItemDO> resultObj, ClassItemDO tempDO) {

        if (StringUtils.isBlank(tempDO.getClassItemName())) {
            resultObj.setMsgErr("返利项目名称不能为空！");
            return false;
        }
        if (StringUtils.isBlank(tempDO.getClassItem())) {
            resultObj.setMsgErr("返利项目代码不能为空！");
            return false;
        }
        if (StringUtils.isBlank(tempDO.getBgtType())) {
            resultObj.setMsgErr("预算类型不能为空！");
            return false;
        }


        List<ClassItemDO> queryList = classItemService.list();
        if (!BootAppUtil.isNullOrEmpty(tempDO.getId())) {
            queryList = queryList.stream().filter(t -> !Objects.equals(t.getId(), tempDO.getId())).collect(Collectors.toList());
        }
        List<String> classItems = queryList.stream().map(ClassItemDO::getClassItem).collect(Collectors.toList());
        List<String> classItemNames = queryList.stream().map(ClassItemDO::getClassItemName).collect(Collectors.toList());
        if (classItems.contains(tempDO.getClassItem())) {
            resultObj.setMsgErr("返利项目代码已存在，保存失败！");
            return false;
        }
        if (classItemNames.contains(tempDO.getClassItemName())) {
            resultObj.setMsgErr("返利项目名称已存在，保存失败！");
            return false;
        }
        return Boolean.TRUE;
    }


    @Operation(summary = "修改", description = "[author: ********]")
    @PostMapping("/save")
    public JsonResultVo<ClassItemDO> save( @RequestBody ClassItemDO tempDO) {
        this.checkClassItem(tempDO);
        JsonResultVo<ClassItemDO> resultObj = new JsonResultVo<>();
        LoginKeyDO loginKey = getUserInfo();

        // 保存公司id(数据表中没有公司id请自行删除)
        if (BootAppUtil.isNullOrEmpty(tempDO.getId())) {
            // 校验返利项目代码是否重复
            LambdaQueryWrapper<ClassItemDO> classItemWrapper = new QmQueryWrapper<ClassItemDO>().lambda();
            classItemWrapper.eq(ClassItemDO::getClassItem, tempDO.getClassItem());
            long count = classItemService.count(classItemWrapper);
            if (count > 0) {
                resultObj.setMsgErr("返利项目代码已存在，保存失败！");
                return resultObj;
            }
            tempDO.setCompanyId(loginKey.getCompanyId());
        }

        if (CharSequenceUtil.isBlank(tempDO.getCreditAcct())) {
            // 不知为何，前端传回的数据，偶尔会有丢失对应账户的，也无法复现
            resultObj.setMsgErr("对应账户不能为空，保存失败！");
            return resultObj;
        }
        if ("1".equals(tempDO.getStop())) {
            List<String> policyNames = execFormalCalcHistoryMapper.selectApplyingEntryByClassItem(tempDO.getClassItem());
            if (CollUtil.isNotEmpty(policyNames)) {
                StringBuilder sb = new StringBuilder("当前政策正在申请入账，请处理后在进行停用:");
                for (String policyName : policyNames) {
                    sb.append(policyName);
                    sb.append(",");
                }
                sb.delete(sb.lastIndexOf(","), sb.length());
                throw new QmException(sb.toString());
            }
        }
        // 更新
        if (!BootAppUtil.isNullOrEmpty(tempDO.getId())) {
            ClassItemDO oldClassItemInfo = classItemService.getById(tempDO.getId());
            // 记录修改日志
            classItemLogService.recordModifyLog(oldClassItemInfo, tempDO, loginKey);
        }
        boolean flag = classItemService.saveOrUpdateWithLog(tempDO);
        if (flag) {
            // 更改该项目代码下所有政策的入账方式（财务审核改过的除外）
            List<PolicyDO> policyDOS = classItemService.getPolicyList(tempDO.getClassItem());
            policyDOS.forEach(item -> {
                item.setAccountEntryMethod(tempDO.getAccountMode());
                policyService.updateById(item);
            });
            resultObj.setData(tempDO);
            resultObj.setMsg("保存成功！");
        } else {
            resultObj.setMsgErr("保存失败！");
        }


        return resultObj;
    }

    private void checkClassItem(ClassItemDO tempDO) {
        AccountEntryMethodEnum accountMode = tempDO.getAccountMode();
        if (StringUtils.isBlank(accountMode.getCode())){
            throw new QmException("入账方式不能为空！");
        }
    }

    @Operation(summary = "查询返利项目列表-不分页", description = "[author: ********]")
    @PostMapping("/getClassItemList")
    public JsonResultVo<List<ClassItemDO>> getClassItemList(@RequestBody ClassItemRequest classItemReq) {

        JsonResultVo<List<ClassItemDO>> ret = new JsonResultVo<>();

        List<ClassItemDO> list = classItemService.getUsingClassItemList();


        if (StringUtils.isNotEmpty(classItemReq.getBgtType())) {

            // 代理制修改--直接从返利项目筛选
            list = list.stream().filter(t -> Objects.equals(t.getBgtType(), classItemReq.getBgtType())).collect(Collectors.toList());

        }
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询返利项目列表-带权限", description = "[author: ********]")
    @PostMapping("/getClassItemListWithApprove")
    public JsonResultVo<List<ClassItemDO>> getClassItemListWithApprove(@RequestBody ClassItemRequest request) {
        JsonResultVo<List<ClassItemDO>> ret = new JsonResultVo<>();
        List<ClassItemDO> list = classItemService.getUsingClassItemList();
        // 过滤掉当前用户无权限的项目
        List<ClassItemApproveDO> classItemApproveDOS = classItemApproveService.getCurrentUserClassItemApproveDOList();
        Set<String> approvedNames = classItemApproveDOS.stream().map(ClassItemApproveDO::getClassItem).collect(Collectors.toSet());
        if (validClassItem) {
            list.removeIf(ele -> !approvedNames.contains(ele.getClassItem()));
        }
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询机构 - 树形结构", description = "[author: ********]")
    @PostMapping("/getOrganizationTree")
    public JsonResultVo<List<FawDepartmentVO>> getOrganizationTree() {
        return classItemService.getOrganizationTree(new FawDepartmentDTO());
    }

    @Operation(summary = "通过部门获取人员", description = "[author: ********]")
    @PostMapping("/findUserByOrganization")
    public JsonResultVo<List<FawAgentVO>> findUserByOrganization(@RequestBody FawDepartmentDTO fawDepartmentDTO) {
        return classItemService.findUserByOrganization(fawDepartmentDTO);
    }

    @Operation(summary = "返利项目绑定人员", description = "[author: ********]")
    @PostMapping("bindingUser")
    public JsonResultVo<ClassItemBindUserDTO> bindingUser(@RequestBody ClassItemBindUserDTO classItemBindUserDTO) {
        LoginKeyDO loginKeyDO = getUserInfo();
        return classItemApproveService.bindingUser(classItemBindUserDTO, loginKeyDO);
    }

}
