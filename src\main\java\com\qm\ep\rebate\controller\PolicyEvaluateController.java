package com.qm.ep.rebate.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.PolicyEvaluatePO;
import com.qm.ep.rebate.domain.request.PolicyEvaluateQueryRequest;
import com.qm.ep.rebate.domain.request.PolicyRebateQueryRequest;
import com.qm.ep.rebate.domain.response.PolicyCalDetailResponse;
import com.qm.ep.rebate.domain.response.PolicyEvaluateQueryResponse;
import com.qm.ep.rebate.domain.response.PolicyRebateDetailResponse;
import com.qm.ep.rebate.domain.response.PolicyRebateResponse;
import com.qm.ep.rebate.service.PolicyEvaluateService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 
 * 政策评价
 *
 *
 * <AUTHOR>
 * @since 2024-10-10
 * @eo.api-type http
 * @eo.groupName 政策评价
 * @eo.path /policy-evaluate
 */
@Tag(name = "政策评价表", description = "[author: 10200571]")
@RestController
@RequestMapping("/policy-evaluate")

@Slf4j
public class PolicyEvaluateController {

    @Resource
    private PolicyEvaluateService policyEvaluateService;

    /**
     * 政策评价列表查询
     *
     * @param request 请求
     * @return {@link JsonResultVo }<{@link List }<{@link PolicyEvaluatePO }>>
     * @eo.name 政策评价列表查询
     * @eo.url /tableList
     * @eo.method post
     * @eo.request-type json
     */
    @Operation(summary = "政策评价列表查询", description = "[author: 10200571]")
    @PostMapping("/tableList")
    public JsonResultVo<PolicyEvaluateQueryResponse> tableList(@RequestBody PolicyEvaluateQueryRequest request) {
        JsonResultVo<PolicyEvaluateQueryResponse> resultObj = new JsonResultVo<>();
        resultObj.setData(policyEvaluateService.tableList(request));
        return resultObj;
    }

    /**
     * 保存政策评价
     *
     * @param saveRequest 保存请求
     * @return {@link JsonResultVo }<{@link PolicyEvaluatePO }>
     */
    @Operation(summary = "保存政策评价", description = "[author: 10200571]")
    @PostMapping("/saveEvaluate")
    public JsonResultVo<PolicyEvaluatePO> saveEvaluate(@RequestBody PolicyEvaluatePO saveRequest) {
        JsonResultVo<PolicyEvaluatePO> resultObj = new JsonResultVo<>();
        resultObj.setData(policyEvaluateService.saveEvaluate(saveRequest));
        return resultObj;
    }


    /**
     * 政策评价详情
     *
     * @param saveRequest 保存请求
     * @return {@link JsonResultVo }<{@link PolicyEvaluatePO }>
     */
    @Operation(summary = "政策评价详情", description = "[author: 10200571]")
    @PostMapping("/detailById")
    public JsonResultVo<PolicyEvaluatePO> detailById(@RequestBody PolicyEvaluatePO saveRequest) {
        JsonResultVo<PolicyEvaluatePO> resultObj = new JsonResultVo<>();
        LambdaQueryWrapper<PolicyEvaluatePO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.eq(StringUtils.isNotBlank(saveRequest.getId()), PolicyEvaluatePO::getId, saveRequest.getId());
        lambdaWrapper.eq(StringUtils.isNotBlank(saveRequest.getTaskInstanceCode()), PolicyEvaluatePO::getTaskInstanceCode, saveRequest.getTaskInstanceCode());
        resultObj.setData(policyEvaluateService.getOne(lambdaWrapper));
        return resultObj;
    }
    /**
     * 测试政策评价计算
     *
     * @param request 政策评价计算
     * @return {@link JsonResultVo }<{@link PolicyEvaluatePO }>
     */
    @Operation(summary = "政策评价计算", description = "[author: 10200571]")
    @PostMapping("/startCalculatePolicyEvaluate")
    public JsonResultVo<String> startCalculatePolicyEvaluate(@RequestBody Map<String,String> request) {
        JsonResultVo<String> resultObj = new JsonResultVo<>();
        policyEvaluateService.startCalculatePolicyEvaluate(request.get("policyId"));
        resultObj.setData("success");
        return resultObj;
    }


    /**
     * 返利结果查询看板
     *
     * @param request 请求
     * @return {@link JsonResultVo }<{@link List }<{@link PolicyEvaluatePO }>>
     * @eo.name 返利结果查询看板
     * @eo.url /tableList
     * @eo.method post
     * @eo.request-type json
     */
    @Operation(summary = "返利结果查询看板-列表", description = "[author: 10200571]")
    @PostMapping("/tableRebateResult")
    public JsonResultVo<QmPage<PolicyRebateResponse>> tableRebateResult(@RequestBody PolicyRebateQueryRequest request) {
        JsonResultVo<QmPage<PolicyRebateResponse>> resultObj = new JsonResultVo<>();
        resultObj.setData(policyEvaluateService.tableRebateResult(request));
        return resultObj;
    }


    /**
     * 返利结果查询看板-详情
     *
     * @param request 请求
     * @return {@link JsonResultVo }<{@link List }<{@link PolicyEvaluatePO }>>
     * @eo.name 返利结果查询看板
     * @eo.url /tableList
     * @eo.method post
     * @eo.request-type json
     */
    @Operation(summary = "返利结果查询看板-详情", description = "[author: 10200571]")
    @PostMapping("/detailRebateResult")
    public JsonResultVo<QmPage<PolicyRebateDetailResponse>> detailRebateResult(@Valid @RequestBody PolicyRebateQueryRequest request) {
        JsonResultVo<QmPage<PolicyRebateDetailResponse>> resultObj = new JsonResultVo<>();
        resultObj.setData(policyEvaluateService.detailRebateResult(request));
        return resultObj;
    }


    /**
     * 计算数据详情
     *
     * @param request 请求
     * @return {@link JsonResultVo }<{@link List }<{@link PolicyEvaluatePO }>>
     * @eo.name 计算数据详情
     * @eo.url /tableList
     * @eo.method post
     * @eo.request-type json
     */
    @Operation(summary = "计算数据详情", description = "[author: 10200571]")
    @PostMapping("/detailCalculateData")
    public JsonResultVo<PolicyCalDetailResponse> detailCalculateData(@RequestBody PolicyRebateQueryRequest request) {
        JsonResultVo<PolicyCalDetailResponse> resultObj = new JsonResultVo<>();
        resultObj.setData(policyEvaluateService.detailCalculateData(request));
        return resultObj;
    }

}
