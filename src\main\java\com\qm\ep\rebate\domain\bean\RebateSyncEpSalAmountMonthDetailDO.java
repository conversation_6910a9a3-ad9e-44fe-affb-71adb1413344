package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 同步ep 返利金额 （车系以及政策汇总金额）月汇总数据表
 *  
 *
 * <AUTHOR> @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sync_ep_rebate_month")
@Schema(description = "数据:rebate同步ep月金额数据")
public class RebateSyncEpSalAmountMonthDetailDO extends Model<RebateSyncEpSalAmountMonthDetailDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Schema(description = "数据-额外参数Json")
    @TableField("extended_param")
    private String extendedParams;


    @Schema(description = "数据-数据类型 :车型 series,政策policy")
    @TableField("data_type")
    private String dataType;

    @Schema(description = "数据-金额")
    @TableField("target_value")
    private BigDecimal targetValue;


    @Schema(description = "数据-数据分组type标识 1大区分组 2:经销商分组")
    @TableField("group_type")
    private Integer groupType;


    @Schema(description = "数据-大区名称")
    @TableField("region_name")
    private String regionName;


    @Schema(description = "数据-名称-车系或者政策名称")
    @TableField("show_name")
    private String showName;


    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;


    @Schema(description = "数据-业务数据时间")
    @TableField(value = "business_time")
    private String businessTime;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
