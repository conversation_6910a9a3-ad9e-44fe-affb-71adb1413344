package com.qm.ep.rebate.domain.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "数据:策略库 BGT 响应")
@Data
public class PolicyBaseBgtResponse {
    /**
     * 预算金额
     */

    @Schema(description = "预算金额")
    private BigDecimal totalAmount;
    /**
     * 返利项目车系余额
     */
    @Schema(description = "返利项目车系余额")
    private BigDecimal classItemAmountBalance;
    /**
     * 返利项目预算类型
     */
    @Schema(description = "返利项目预算类型")
    private String bgtType;
}
