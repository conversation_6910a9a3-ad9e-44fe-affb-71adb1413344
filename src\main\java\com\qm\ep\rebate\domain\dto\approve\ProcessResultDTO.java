package com.qm.ep.rebate.domain.dto.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2023/7/4 11:26
 * @version: $
 */
@Schema(description = "数据:实体类-：")
@Data
@Builder
public class ProcessResultDTO<T> {
    @Schema(description = "数据-地位")
    private String status;
    @Schema(description = "数据-味精代码")
    private String msgCode;
    @Schema(description = "数据-味精")
    private T msg;

    public ProcessResultDTO() {
    }

    private ProcessResultDTO(String status, String msgCode, T msg) {
        this.status = status;
        this.msgCode = msgCode;
        this.msg = msg;
    }
    public static <T> ProcessResultDTO<T> success(T msg) {
        return new ProcessResultDTO("1", "200",  msg);
    }

    public static <T> ProcessResultDTO<T> faild(T msg) {
        return new ProcessResultDTO("0", "500",  msg);
    }
}
