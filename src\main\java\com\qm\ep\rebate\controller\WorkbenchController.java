package com.qm.ep.rebate.controller;

import cn.hutool.core.util.RandomUtil;
import com.qm.ep.rebate.domain.bean.SystemConfigDO;
import com.qm.ep.rebate.domain.dto.workbench.TriggerInfoDTO;
import com.qm.ep.rebate.domain.dto.workbench.TriggerTaskResponseDTO;
import com.qm.ep.rebate.domain.dto.workbench.WorkbenchRequest;
import com.qm.ep.rebate.enumerate.RebateCodeEnum;
import com.qm.ep.rebate.service.SystemConfigService;
import com.qm.ep.rebate.service.workbench.WorkbenchService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/work")
@Tag(name = "任务")
public class WorkbenchController {
    @Resource
    private WorkbenchService workbenchService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Operation(summary = "关闭任务", description = "[author: 10200571]")
    @GetMapping("/closeTask")
    public JsonResultVo<Void> closeTask(@RequestParam String closeKaCode, @RequestParam String closeTaskInstanceCode) {
        try {
            workbenchService.closeTask(closeKaCode, closeTaskInstanceCode);
            return new JsonResultVo<>();
        } catch (Exception e) {
            JsonResultVo<Void> data = new JsonResultVo();
            data.setCode(500);
            data.setMsgErr(e.getMessage(), e);
            return data;
        }


    }

    @Operation(summary = "触发首节点任务", description = "[author: 10200571]")
    @PostMapping("/startFirstTask")
    public JsonResultVo startFirstTask(@Validated @RequestBody WorkbenchRequest request) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        result.setData(workbenchService.triggerStartTask(request.getCode(), request.getTriggerInfoList()));
        return result;
    }

    @Operation(summary = "触发中间节点任务", description = "[author: 10200571]")
    @PostMapping("/startMiddleTask")
    public JsonResultVo startMiddleTask(@Validated @RequestBody WorkbenchRequest request) {
        JsonResultVo<Object> result = new JsonResultVo<>();
        if (StringUtils.isBlank(request.getPreTaskInstanceCode())) {
            throw new QmException("preTaskInstanceCode不能为空");
        }
        result.setData(workbenchService.triggerMiddleTask(request.getPreTaskInstanceCode(), request.getCode(), request.getTriggerInfoList()));
        return result;
    }

    @Operation(summary = "测试创建待办", description = "[author: 10200571]")
    @GetMapping("/triggerStartTaskTest")
    public JsonResultVo<TriggerTaskResponseDTO> triggerStartTaskTest(@RequestBody Map<String, String> userCodeMap) {
        JsonResultVo<TriggerTaskResponseDTO> jsonResultVo = new JsonResultVo<>();
        TriggerTaskResponseDTO triggerTaskResponseDTO = null;
        try {
            TriggerInfoDTO triggerInfoDTO = TriggerInfoDTO.builder()
                    .userCode(userCodeMap.get("userCode"))
                    .description(userCodeMap.get("desc"))
                    .bizId(UUID.randomUUID().toString())
                    .build();
            triggerTaskResponseDTO = workbenchService.triggerStartTaskForBudget(userCodeMap.get("ka_code"), triggerInfoDTO);
            jsonResultVo.setData(triggerTaskResponseDTO);
        } catch (Exception e) {
            jsonResultVo.setCode(500);
            jsonResultVo.setMsgErr(e.getMessage(), e);
        }

        return jsonResultVo;
    }

    @Operation(summary = "定时任务-批量关闭政策配置及发布入账待办任务", description = "[author: 10200571]")
    @GetMapping("/scheduleCloseTask")
    public JsonResultVo<Void> scheduleCloseTask(String closeKaCode, String createOn) {
        if (StringUtils.isBlank(createOn)) {
            createOn = "2024-01-01";
        }
        workbenchService.scheduleCloseTask(closeKaCode, createOn);
        return new JsonResultVo<>();
    }



}
