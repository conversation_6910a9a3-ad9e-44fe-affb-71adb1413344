package com.qm.ep.rebate.domain.dto.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2023/7/4 16:58
 * @version: $
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "流程历史查询")
public class ProcessHistoryQueryDTO {

    @Schema(description = "数据-租户限制ID")
    private String tenantLimitId;
    @Schema(description = "数据-实例ID")
    private String procInstId;
    @Schema(description = "数据-源")
    private String appsource;
    @Schema(description = "数据-返回")
    private Boolean returnNoPreviewNodes;
}
