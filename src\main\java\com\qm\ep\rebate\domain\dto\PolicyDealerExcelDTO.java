package com.qm.ep.rebate.domain.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PolicyDealerExcelDTO {

    @Excel(name = "品牌", orderNum = "1")
    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "经销商代码")
    @Excel(name = "经销商代码", orderNum = "2")
    private String dealerCode;

    @Schema(description = "经销商名称")
    @Excel(name = "经销商名称", orderNum = "3")
    private String dealerName;


}
