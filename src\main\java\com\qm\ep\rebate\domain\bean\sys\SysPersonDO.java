package com.qm.ep.rebate.domain.bean.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYSC030")
@Schema(description = "数据:系统用户表")
public class SysPersonDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-用户id")
    @TableField(value = "personId")
    private String personId;

    @Schema(description = "数据-用户编码")
    @TableField(value = "personCode")
    private String personCode;

    @Schema(description = "数据-用户名称")
    @TableField(value = "personName")
    private String personName;

    @Schema(description = "数据-部门id")
    @TableField(value = "deptId")
    private String deptId;

    @Schema(description = "数据-部门编码")
    @TableField(value = "deptCode")
    private String deptCode;

    @Schema(description = "数据-部门名称")
    @TableField(value = "deptName")
    private String deptName;

    @Schema(description = "数据-机构id")
    @TableField(value = "orgId")
    private String orgId;

    @Schema(description = "数据-机构编码")
    @TableField(value = "orgCode")
    private String orgCode;

    @Schema(description = "数据-机构名称")
    @TableField(value = "orgName")
    private String orgName;

    @Schema(description = "数据-公司id")
    @TableField(value = "companyId")
    private String companyId;

    @Schema(description = "数据-停用标识")
    @TableField(value = "vstop")
    private String vstop;
}
