package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CALFACTORDETAIL")
@Schema(description = "数据:计算因子明细表对象")
public class CalFactorDetailDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-计算因子主表主键")
    @TableField("CALID")
    private String calId;

    @Schema(description = "数据-字段来源表名")
    @TableField("TABLEID")
    private String tableId;

    @Schema(description = "数据-字段名")
    @TableField("FIELDNAME")
    private String fieldName;

    @Schema(description = "数据-别名")
    @TableField("ALIASNAME")
    private String aliasName;

    @Schema(description = "数据-字段类型（输出字段 =》 关联取值对象，汇总字段 =》 汇总对象，取值字段 =》 取值字段")
    @TableField("TYPE")
    private String type;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;
}
