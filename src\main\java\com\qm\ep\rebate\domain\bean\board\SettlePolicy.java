package com.qm.ep.rebate.domain.bean.board;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:结算政策")
@Data
public class SettlePolicy {




    @Schema(description = "唯一键")
    private String uniqueKey;

    /**
     * 实际申请兑付的时间
     */
    @Schema(description = "实际申请兑付的时间")
    private String entryCreateTime;

    @Schema(description = "政策主键")
    private String policyId;

    /**
     * 政策编码
     */
    @Schema(description = "政策编码")
    private String policyCode;

    /**
     * 政策名称
     */
    @Schema(description = "政策名称")
    private String policyName;


    /**
     * 政策创建人
     */
    @Schema(description = "政策创建人")
    private String creator;

    /**
     * 员工工号
     */
    @Schema(description = "员工工号")
    private String employeeId;

    @Schema(description = "申请入学日期")
    private String applyEntryDate;
    @Schema(description = "报名完成日期")
    private String entryFinishDate;

    @Schema(description = "结算时间")
    private String settleTime;
    /**
     * 执行结束时间
     */
    @Schema(description = "执行结束时间")
    private String dEnd;

    /**
     * 结算次数
     */
    @Schema(description = "结算次数")
    private int settleCount;

    @Schema(description = "账户输入方式")
    private String accountEntryMethod;

    @Schema(description = "校准时间")
    private String calTime;
}
