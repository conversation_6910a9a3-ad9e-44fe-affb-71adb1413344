package com.qm.ep.rebate.domain.bean;

import com.qm.ep.rebate.enumerate.DimensionClassEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Schema(description = "数据:实体类-目标维度树 do")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TargetDimensionTreeDO implements Serializable {
    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-指标名称")
    private String targetName;

    @Schema(description = "数据-唯一别名")
    private String uniqueName;

    @Schema(description = "数据-维度分类")
    private DimensionClassEnum dimensionClass;

    @Schema(description = "数据-维度")
    private String dimension;

    @Schema(description = "数据-指标值")
    private String targetValue;

}
