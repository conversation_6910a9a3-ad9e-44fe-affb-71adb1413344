package com.qm.ep.rebate.common.constant;

/**
 * <AUTHOR>
 */
public class ValidationMessageConstants {

    private ValidationMessageConstants() {
        throw new IllegalStateException("检验表单信息常量");
    }

    public static final String FACTOR_NOT_EXISTS = "当前计算因子不存在";
    public static final String FORMULA_NOT_EXISTS = "当前计算公式不存在";


    public static final String REPORT_NOT_EXISTS = "当前报表不存在";
    public static final String REPORT_PLAN_CONDITION_LENGTH_ERROR = "关联方案中条件值长度过长";
    public static final String REPORT_FILTER_CONDITION_LENGTH_ERROR = "筛选条件中条件值长度过长";

}
