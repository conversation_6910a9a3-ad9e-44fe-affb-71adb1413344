package com.qm.ep.rebate.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.BusinessConstructionDO;
import com.qm.ep.rebate.domain.bean.BusinessTableDO;
import com.qm.ep.rebate.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebate.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebate.domain.dto.BusinessConstructionDTO;
import com.qm.ep.rebate.domain.dto.TurnBusinessBottomTableDTO;
import com.qm.ep.rebate.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebate.mapper.ExecTrialCalcHistoryMapper;
import com.qm.ep.rebate.service.BusinessConstructionService;
import com.qm.ep.rebate.service.BusinessTableService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/turnBusinessBottomTable")
@Tag(name = "计算结果转业务底表", description = "[author: 10200571]")
public class ConvertBusinessTableController extends BaseController {
    @Resource
    private BusinessTableService businessTableService;
    @Resource
    private ExecFormalCalcHistoryMapper execFormalCalcHistoryMapper;
    @Resource
    private ExecTrialCalcHistoryMapper execTrialCalcHistoryMapper;
    @Resource
    private BusinessConstructionService businessConstructionService;
    @Operation(summary = "转业务底表和校验", description = "[author: 10200571]")
    @PostMapping("/turnBusinessBottomTable")
    public JsonResultVo<String> turnBusinessBottomTableVerification(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO) throws InterruptedException {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        String tableName=turnBusinessBottomTableDTO.getTableName();
        // 判断结构变化，是否被使用
        businessTableService.getCalcHistory(turnBusinessBottomTableDTO,tableName);
        businessTableService.saveTurnBusinessBottomTable(tableName, turnBusinessBottomTableDTO);
        return jsonResultVo;
    }
    @Operation(summary = "获取字段列名", description = "[author: 10200571]")
    @PostMapping("/getFields")
    public JsonResultVo<List<BusinessConstructionDTO>> getFields(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO) {
        JsonResultVo<List<BusinessConstructionDTO>> jsonResultVo = new JsonResultVo<>();
        AtomicInteger index = new AtomicInteger(1);
        //查询类型,小数点，必填项，联合唯一标识回显
        QmQueryWrapper<BusinessConstructionDO> businessConstructionWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<BusinessConstructionDO> businessConstructionLambdaQueryWrapper = businessConstructionWrapper.lambda();
        businessConstructionLambdaQueryWrapper.eq(BusinessConstructionDO::getTableName, turnBusinessBottomTableDTO.getTableName());
        List<BusinessConstructionDO> businessConstructionList=businessConstructionService.list(businessConstructionLambdaQueryWrapper);
        //查询所有政策可用状态
        QmQueryWrapper<BusinessTableDO> businessTableWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<BusinessTableDO> businessTableLambdaQueryWrapper = businessTableWrapper.lambda();
        businessTableLambdaQueryWrapper.eq(BusinessTableDO::getTableName, turnBusinessBottomTableDTO.getTableName());
        List<BusinessTableDO> businessTableList=businessTableService.list(businessTableLambdaQueryWrapper);
        if(businessTableList.size()>1){
            throw new QmException("business_table表中名称重复");
        }
        if ("formal".equals(turnBusinessBottomTableDTO.getCalculationType())) {
            ExecFormalCalcHistoryDO execFormalCalcHistoryDO = execFormalCalcHistoryMapper.selectById(turnBusinessBottomTableDTO.getId());
            JSONObject jsonObject = JSON.parseObject(execFormalCalcHistoryDO.getSqlStructure());
            List<String> fields = jsonObject.getJSONArray("fields").toJavaList(String.class);
            List<BusinessConstructionDTO> resultList = fields.stream().map(field -> {
                int sort = index.get();
                BusinessConstructionDTO businessConstructionDTO = new BusinessConstructionDTO();
                for(BusinessTableDO businessTableDO:businessTableList){
                    businessConstructionDTO.setOpenState(businessTableDO.getOpenState());
                }
                for(BusinessConstructionDO businessConstructionDO:businessConstructionList){
                    if(Objects.equals(businessConstructionDO.getFieldName(), field)){
                        businessConstructionDTO.setFieldType(businessConstructionDO.getFieldType());
                        businessConstructionDTO.setDecimalPoint(businessConstructionDO.getDecimalPoint());
                        businessConstructionDTO.setRequired(businessConstructionDO.getRequired());
                        businessConstructionDTO.setUniqueKey(businessConstructionDO.getUniqueKey());
                    }
                }
                businessConstructionDTO.setId(IdUtil.simpleUUID());
                businessConstructionDTO.setFieldName(field);
                businessConstructionDTO.setSort(sort++);
                index.set(sort);
                return businessConstructionDTO;
            }).collect(Collectors.toList());
            jsonResultVo.setData(resultList);
        } else if ("trial".equals(turnBusinessBottomTableDTO.getCalculationType())) {
            ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execTrialCalcHistoryMapper.selectById(turnBusinessBottomTableDTO.getId());
            JSONObject jsonObject = JSON.parseObject(execTrialCalcHistoryDO.getSqlStructure());
            List<String> fields = jsonObject.getJSONArray("fields").toJavaList(String.class);
            List<BusinessConstructionDTO> resultList = fields.stream().map(field -> {
                int sort = index.get();
                BusinessConstructionDTO businessConstructionDTO = new BusinessConstructionDTO();
                for(BusinessTableDO businessTableDO:businessTableList){
                    businessConstructionDTO.setOpenState(businessTableDO.getOpenState());
                }
                for(BusinessConstructionDO businessConstructionDO:businessConstructionList){
                    if(Objects.equals(businessConstructionDO.getFieldName(), field)){
                        businessConstructionDTO.setFieldType(businessConstructionDO.getFieldType());
                        businessConstructionDTO.setDecimalPoint(businessConstructionDO.getDecimalPoint());
                        businessConstructionDTO.setRequired(businessConstructionDO.getRequired());
                        businessConstructionDTO.setUniqueKey(businessConstructionDO.getUniqueKey());
                    }
                }
                businessConstructionDTO.setId(IdUtil.simpleUUID());
                businessConstructionDTO.setFieldName(field);
                businessConstructionDTO.setSort(sort++);
                index.set(sort);
                return businessConstructionDTO;
            }).collect(Collectors.toList());
            jsonResultVo.setData(resultList);
        }
        return jsonResultVo;
    }

    @Operation(summary = "转业务底表和校验", description = "[author: 10200571]")
    @PostMapping("/turnBusinessBottomTableBySummary")
    public JsonResultVo<String> turnBusinessBottomTableBySummary(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO) throws InterruptedException {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        String tableName=turnBusinessBottomTableDTO.getTableName();

        //检验底表名称是否存在，如果已经存在提示底表名称已经存在，兑付完成的记录
        LambdaQueryWrapper<BusinessTableDO> tableQuery = new LambdaQueryWrapper<>();
        tableQuery.eq(BusinessTableDO::getTableName, tableName);
        tableQuery.eq(BusinessTableDO::getTableType, "auto");
        List<BusinessTableDO> tableList = businessTableService.list(tableQuery);
        if(CollectionUtils.isNotEmpty(tableList)){
            throw new QmException("底表名称已经存在");
        }

        List<ExecFormalCalcHistoryDO> historyList = execFormalCalcHistoryMapper.selectListByPolicy(turnBusinessBottomTableDTO.getPolicyId(), "53");

        if(CollectionUtils.isNotEmpty(historyList)) {

            // 判断结构变化，是否被使用
            ExecFormalCalcHistoryDO history = historyList.get(0);
            turnBusinessBottomTableDTO.setObjectId(history.getObjectId());
            businessTableService.getCalcHistory(turnBusinessBottomTableDTO,tableName);

            //转底表
            try {
                businessTableService.saveTurnBusinessBottomTableBySummary(tableName, turnBusinessBottomTableDTO);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

        }

        return jsonResultVo;
    }
}
