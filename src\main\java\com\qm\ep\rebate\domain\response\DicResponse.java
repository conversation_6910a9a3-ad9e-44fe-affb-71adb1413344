package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:数据DIC响应")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DicResponse {
    /**
     * 预算类型（0-常规，1-专项）
     */
    @Schema(description = "预算类型（0-常规，1-专项）")
    private List<BaseEnumVO> bgtType;
    /**
     * 预算释放状态(0-不可释放, 1-未释放,2-已释放)
     */
    @Schema(description = "预算释放状态(0-不可释放, 1-未释放,2-已释放)")
    private List<BaseEnumVO> bgtReleaseStatus;
    /**
     * 达成目标类型（00-aak，01-std，02-EBT%）
     */
    @Schema(description = "达成目标类型（00-aak，01-std，02-EBT%）")
    private List<BaseEnumVO> bgtSaleAim;
    /**
     * 调整方式（0-点数，1-金额）
     */
    @Schema(description = "调整方式（0-点数，1-金额）")
    private List<BaseEnumVO> bgtModifyWay;
    /**
     * 变换类型（0-减少，1-增加）
     */
    @Schema(description = "变换类型（0-减少，1-增加）")
    private List<BaseEnumVO> bgtModifyType;
    /**
     * 预算申请类型（0-常规调整，1-专项调整）
     */
    @Schema(description = "预算申请类型（0-常规调整，1-专项调整）")
    private List<BaseEnumVO> bgtApplyType;
    /**
     * 预算申请状态（10-初始、11-提交、12-通过、13驳回）
     */
    @Schema(description = "预算申请状态（10-初始、11-提交、12-通过、13驳回）")
    private List<BaseEnumVO> bgtApplyStatus;

    /**
     * 0-年度，季度-(1,2,3,4)
     */
    @Schema(description = "0-年度，季度-(1,2,3,4)")
    private List<BaseEnumVO> bgtTimeEnum;

}
