package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *  
 * 
 *  
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("plan_quartz")
@Schema(description = "数据实体")
public class PlanQuartzDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策名称")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "数据-计算方案Id")
    @TableField("planId")
    private String planId;


    @Schema(description = "数据-任务id")
    @TableField("jobId")
    private String jobId;

    @Schema(description = "数据-任务名称")
    @TableField("jobName")
    private String jobName;

    @Schema(description = "数据-任务分组")
    @TableField("jobId")
    private String jobGroup;

    @Schema(description = "数据-任务执行器")
    @TableField("className")
    private String className;

    @Schema(description = "数据-JSON结构参数")
    @TableField("jobData")
    private String jobData;



}
