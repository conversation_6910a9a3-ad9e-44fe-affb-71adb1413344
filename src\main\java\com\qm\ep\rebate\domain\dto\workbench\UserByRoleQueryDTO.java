package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-04-10 09:36
 */
@Schema(description = "数据:实体类-用户角色查询 DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserByRoleQueryDTO {

    /**
     * 角色id
     */
    @Schema(description = "数据-角色id")
    private String id;

    /**
     * 角色编码
     */
    @Schema(description = "数据-角色编码")
    private String code;
}
