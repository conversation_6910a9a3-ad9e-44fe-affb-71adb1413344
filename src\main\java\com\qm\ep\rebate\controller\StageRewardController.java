package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.vo.StageRewardVO;
import com.qm.ep.rebate.service.StageRewardService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
  * 阶段奖励
  * <AUTHOR>
  * @version 1.0
  *  date: 2022/9/6 15:11
  */
@Tag(name = "阶段奖励控制器")
@RestController
@RequestMapping("/stageReward")
public class StageRewardController extends BaseController {

    @Resource
    private StageRewardService stageRewardService;

    @Operation(summary = "获取阶段奖励视图", description = "[author: 10200571]")
    @GetMapping("/getView")
    public JsonResultVo<List<StageRewardVO>> getStageRewardView(String dealerCode){
        JsonResultVo<List<StageRewardVO>> result = new JsonResultVo<>();
        result.setData(stageRewardService.queryStageRewardView(dealerCode));
        return result;
    }

}
