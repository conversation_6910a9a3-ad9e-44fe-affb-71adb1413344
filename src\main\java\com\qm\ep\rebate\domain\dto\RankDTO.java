package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "数据:RankDTO对象")
@ToString(callSuper = true)
public class RankDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-政策主键")
    private String policyId;

    @Schema(description = "数据-计算对象主键")
    private String objectId;

    @Schema(description = "数据-计算对象类型")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "数据-因子别名")
    private String alias;

    @Schema(description = "数据-排名字段")
    private String orderField;

    @Schema(description = "数据-分组字段")
    private List<String> groupField;

    @Schema(description = "数据-排序顺序（asc: 正序，desc: 倒序）")
    private String sort;

    @Schema(description = "数据-排序规则（dense: 顺序，sparse: 跳序）")
    private String logic;

    @Schema(description = "数据-备注")
    private String vremark;
}

