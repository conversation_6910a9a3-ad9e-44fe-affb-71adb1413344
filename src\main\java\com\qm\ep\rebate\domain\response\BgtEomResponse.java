package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  
 * EOM年度预算点数与金额表
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Data
@Schema(description = "数据:EOM年度预算点数与金额表")
public class BgtEomResponse implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID")
    private Integer id;

    @Schema(description = "数据-返利项目")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-对应账号（0- 返利，1-代理制保证金折让，2-代理制佣金）")
    private String rebateType;

    @Schema(description = "数据-预算总金额")
    private BigDecimal eomTotalAmount;

}
