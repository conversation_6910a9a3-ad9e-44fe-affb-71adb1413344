package com.qm.ep.rebate.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 商务政策应用级配置类
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@Component
@Data
@RefreshScope
public class RebateConfig {
    /**
     * 服务环境
     */
    @Value("${rebate.config.environment:uat}")
    private String environment;

    /**
     * 平台应用key
     */
    @Value("${rebate.config.ucg.app-key:96109904e3ed4b3d9f30bf4b3abf0c0f}")
    private String appKey;

    /**
     * 平台应用secret
     */
    @Value("${rebate.config.ucg.app-secret:89fcbc8e03214d888af3d9fdcf1585af}")
    private String appSecret;

    /**
     * 网关地址
     */
    @Value("${rebate.config.ucg.host:https://uat-api.faw.cn:30443}")
    private String host;

    /**
     * 角色工作台appKey
     */
    @Value("${rebate.config.workbench.clientId:7c25b67be4784f0e9699c6f2ebbcf00e}")
    private String workbenchClientId;

    @Value("${spring.jwt.qfc-security-secret:qfc-security-secret}")
    private String jwtSecretKey; //jwt

    @Value("${jwt.ep-pub-key.path:jwt/pub.key}")
    private String epPubKeyPath; //ep-jwt

}
