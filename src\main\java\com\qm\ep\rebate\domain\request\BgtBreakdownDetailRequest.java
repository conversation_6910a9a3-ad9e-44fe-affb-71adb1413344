package com.qm.ep.rebate.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *  
 * 预算分解详情-返利项目
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Schema(description = "数据:  预算分解详情-返利项目  ")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BgtBreakdownDetailRequest  {


    @Schema(description = "主键")
    private Integer id;


    @Schema(description = "数据BGT公司故障主要主键")
    private Integer bgtBreakdownMainId;


    @Schema(description = "类项目")
    private String classItem;


    @Schema(description = "类项名称")
    private String classItemName;


    @Schema(description = "销售类型")
    private String saleType;


    @Schema(description = "数据BGT型")
    private String bgtType;


    @Schema(description = "系列")
    private String series;


    @Schema(description = "系列点")
    private BigDecimal seriesPoint;


    @Schema(description = "类项目总分")
    private BigDecimal classitemTotalPoint;


    @Schema(description = "数据classitem used 点")
    private BigDecimal classitemUsedPoint;


    @Schema(description = "数据classitem 点数平衡")
    private BigDecimal classitemPointBalance;


    @Schema(description = "数据classitem 总金额")
    private BigDecimal classitemTotalAmount;


    @Schema(description = "类项目使用量")
    private BigDecimal classitemUsedAmount;


    @Schema(description = "类项目金额余额")
    private BigDecimal classitemAmountBalance;

    @Schema(description = "细分年份")
    private String breakdownYear;

    @Schema(description = "细分季度")
    private String breakdownQuarter;

    @Schema(description = "使用状态")
    private String useStatus;


}
