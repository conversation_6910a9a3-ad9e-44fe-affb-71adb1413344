package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "数据:角色工作台指标类")
public class IndicatorDTO {
    @Schema(description = "数据-指标编码")
    private String keyIndexCode;

    @Schema(description = "数据-灯塔指标id")
    private String lightId;

    @Schema(description = "数据-指标对应的数值")
    private String keyIndexCount;

    @Schema(description = "数据-趋势。1：表示正/绿灯；2：表示负/红灯；3表示无其余情况/黄灯")
    private Integer keyTrend;
}
