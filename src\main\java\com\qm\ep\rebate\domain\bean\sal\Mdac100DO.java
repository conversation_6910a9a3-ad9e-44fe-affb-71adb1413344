package com.qm.ep.rebate.domain.bean.sal;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 *  
 * 经销商基础信息Vdealer
 *
 *
 * <AUTHOR>
 * @since 2020-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "数据:经销商基础信息Vdealer")
public class Mdac100DO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-UUID")
    private String id;

    @Schema(description = "数据-经销商代码")
    private String vdealer;

    @Schema(description = "数据-经销商名称")
    private String vdealername;

    @Schema(description = "数据-经销商简称")
    private String vdealershortname;

    @Schema(description = "数据-曾用名")
    private String vdealeroldname;

    @Schema(description = "数据-经销商类型")
    private String vdealertype;

    @Schema(description = "数据-建档日期")
    private Date dcreate;

    @Schema(description = "数据-组织机构代码证号")
    private String vinstmark;

    @Schema(description = "数据-投资母体/公司实际出资人")
    private String vinvestor;

    @Schema(description = "数据-注册资金")
    private Double nregfin;

    @Schema(description = "数据-法人代表")
    private String vcorporation;

    @Schema(description = "数据-总经理")
    private String vmanager;

    @Schema(description = "数据-总经理移动电话")
    private String vmobile;

    @Schema(description = "数据-所有制代码")
    private String vownership;

    @Schema(description = "数据-经销商级别")
    private String vdealerlevel;

    @Schema(description = "数据-公司ID")
    private String ncompanyid;

    @Schema(description = "数据-停用标识")
    private String vstop;

    @Schema(description = "数据-停用日期")
    private Date dstop;

    @Schema(description = "数据-停用原因")
    private String vstopreason;

    @Schema(description = "数据-备注")
    private String vmemo;

    @Schema(description = "数据-整车销售组织")
    private String norgan;

    @Schema(description = "数据-服务组织")
    private String nsvcorg;

    @Schema(description = "数据-备件组织")
    private String nspaorg;

    @Schema(description = "数据-地址")
    private String vaddr;

    @Schema(description = "数据-联系电话")
    private String vtel;

    @Schema(description = "数据-传真")
    private String vfax;

    @Schema(description = "数据-EMAIL")
    private String vemail;

    @Schema(description = "数据-经销商属性，是实际经销商还是预留经销商")
    private String vattr;

    @Schema(description = "数据-时间戳")
    private Timestamp dtstamp;

    @Schema(description = "数据-国家")
    private String ncountryid;

    @Schema(description = "数据-省区")
    private String nprovince;

    @Schema(description = "数据-市")
    private String ncity;

    @Schema(description = "数据-县")
    private String ncountyid;

    @Schema(description = "数据-02是厂内 01是厂外  数据字典项LOCALORFOREIGN")
    private String vloca;

    @Schema(description = "数据-成立日期")
    private Date destablish;

    @Schema(description = "数据-联系人")
    private String vlinkman;

    @Schema(description = "数据-启用标识 0 不启用，1 启用")
    private String vstartflag;

    @Schema(description = "数据-启用日期")
    private Date dstartdate;

    @Schema(description = "数据-对经销商档案中经销商特殊标识为真的经销商，省区可以修改")
    private String vmodifyprovince;

    @Schema(description = "数据-经营范围(手输)-红旗增加")
    private String vcopeofbusiness;

    @Schema(description = "数据-建店类别(数据字典BUILDCATEGORY:01新建、02改建)-红旗增加")
    private String vbuildcategory;

    @Schema(description = "数据-建店标准(数据字典BUILDSTANDARD:01/标准店、02/非标准店)-红旗增加")
    private String vbuildstandard;

    @Schema(description = "数据-维修资质(数据字典：REPAIRQUALIFICATION)-红旗增加")
    private String vrepairqualification;

    @Schema(description = "数据-经营类型(数据字典BUSINESSTYPEOFDLR：01/4S、02/销售服务店、03/城市展厅店、04/服务店、05/过渡销售店、06/过渡服务店)-红旗增加")
    private String vbusinesstype;

    @Schema(description = "数据-开通店端 0 不启用，1 启用")
    private String vnewdms;

    @Schema(description = "数据-对应经销商id，用于不分公司查询时使用(51公司占50公司计划资源)")
    private String nrelatedealerid;

    @Schema(description = "数据-是否代理商，0经销商，1代理商")
    private String isAgent;

    @Schema(description = "大区")
    private String vinsttext2;

}
