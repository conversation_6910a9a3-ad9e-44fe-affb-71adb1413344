package com.qm.ep.rebate.controller;


import com.qm.ep.rebate.infrastructure.util.RedisUtil;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;


@RestController
@Tag(name = "redis操作")
@RequestMapping("/redis")
@Slf4j
public class RedisController {
    @Autowired
    private RedisUtil redisUtil;


    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @GetMapping ("/getKey")
    public JsonResultVo<String> getKey(String key) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setData(redisUtil.get(key));
        return ret;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @GetMapping ("/getKeys")
    public JsonResultVo<Set<String>> getKeys(String pattern) {
        JsonResultVo<Set<String>> ret = new JsonResultVo<>();
        ret.setData(redisUtil.keys(pattern));
        return ret;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @GetMapping ("/deleteKey")
    public JsonResultVo<String> deleteKey(String key) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        redisUtil.del(key);
        ret.setData("ok");
        return ret;
    }



}
