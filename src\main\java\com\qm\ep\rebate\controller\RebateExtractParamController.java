package com.qm.ep.rebate.controller;


import com.alibaba.nacos.common.utils.CollectionUtils;
import com.qm.ep.rebate.domain.bean.RebateExtractParamPO;
import com.qm.ep.rebate.domain.request.*;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.ep.rebate.service.RebateExtractCacheService;
import com.qm.ep.rebate.service.RebateExtractParamService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 *  
 * 返利折让申请单与处理单合并表 前端控制器
 *  
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@RestController
@RequestMapping("/rebateExtractParam")
@Tag(name = "返利折让参数Controller")
@Slf4j
public class RebateExtractParamController {

    @Resource
    private RebateExtractParamService paramService;

    @Resource
    RebateExtractCacheService rebateExtractCacheService;


    @Operation(summary = "设置返利折让参数-列表", description = "[author: 10200571]")
    @PostMapping("/getParamList")
    public JsonResultVo<List<RebateExtractParamPO>> getParamList() {
        JsonResultVo<List<RebateExtractParamPO>> result = new JsonResultVo<>();
        result.setData(paramService.list());
        return result;
    }

    @Operation(summary = "设置返利折让参数-保存", description = "[author: 10200571]")
    @PostMapping("/saveParam")
    public JsonResultVo<List<RebateExtractParamPO>> saveParam(@Valid @RequestBody RebateExtractParamSaveRequest request) {
        JsonResultVo<List<RebateExtractParamPO>> result = new JsonResultVo<>();
        List<RebateExtractParamPO> params = request.getParams();
        if(CollectionUtils.isNotEmpty(params)) {
            LoginKeyDO loginDO = BootAppUtil.getLoginKey();
            params.forEach(p -> {
                if("P02".equals(p.getParamName())) {
                    p.setParamValue(p.getParamValue() + " 00:00:00");
                }
                if("P03".equals(p.getParamName())) {
                    p.setParamValue(p.getParamValue() + " 23:59:59");
                }
                p.setChangeUser(loginDO.getOperatorName());
                p.setChangeTime(DateUtil.nowString());
                p.setDtstamp(LocalDateTime.now());
            });
        }
        paramService.saveOrUpdateBatch(params);
        result.setData(paramService.list());

        //  开启返利折让，缓存所有经销商上个月和本月的STD目标和STD达成数和单车批发指导价
        rebateExtractCacheService.cacheDataAsync();

        return result;
    }

    @Operation(summary = "设置返利折让参数-钉钉发送", description = "[author: 10200571]")
    @GetMapping("/sendDing")
    public JsonResultVo<String> sendDing() {
        JsonResultVo<String> result = new JsonResultVo<>();
        paramService.sendDing();
        result.setData("钉钉发送成功");
        return result;
    }

}
