package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 车系销量预测表
 *
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
@Schema(description = "数据:实体类-  车系销量预测表  ")
@Getter
@Setter
@TableName("bgt_series_sale_amount")
public class BgtSeriesSaleAmountPO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 年度
     */
    @Schema(description = "数据-年度")
    @TableField("sale_year")
    private String saleYear;

    /**
     * 月度
     */
    @Schema(description = "数据-月度")
    @TableField("sale_month")
    private String saleMonth;

    /**
     * 车系
     */
    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    /**
     * 平均指导价
     */
    @Schema(description = "数据-平均指导价")
    @TableField("average_price")
    private BigDecimal averagePrice;

    /**
     * 销售类型（00-aak，01-std, 99-平均指导价手动录入）
     */
    @Schema(description = "数据-销售类型（00-aak，01-std, 99-平均指导价手动录入）")
    @TableField("sale_type")
    private String saleType;

    /**
     * 版本号
     */
    @Schema(description = "数据-版本号")
    @TableField("version")
    private String version;

    /**
     * 使用状态（0-未使用，1-正在使用）
     */
    @Schema(description = "数据-使用状态（0-未使用，1-正在使用）")
    @TableField("use_status")
    private String useStatus;

    /**
     * 创建者
     */
    @Schema(description = "数据-创建者")
    @TableField("CREATEBY")
    private String createby;

    /**
     * 创建日期
     */
    @Schema(description = "数据-创建日期")
    @TableField("CREATEON")
    private Date createon;

    /**
     * 更新者
     */
    @Schema(description = "数据-更新者")
    @TableField("UPDATEBY")
    private String updateby;

    /**
     * 更新时间
     */
    @Schema(description = "数据-更新时间")
    @TableField("UPDATEON")
    private Date updateon;

    /**
     * 时间戳
     */
    @Schema(description = "数据-时间戳")
    @TableField("DTSTAMP")
    private Date dtstamp;


}
