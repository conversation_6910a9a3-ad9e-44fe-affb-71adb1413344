package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.domain.bean.PolicyPublishedExcelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:实体类-策略发布 Excel DTO")
@Data
public class PolicyPublishedExcelDTO {
    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-列表")
    private List<PolicyPublishedExcelDO> list;
}
