package com.qm.ep.rebate.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * 工作台人员效能配置
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@Configuration
@RefreshScope
@Data
public class WorkbenchPerformanceConfig {

    @Value("${performance.publish.indicatorCode:BI-000032}")
    private String publishIndicatorCode;

    @Value("${performance.publish.indicatorName:政策发布逾期}")
    private String publishIndicatorName;

    @Value("${performance.pay.indicatorCode:BI-000032}")
    private String payIndicatorCode;

    @Value("${performance.pay.indicatorName:政策兑付逾期}")
    private String payIndicatorName;

    @Value("${performance.settle.indicatorCode:BI-000032}")
    private String settleIndicatorCode;

    @Value("${performance.settle.indicatorName:政策结算周期}")
    private String settleIndicatorName;

    @Value("${performance.acquire.indicatorCode:BI-000032}")
    private String acquireIndicatorCode;

    @Value("${performance.acquire.indicatorName:政策获取率}")
    private String acquireIndicatorName;

    @Value("${performance.dlr.indicatorCode:BI-000034}")
    private String dlrCode;

    @Value("${performance.dlr.indicatorName:DLR产品使用率}")
    private String dlrName;

    @Value("${performance.departmentCode:CSP}")
    private String departmentCode;

    @Value("${performance.test:false}")
    private boolean test;

    @Value("${performance.testMonth:false}")
    private boolean testMonth;

    @Value("${performance.isSendTrailAim:false}")
    private boolean isSendTrailAim;


}
