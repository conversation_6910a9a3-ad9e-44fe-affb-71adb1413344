package com.qm.ep.rebate.common.rabbitmq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.core.ReturnedMessage;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CallBackSenderServer implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnsCallback{

    @Resource(name="backlogRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    /**
     * 消息确认监听器ConfirmCallback
     * @param correlationData
     * @param ack
     * @param s
     */
    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String s) {
        System.out.println("correlationData: " + correlationData);
        System.out.println("ack: " + ack);//ack 发送成功
        if (!ack) {
            System.out.println("异常处理....");
        }
    }


    /**
     * fanout方式发送
     */
    public void sendInfoByFanout(String context, String exchange, String routingKey){
        CorrelationData correlationData = new CorrelationData();
        correlationData.setId(UUID.randomUUID().toString());
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setContentType("application/json");
        Message message = new Message(context.getBytes(), messageProperties);
        rabbitTemplate.setConfirmCallback(this);
        this.rabbitTemplate.convertAndSend(exchange, routingKey, message, correlationData);
    }

    @Override
    public void returnedMessage(ReturnedMessage returnedMessage) {
        log.error("消息未到达队列，exchange:{}, routingKey:{}, replyCode:{}, replyText:{}", returnedMessage.getExchange(), returnedMessage.getRoutingKey(), returnedMessage.getReplyCode(), returnedMessage.getReplyText());
    }
}
