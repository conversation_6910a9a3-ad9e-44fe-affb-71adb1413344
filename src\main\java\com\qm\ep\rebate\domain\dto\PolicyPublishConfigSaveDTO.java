package com.qm.ep.rebate.domain.dto;


import com.qm.ep.rebate.domain.bean.PolicyPublishConfigDO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 *  
 * 发布配置信息
 *  
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data
@Schema(description = "数据:发布配置信息，主要记录经销商")
public class PolicyPublishConfigSaveDTO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-政策ID")
    private String policyId;

    @Schema(description = "数据-参与试算标识 1是，0否")
    private Integer canTrial;

    @Schema(description = "数据-list")
    List<PolicyPublishConfigDO> list;

    @NotBlank(message = "试算类型不能为空")
    @Schema(description = "试算器-试算类型:aak-AAK,std-STD，invoice-销售发票")
    private String trailType;
}
