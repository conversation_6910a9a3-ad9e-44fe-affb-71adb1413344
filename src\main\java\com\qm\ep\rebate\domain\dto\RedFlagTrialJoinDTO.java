package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.enumerate.JoinTypeEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "数据:RedFlagTrialJoinDTO对象")
@ToString(callSuper = true)
public class RedFlagTrialJoinDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-政策主键")
    private String policyId;

    @Schema(description = "数据-计算因子主键")
    private String factorId;

    @Schema(description = "数据-关联底表名称")
    private String tableName;

    @Schema(description = "数据-关联关系")
    private JoinTypeEnum joinType;

    @Schema(description = "数据-备注")
    private String vremark;

    @Schema(description = "数据-关联条件")
    private List<RedFlagTrialJoinOnDTO> conditions;

}
