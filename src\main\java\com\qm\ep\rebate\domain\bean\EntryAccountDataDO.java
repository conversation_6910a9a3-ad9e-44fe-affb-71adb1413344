package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 *
 * 
 *  
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("entry_account_data")
@Schema(description = "数据实体")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EntryAccountDataDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id",  type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-每次入账唯一主键（正式计算历史表ID）")
    @TableField("uniqueKey")
    private String uniqueKey;

    @Schema(description = "数据-返利项目代码，取自销售模块维护返利项目程序")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-入账方式代码，取自字典项：RBTPOLICYACCT")
    @TableField("auditType")
    private String auditType;

    @Schema(description = "数据-每条数据唯一代码（正式计算历史表ID + 正式计算结果表ID）")
    @TableField("billNo")
    private String billNo;

    @Schema(description = "数据-系列")
    @TableField("series")
    private String series;

    @Schema(description = "数据-经销商代码")
    @TableField("dealerCode")
    private String dealerCode;

    @Schema(description = "数据-经销商名称")
    @TableField("dealerName")
    private String dealerName;

    @Schema(description = "数据-返利金额")
    @TableField("rebateAmount")
    private String rebateAmount;

    @Schema(description = "数据-备注（商务政策名称 + 经销商备注（如有） + VIN码）")
    @TableField("remark")
    private String remark;

    @Schema(description = "数据-操作员ID")
    @TableField("operatorId")
    private String operatorId;

    @Schema(description = "数据-状态码")
    @TableField("stateCode")
    private String stateCode;

    @Schema(description = "数据-政策主键")
    @TableField("policyId")

    private String policyId;

    @Schema(description = "数据-政策编码")
    @TableField("policyCode")
    private String policyCode;

    @Schema(description = "数据-政策名臣")
    @TableField("policyName")
    private String policyName;

    @Schema(description = "数据-任务实例编码")
    @TableField("taskinsanceCode")
    private String taskinsanceCode;

    @Schema(description = "数据-任务流实例编码")
    @TableField("taskFlowinstanceCode")
    private String taskFlowinstanceCode;

    @Schema(description = "数据-审批流主键")
    @TableField("processInstanceId")
    private String processInstanceId;

    @Schema(description = "数据-创建者")
    @TableField("CREATEBY")
    private String createby;

    @Schema(description = "数据-创建日期")
    @TableField("CREATEON")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createon;

    @Schema(description = "数据-更新者")
    @TableField("UPDATEBY")
    private String updateby;

    @Schema(description = "数据-更新日期")
    @TableField("UPDATEON")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateon;

    @Schema(description = "数据-提交人")
    @TableField("submitBy")
    private String submitBy;

    @Schema(description = "数据-提交人代码")
    @TableField("submitCode")
    private String submitCode;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-预算是否释放（0-不释放，1-释放）")
    @TableField(exist = false)
    private String bgtRelease;

    @Schema(description = "数据-不含税金额")
    @TableField("notax_amount")
    private String notaxAmount;

    @Schema(description = "数据-税率")
    @TableField("tax_rate")
    private String taxRate;

    @Schema(description = "数据-vin码")
    @TableField("vin")
    private String vin;

    @Schema(description = "数据-计算结果id")
    @TableField(exist = false)
    private String resultId;

    @Schema(description = "冗余字段1")
    @TableField("field1")
    private String field1;

    @Schema(description = "返利金额汇总")
    @TableField(exist = false)
    private String totalRebateAmount;

    @Schema(description = "因子名称")
    @TableField(exist = false)
    private String factorName;


}
