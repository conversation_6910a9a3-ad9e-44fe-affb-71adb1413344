package com.qm.ep.rebate.domain.bean.approve;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @className: TaskInstance
 * @description: TODO 类描述 
 * @author: sunrenlong
 * @date: 2023/07/21 13:11
 **/

@Schema(description = "任务实例")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "task_instance")
public class TaskInstanceDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    /**
     * 审批模板code
     */
    @TableField(value = "task_template_code")
    @Schema(description = "审批模板code")
    private String taskTemplateCode;

    /**
     * 审批模板版本
     */
    @TableField(value = "task_template_version_code")
    @Schema(description = "审批模板版本")
    private String taskTemplateVersionCode;

    /**
     * 审批流实例
     */
    @TableField(value = "task_flow_instance_code")
    @Schema(description = "审批流实例")
    private String taskFlowInstanceCode;

    /**
     * 审批实例
     */
    @TableField(value = "task_instance_code")
    @Schema(description = "审批实例")
    private String taskInstanceCode;

    /**
     * 业务id
     */
    @TableField(value = "biz_id")
    @Schema(description = "业务id")
    private String bizId;

    @TableField(value = "overdue_time")
    @Schema(description = "数据")
    private Date overdueTime;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description = "描述")
    private String description;

    @TableField(value = "distribute_user_name")
    @Schema(description = "数据")
    private String distributeUserName;

    @TableField(value = "distribute_user_code")
    @Schema(description = "数据")
    private String distributeUserCode;

    @TableField(value = "sender_user_name")
    @Schema(description = "数据")
    private String senderUserName;

    @TableField(value = "sender_user_code")
    @Schema(description = "数据")
    private String senderUserCode;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    @Schema(description = "完成时间")
    private Date completeTime;

    @TableField(value = "create_time",fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "数据")
    private Date createTime;

    /**
     * 逻辑删除
     */
    @TableField(value = "del_flag")
    @Schema(description = "逻辑删除")
    private Integer delFlag;

    @TableField(value = "team_code")
    @Schema(description = "数据")
    private String teamCode;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Schema(description = "备注")
    private String remark;

    @TableField(value = "custom_url")
    @Schema(description = "数据")
    private String customUrl;

    @TableField(value = "custom_url_params")
    @Schema(description = "数据")
    private String customUrlParams;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 审批状态
     */
    @TableField(value = "approve_status")
    @Schema(description = "审批状态")
    private Integer approveStatus;

    /**
     * 审批时间
     */
    @TableField(value = "approve_time")
    @Schema(description = "审批时间")
    private Date approveTime;

    /**
     * 驳回原因
     */
    @TableField(value = "reject_reason")
    @Schema(description = "驳回原因")
    private String rejectReason;

    /**
     * 流程id
     */
    @TableField(value = "process_instance_id")
    @Schema(description = "流程id")
    private String processInstanceId;

    /**
     * 强制关闭
     */
    @TableField(value = "is_forced_closed")
    @Schema(description = "强制关闭")
    private String isForcedClosed;
}