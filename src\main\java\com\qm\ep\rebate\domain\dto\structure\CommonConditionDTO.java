package com.qm.ep.rebate.domain.dto.structure;

import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-常见病 DTO")
@Data
public class CommonConditionDTO {
    @Schema(description = "数据-策略 ID")
    private String policyId;
    @Schema(description = "数据-Calc 对象类型")
    private CalcObjectTypeEnum calcObjectType;
    @Schema(description = "数据-字段名称")
    private String fieldName;
    @Schema(description = "数据-字段名称来自")
    private String fieldNameFrom;
    @Schema(description = "数据-字段名称源")
    private String fieldNameSource;
    @Schema(description = "数据-Cal 型")
    private String calType;
    @Schema(description = "数据-骗局类型")
    private String conType;
    @Schema(description = "数据-滤液类型")
    private String filtrateType;
    @Schema(description = "数据-滤液")
    private String filtrate;
    @Schema(description = "数据-滤液来自")
    private String filtrateFrom;
    @Schema(description = "数据-滤液来源")
    private String filtrateSource;
    @Schema(description = "数据-括号前")
    private String bracketBefore;
    @Schema(description = "数据-括号后")
    private String bracketAfter;
    @Schema(description = "数据-排")
    private Integer rank;
    // tableName是否需要拼接Source字段
    @Schema(description = "数据-tableName是否需要拼接Source字段")
    private Boolean tableNameNeedConcatSource;

    @Schema(description = "数据-额外")
    private Map<String, Object> extra;
}