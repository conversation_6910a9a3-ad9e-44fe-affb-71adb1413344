package com.qm.ep.rebate.domain.dto.sal;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "代理商经销商输出对象")
public class Mdac100OutDTO {

    @Schema(description = "数据-UUID")
    private String id;

    @Schema(description = "数据-经销商代码")
    private String vdealer;

    @Schema(description = "数据-经销商名称")
    private String vdealername;

    @Schema(description = "数据-经销商简称")
    private String vdealershortname;

    @Schema(description = "数据-曾用名")
    private String vdealeroldname;

    @Schema(description = "数据-经销商类型")
    private String vdealertype;

    @Schema(description = "数据-是否代理商，0经销商，1代理商")
    private String isAgent;

}
