package com.qm.ep.rebate.domain.dto.sal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;


/**
 *
 * 查询收款明细
 *
 *
 * <AUTHOR>
 * @since 2021-03-04
 */
@Schema(description = "数据:查询收款明细")
@Data
public class SearchPaymentDetailDTO extends JsonParamDto {

    @Schema(description = "数据-系统单号 编号规则 CREDITNO")
    private String vsysbillno;

    @Schema(description = "数据-入账票据号")
    private String vbillno;

    @Schema(description = "数据-公司ID")
    private String ncompanyid;

    @Schema(description = "数据-单位标识：1客户/2经销商")
    private String vunitflag;

    @Schema(description = "数据-客户ID")
    private String ncustomerid;

    @Schema(description = "数据-信用账户")
    private String vcreditacct;

    @Schema(description = "数据-账户类别")
    private String vacctclass;

    @Schema(description = "数据-账户类型")
    private String vaccttype;

    @Schema(description = "数据-收款方式")
    private String vgatheringmode;

    @Schema(description = "数据-返利项目")
    private String vclassitem;


    @Schema(description = "数据-金额")
    private Double nbalance;


    @Schema(description = "数据-操作员ID")
    private String noperatorid;

    @Schema(description = "数据-操作日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date doperatedate;


    @Schema(description = "数据-确认标识")
    private String visconfirm;

    @Schema(description = "数据-入账日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dpostingdate;

    @Schema(description = "数据-备注")
    private String vremark;

    @Schema(description = "数据-银行")
    private String vbank;

    @Schema(description = "数据-银行账号")
    private String vmarkforbank;

    @Schema(description = "数据-银行流水号")
    private String vbanklsh;

    @Schema(description = "数据-融资申请单编号 salc107c")
    private String vfinanceno;

    @Schema(description = "数据-单位ID")
    private String customer;

    @Schema(description = "数据-信用账户")
    private String[] credit;

    @Schema(description = "数据-组织机构")
    private String norgan;

    @Schema(description = "数据-起始日期")
    private String begin;

    @Schema(description = "数据-截止日期")
    private String end;

    @Schema(description = "数据-系列")
    private String vpdtseries;

    @Schema(description = "数据-经销商id")
    private String ndealerid;

}
