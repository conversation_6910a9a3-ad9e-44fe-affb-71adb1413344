package com.qm.ep.rebate.domain.bean;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 *
 *
 * <AUTHOR>
 * @since 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "数据:维护政策信息导入excel导入")
public class PolicyPublishedExcelDO implements Serializable {
    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-IDs_policy_published")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-公司Id")
    @TableField(value = "NCOMPANYID")
    private String ncompanyid;


    @Excel(name = "部门", orderNum = "1")
    @Schema(description = "数据-部门")
    @TableField(value = "DEPARTMENT")
    private String department;


    @Excel(name = "政策名称", orderNum = "2")
    @Schema(description = "数据-政策名称")
    @TableField(value = "VPOLICYNAME")
    private String vpolicyname;

    @Excel(name = "政策编号", orderNum = "3")
    @Schema(description = "数据-政策编号")
    @TableField("VPOLICYCODE")
    private String vpolicycode;

    @Excel(name = "发布日期", orderNum = "4")
    @Schema(description = "数据-发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    @TableField(value = "PUBLISHDATE")
    private Date publishdate;

    @Excel(name = "政策经办人", orderNum = "5")
    @Schema(description = "数据-政策经办人")
    @TableField(value = "AGENT")
    private String agent;

    @Excel(name = "是否调整", orderNum = "6")
    @Schema(description = "数据-是否调整")
    @TableField(value = "ISADJUST")
    private String isadjust;

    @Excel(name = "调整后政策编号", orderNum = "7")
    @Schema(description = "数据-调整后政策编号")
    @TableField(value = "ADJUSTVPOLICYCODE", exist = false)
    private String adjustvpolicycode;

    @Schema(description = "数据-失败原因")
    @TableField(value = "reseaon", exist = false)
    private String reseaon;

    @Excel(name = "状态", orderNum = "8")
    @Schema(description = "数据-状态")
    @TableField("STAGE")
    private String stage;

    @Excel(name = "政策周期", orderNum = "9")
    @Schema(description = "数据-政策周期")
    @TableField("POLICYCYCLE")
    private String policycycle;

    @Excel(name = "备注", orderNum = "10")
    @Schema(description = "数据-备注")
    @TableField(value = "REMARK")
    private String remark;

    @Excel(name = "停用标识", orderNum = "11")
    @Schema(description = "数据-停用标识")
    @TableField(value = "STOP")
    private String stop;


    @Excel(name = "执行开始时间", orderNum = "12")
    @Schema(description = "执行开始时间")
    @TableField(value = "dbegin")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @Excel(name = "执行结束时间", orderNum = "13")
    @Schema(description = "执行开始时间")
    @TableField(value = "dend")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

}
