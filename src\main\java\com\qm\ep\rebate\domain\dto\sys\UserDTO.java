package com.qm.ep.rebate.domain.dto.sys;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:UserDTO")
@ToString(callSuper = true)
public class UserDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-用户ID")
    private String userId;
    @Schema(description = "数据-用户代码")
    private String userCode;
    @Schema(description = "数据-用户名称")
    private String userName;

    @Schema(description = "部门id")
    @NotBlank(message = "部门id不能为空")
    private String deptId;


}