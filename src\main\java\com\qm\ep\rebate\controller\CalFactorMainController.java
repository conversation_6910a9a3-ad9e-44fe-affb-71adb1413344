package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.domain.bean.CalculateFactorsMainDO;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.dto.*;
import com.qm.ep.rebate.domain.vo.CalFactorVO;
import com.qm.ep.rebate.enumerate.YesOrNoEnum;
import com.qm.ep.rebate.mapper.CalculateFactorsMainMapper;
import com.qm.ep.rebate.remote.feign.RebateCalcFeignClient;
import com.qm.ep.rebate.remote.feign.RebateDataFeignClient;
import com.qm.ep.rebate.service.*;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.ep.rebate.infrastructure.util.RegexUtils;
import com.qm.ep.rebate.service.impl.PolicyFlagServiceImpl;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/calFactorMain")
@Tag(name = "计算因子", description = "[author: 10200571]")
public class CalFactorMainController extends BaseController {

    @Autowired
    private CalFactorMainService calFactorService;
    @Autowired
    private CalculateFactorsMainService calculateFactorsMainService;
    @Autowired
    private PolicyService policyService;
    @Resource
    private SystemConfigService systemConfigService;
    @Autowired
    private SysPersonOrgService sysPersonOrgService;

    @Autowired
    private CalculateFactorsMainMapper calculateFactorsMainMapper;

    @Resource
    private RebateCalcFeignClient rebateCalcFeignClient;
    @Autowired
    private RebateDataFeignClient rebateDataFeignClient;
    @Autowired
    private PolicyFlagService policyFlagService;

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<CalculateFactorsMainDO> deleteById(@RequestBody CalculateFactorsMainDO tempDO) {
        JsonResultVo<CalculateFactorsMainDO> resultObj = new JsonResultVo<>();
        CalculateFactorsMainDO paramsDO = calculateFactorsMainService.getById(tempDO.getId());
        // 传入参数，objectid和policyId判断历史表中，是否在转换中（状态为3），转换中不可删除
        Map<String, Object> map = new HashMap<>();
        map.put("policyId", paramsDO.getPolicyid());
        map.put("objectId", tempDO.getId());
        JsonResultVo<Object> ret = rebateDataFeignClient.selectHistory(map);
        if (ret.isOk()) {
            Map<String, Object> data = (Map<String, Object>) ret.getData();
            boolean isOK = Boolean.parseBoolean(data.get("isOk").toString());
            if (!isOK) {
                resultObj.setMsgErr(data.get("msg").toString());
                return resultObj;
            }
        } else {
            resultObj.setMsgErr(ret.getMsg());
            return resultObj;
        }
        boolean flag = calFactorService.deleteMain(tempDO.getId());
        if (flag) {
            // 删除关联内容calfactordetail, calfactorrank, calfactorjoin, calfactorjoinon, calfactorconditions, businessconstruction2 -> 用计算因子名关联
            calculateFactorsMainMapper.deleteDetail(tempDO.getId());
            calculateFactorsMainMapper.deleteRank(tempDO.getId());
            calculateFactorsMainMapper.deleteJoin(tempDO.getId());
            calculateFactorsMainMapper.deleteJoinOn(tempDO.getId());
            calculateFactorsMainMapper.deleteConditions(tempDO.getId());
            calculateFactorsMainMapper.deleteBusiness(paramsDO.getPolicyid(), paramsDO.getFactorname());
            // 删除相关试算历史
            List<String> calIds = new ArrayList<>();
            calIds.add(tempDO.getId());
            rebateCalcFeignClient.deleteByCalcObjectIds(calIds);
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }

    @Operation(summary = "查询计算因子详情", description = "[author: 10200571]")
    @PostMapping("/info")
    public JsonResultVo<CalFactorVO> info(@RequestBody CalFactorDTO calFactorDTO) {
        JsonResultVo<CalFactorVO> jsonResultVo = new JsonResultVo<>();
        // 校验主键
        if (BootAppUtil.isNullOrEmpty(calFactorDTO.getId())) {
            jsonResultVo.setMsgErr("查询失败");
            return jsonResultVo;
        }
        jsonResultVo.setData(calFactorService.info(calFactorDTO.getId()));
        return jsonResultVo;
    }

    /**
     * 计算因子 主表查询
     */
    @Operation(summary = "计算因子 主表查询", description = "[author: 10200571]")
    @PostMapping("/getCalFactorsMain")
    @DS(DataSourceType.W)
    public JsonResultVo<QmPage<CalculateFactorsMainDO>> getCalFactorsMain(@RequestBody CalculateFactorsMainDTO tempDTO) {
        // 定义查询构造器
        QmQueryWrapper<CalculateFactorsMainDO> queryWrapper = new QmQueryWrapper<>();
        // 拼装实体属性查询条件
        LambdaQueryWrapper<CalculateFactorsMainDO> lambdaWrapper = queryWrapper.lambda();
        // 查询数据，使用table函数。
        lambdaWrapper.eq(CalculateFactorsMainDO::getPolicyid, tempDTO.getPolicyid());
        lambdaWrapper.orderByAsc(CalculateFactorsMainDO::getMainTable);
        QmPage<CalculateFactorsMainDO> list = calculateFactorsMainService.table(queryWrapper, tempDTO);
        // 拼装数据是否可编辑
        List<CalculateFactorsMainDO> items = list.getItems();
        items = this.mainTableSort(items);
        //items按照mainTable排序
        items = items.stream().sorted(Comparator.comparing(CalculateFactorsMainDO::getMainTable).thenComparing(CalculateFactorsMainDO::getFactorname)).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(items)) {
            Map<String, String> editMap = policyFlagService.getFactorEditFlagMap(tempDTO.getPolicyid(), items);
            items.forEach(item -> item.setIsEdit(editMap.get(item.getId())));
        }
        JsonResultVo<QmPage<CalculateFactorsMainDO>> ret = new JsonResultVo<>();
        list.setItems(items);
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询计算因子模板列表", description = "[author: 10200571]")
    @PostMapping("/getFactorTemplate")
    public JsonResultVo<QmPage<CalculateFactorsMainDO>> getFactorTemplate(@RequestBody CalculateFactorsMainDTO tempDTO) {
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);
        LoginKeyDO userInfo = getUserInfo();

        QmQueryWrapper<PolicyDO> policyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyDO> lambda = policyWrapper.lambda();
        lambda.eq(PolicyDO::getIsTemplate, YesOrNoEnum.YES)
                .like(StringUtils.isNotEmpty(tempDTO.getPolicyName()), PolicyDO::getVpolicyname, tempDTO.getPolicyName());
        if ("bx".equals(company)) {
            List<String> createByList = sysPersonOrgService.queryPersonIdAtRelatedLevel(userInfo.getOperatorId());
            lambda.in(PolicyDO::getCreateBy, createByList);
        }
        List<PolicyDO> policyDOList = policyService.list(policyWrapper);

        JsonResultVo<QmPage<CalculateFactorsMainDO>> ret = new JsonResultVo<>();
        if (CollUtil.isEmpty(policyDOList)) {
            QmPage<CalculateFactorsMainDO> page = new QmPage<>();
            ret.setData(page);
        } else {
            List<String> policyIdList = policyDOList.stream().map(PolicyDO::getId).collect(Collectors.toList());
            QmQueryWrapper<CalculateFactorsMainDO> queryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<CalculateFactorsMainDO> lambdaWrapper = queryWrapper.lambda();
            lambdaWrapper.in(CalculateFactorsMainDO::getPolicyid, policyIdList)
                    .like(StringUtils.isNotEmpty(tempDTO.getFactorname()), CalculateFactorsMainDO::getFactorname, tempDTO.getFactorname());
            QmPage<CalculateFactorsMainDO> page = calculateFactorsMainService.table(queryWrapper, tempDTO);
            ret.setData(page);
        }
        return ret;
    }

    @Operation(summary = "保存计算因子", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<String> save(@RequestBody CalFactorDTO calFactorDTO) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        String msg = validateCalFactorDTO(calFactorDTO);
        if (BootAppUtil.isnotNullOrEmpty(msg)) {
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String factorId = calFactorService.saveCalFactorMain(calFactorDTO, getUserInfo(), "save");
        jsonResultVo.setData(factorId);
        return jsonResultVo;
    }

    @Operation(summary = "计算因子复制", description = "[author: 10200571]")
    @PostMapping("/copy")
    public JsonResultVo<String> copy(@RequestBody CalFactorDTO calFactorDTO) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        CalFactorVO info = calFactorService.info(calFactorDTO.getId());
        info.setFactorName(calFactorDTO.getFactorName());
        String msg = validateCalFactorDTO(info.convert2DTO());
        if (BootAppUtil.isnotNullOrEmpty(msg)) {
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String factorId = calFactorService.saveCalFactorMain(info.convert2DTO(), getUserInfo(), "copy");
        jsonResultVo.setData(factorId);
        return jsonResultVo;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateCalFactorDTO(CalFactorDTO calFactorDTO) {
        // 校验是否为空
        if (BootAppUtil.isNullOrEmpty(calFactorDTO)) {
            return "保存失败";
        }
        return validateId(calFactorDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateId(CalFactorDTO calFactorDTO) {
        // 校验主键是否合法
        if (BootAppUtil.isnotNullOrEmpty(calFactorDTO.getId()) && calFactorDTO.getId().length() > RebateConstants.ID_LENGTH_36) {
            return "主键长度过长";
        }
        // 校验政策主键是否合法
        if (BootAppUtil.isNullOrEmpty(calFactorDTO.getPolicyId())) {
            return "政策主键不能为空";
        }
        if (calFactorDTO.getPolicyId().length() > RebateConstants.ID_LENGTH_36) {
            return "政策主键长度过长";
        }
        return validateFactor(calFactorDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFactor(CalFactorDTO calFactorDTO) {
        // 校验计算因子名称是否合法
        if (BootAppUtil.isNullOrEmpty(calFactorDTO.getFactorName())) {
            return "计算因子名称不能为空";
        }
        if (calFactorDTO.getFactorName().length() > RebateConstants.FIELD_LENGTH_255) {
            return "计算因子名称长度过长";
        }
        if (!RegexUtils.validateName(calFactorDTO.getFactorName())) {
            return "名称只能包含数字、字母或汉字";
        }
        // 校验计算因子描述是否合法
        if (BootAppUtil.isnotNullOrEmpty(calFactorDTO.getDescription()) && calFactorDTO.getDescription().length() > RebateConstants.FIELD_LENGTH_255) {
            return "计算因子描述长度过长";
        }
        // 校验取值逻辑是否合法
        if (BootAppUtil.isNullOrEmpty(calFactorDTO.getFetchLogic())) {
            return "取值逻辑不能为空";
        }
        if (calFactorDTO.getFetchLogic().length() > RebateConstants.FIELD_LENGTH_255) {
            return "取值逻辑长度过长";
        }
        return validateFactorField(calFactorDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFactorField(CalFactorDTO calFactorDTO) {
        // 校验数据源取值是否合法
        if (calFactorDTO.getDataSource() == null || calFactorDTO.getDataSource().isEmpty()) {
            return "数据源取值不能为空";
        }
        if (String.join(RebateConstants.COMMA_SEPARATOR, calFactorDTO.getDataSource()).length() > RebateConstants.FIELD_LENGTH_255) {
            return "数据源取值过多";
        }
        if (!"rank".equals(calFactorDTO.getFetchLogic())) {
            // 校验取值字段是否合法
            if (BootAppUtil.isNullOrEmpty(calFactorDTO.getDataColumn())) {
                return "取值字段不能为空";
            }
            if (calFactorDTO.getDataColumn().length() > RebateConstants.FIELD_LENGTH_255) {
                return "取值字段长度过长";
            }
        } else {
            // 校验排名是否合法
            if (CollUtil.isEmpty(calFactorDTO.getRankInfo())) {
                return "排名不能为空";
            }
        }

        // 校验关联取值对象是否合法
        if (calFactorDTO.getConValueObject() == null || calFactorDTO.getConValueObject().isEmpty()) {
            return "关联取值对象不能为空";
        }
        /*if(String.join(RebateConstants.COMMA_SEPARATOR, calFactorDTO.getConValueObject()).length() > RebateConstants.FIELD_LENGTH_255){
            return "关联取值对象过多";
        }*/
        return validateFilterCondition(calFactorDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFilterCondition(CalFactorDTO calFactorDTO) {
        List<CalFactorConditionsDTO> filterConditionList = calFactorDTO.getFilterCondition();
        if (filterConditionList != null && !filterConditionList.isEmpty()) {
            for (CalFactorConditionsDTO condition : filterConditionList) {
                try {
                    validateFieldName(condition);
                    validateOperation(condition);
                    validateFilterMethod(condition);
                    validateCondition(condition);
                } catch (QmException e) {
                    return e.getMessage();
                }
            }
        }
        return validateDependentSource(calFactorDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFieldName(FilterCondition condition) {
        // 校验字段名
        if (BootAppUtil.isNullOrEmpty(condition.getFieldName())) {
            throw new QmException("字段名不能为空");
        }
        if (condition.getFieldName().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("字段名长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateOperation(FilterCondition condition) {
        // 校验前括号
        if (condition.getFrontBracket() != null && condition.getFrontBracket().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("括号长度过长");
        }
        // 校验运算符
        if (BootAppUtil.isNullOrEmpty(condition.getOperation())) {
            throw new QmException("运算符不能为空");
        }
        if (condition.getOperation().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("运算符长度过长");
        }
        // 校验后括号
        if (condition.getBackBracket() != null && condition.getBackBracket().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("括号长度过长");
        }
        // 校验关联逻辑
        if (condition.getLogic() != null && condition.getLogic().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("关联逻辑长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFilterMethod(FilterCondition condition) {
        // 校验筛选方式
        if (BootAppUtil.isNullOrEmpty(condition.getFilterMethod())) {
            throw new QmException("筛选方式不能为空");
        }
        if (condition.getFilterMethod().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("筛选方式长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateCondition(FilterCondition condition) {
        // 校验条件值
        if (!RebateConstants.VALUE.equals(condition.getFilterMethod()) && BootAppUtil.isNullOrEmpty(condition.getCondition())) {
            // 当筛选方式不是字符串而且为空，则报错
            throw new QmException("条件值不能为空");
        }
        if (BootAppUtil.isnotNullOrEmpty(condition.getCondition()) && condition.getCondition().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("条件值长度过长");
        }
        // 校验条件值格式
        if (RebateConstants.NUMBER.equals(condition.getFilterMethod()) && !NumberUtil.isNumber(condition.getCondition())) {
            throw new QmException("条件值格式有误");
        }
        if (RebateConstants.DATE.equals(condition.getFilterMethod()) && !DateUtil.validateDateTime(condition.getCondition(), RebateConstants.DATE_FORMAT_PATTERN)) {
            throw new QmException("条件值格式有误");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateDependentSource(CalFactorDTO calFactorDTO) {
        List<String> dataSource = calFactorDTO.getDataSource();
        List<CalFactorJoinDTO> dependents = calFactorDTO.getDependents();
        if (dependents != null && !dependents.isEmpty()) {
            if (dataSource.size() > 1) {
                return "主数据源为两个或两个以上时，从数据源应该为空";
            }
            for (CalFactorJoinDTO dependent : dependents) {
                if (dependent.getJoinType() == null) {
                    return "关联类型不能为空";
                }
                if (BootAppUtil.isNullOrEmpty(dependent.getTableName())) {
                    return "从数据源不能为空";
                }
                if (dependent.getTableName().length() > RebateConstants.FIELD_LENGTH_255) {
                    return "从数据源长度过长";
                }
                List<CalFactorJoinOnDTO> conditions = dependent.getConditions();
                if (conditions != null && !conditions.isEmpty()) {
                    try {
                        validateJoinCondition(conditions);
                    } catch (QmException e) {
                        return e.getMessage();
                    }
                } else {
                    return "关联条件不能为空";
                }
            }
        }
        return "";
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinCondition(List<CalFactorJoinOnDTO> conditions) {
        for (CalFactorJoinOnDTO condition : conditions) {
            validateFieldName(condition);
            validateOperation(condition);
            validateFilterMethod(condition);
            validateCondition(condition);
        }
    }

    /**
     * 针对主表字段排序
     * @param list
     * @return
     */
    private List<CalculateFactorsMainDO> mainTableSort(List<CalculateFactorsMainDO> list) {
        if(CollectionUtils.isNotEmpty(list)) {
            list.forEach(l -> {
                String[] ms = l.getMainTable().split(",");
                ms = Arrays.stream(ms).map(String::trim).toArray(String[]::new);
                Arrays.sort(ms);
                l.setMainTable(String.join(",", ms));
            });
        }
        return list;
    }
}
