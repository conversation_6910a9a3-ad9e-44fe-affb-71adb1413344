package com.qm.ep.rebate.common.constant;

public class ProcessConstant {

    /**
     * 在流程中心中配置应用码
     */
    public static final String PC_URL = "pcUrl";

    public static final String APP_URL = "appUrl";

    public static final String UAT_HOME_URL = "https://uat-iwork.faw.cn/policy_virtual/policySetAudit?";

    public static final String PROD_HOME_URL = "https://iwork.faw.cn/policy_virtual/policySetAudit?";

    public static final String CLUE_ID = "clueId=";
    public static final String TASK_INSTANCE_CODE = "&taskInstanceCode=";
    /**
     * 流程动作监听
     */
    public static final String MESSAGE_SEND = "message_send";
    /**
     *  流程结束时间监听
     */
    public static final String PROCESS_LISTENER = "process_listener";
    /**
     * 流程自动调用服务
     */
    public static final String SERVICE_CALLBACK = "service_callback";

    public static final String SUCCESS = "success";

    public static final String MESSAGE = "message";

    public static final String DATA = "data";

    public static final String INTERFACE_PARAM = "interfaceParam";

    public static final String APPSOURCE = "appsource";

    public static final String EXECUTION = "execution";

    public static final String TASK = "task";

    public static final String EVENT_NAME = "eventName";

    public static final String BUSINESS_KEY = "businessKey";

    public static final String CATEGORY = "category";

    public static final String DELETE_REASON = "deleteReason";

    public static final String END = "end";

    public static final String REJECT_TO_START = "REJECTTOSTART";


    public static final String PROCESSINSTANCEID = "processInstanceId";

    public static final String PROCINSTID = "procInstId";

    public static final String PROCINSTNAME = "procInstName";

    public static final String BTNSTATUS2 = "重新提交";

    public static final String BTNSTATUS3 = "放弃";
}
