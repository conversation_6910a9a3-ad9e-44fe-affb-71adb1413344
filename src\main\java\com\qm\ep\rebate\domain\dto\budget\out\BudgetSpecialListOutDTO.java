package com.qm.ep.rebate.domain.dto.budget.out;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "专项")
@Data
public class BudgetSpecialListOutDTO {

    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-兑付依据（00-aak，01-std）")
    private String saleType;

    @Schema(description = "数据-时间类型")
    private String timeType;

    @Schema(description = "数据-年")
    private String year;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    private String bgtType;

    @Schema(description = "数据-预算金额")
    private BigDecimal classitemTotalAmount;

    @Schema(description = "数据-使用金额")
    private BigDecimal usedAmount;

    @Schema(description = "数据-占用金额")
    private BigDecimal amountBalance;

    @Schema(description = "数据-余额")
    @TableField("classItem_amount_balance")
    private BigDecimal classitemAmountBalance;
}
