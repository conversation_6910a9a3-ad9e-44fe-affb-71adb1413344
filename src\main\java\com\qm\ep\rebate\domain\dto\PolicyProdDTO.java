package com.qm.ep.rebate.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.ep.rebate.domain.bean.PolicyProdDO;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 *
 * 政策对应产品
 *
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Schema(description = "数据:政策对应产品")
@Data
public class PolicyProdDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-主表ID")
    private String npolicyid;
    @Schema(description = "数据-产品id")
    private String nproductid;
    @Schema(description = "数据-时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @Schema(description = "数据-导入列表")
    private List<PolicyProdDO> list;
}