package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REPORT_JOIN_ON")
@Schema(description = "数据:报表明细关联条件表对象")
public class ReportJoinOnDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-报表ID")
    @TableField("REPORT_ID")
    private String reportId;

    @Schema(description = "数据-关联表ID")
    @TableField("JOIN_ID")
    private String joinId;

    @Schema(description = "数据-开始括号")
    @TableField("BRACKET_BEFORE")
    private String bracketBefore;

    @Schema(description = "数据-条件中左边的字段名")
    @TableField("LEFT_FIELD_NAME")
    private String leftFieldName;

    @Schema(description = "数据-条件中左边的字段所在的表名")
    @TableField("LEFT_FIELD_FROM")
    private String leftFieldFrom;

    @Schema(description = "数据-条件中左边的表所在的政策名")
    @TableField("LEFT_TABLE_SOURCE")
    private String leftTableSource;

    @Schema(description = "数据-计算逻辑")
    @TableField("CALCULATION_LOGIC")
    private String calculationLogic;

    @Schema(description = "数据-条件类型")
    @TableField("CONDITION_TYPE")
    private String conditionType;

    @Schema(description = "数据-条件中右边的值")
    @TableField("RIGHT_VALUE")
    private String rightValue;

    @Schema(description = "数据-条件中右边的字段所在的表名")
    @TableField("RIGHT_FIELD_FROM")
    private String rightFieldFrom;

    @Schema(description = "数据-条件中右边的表所在的政策名")
    @TableField("RIGHT_TABLE_SOURCE")
    private String rightTableSource;

    @Schema(description = "数据-结束括号")
    @TableField("BRACKET_AFTER")
    private String bracketAfter;

    @Schema(description = "数据-关联逻辑")
    @TableField("ASSOCIATION_LOGIC")
    private String associationLogic;

    @Schema(description = "数据-排序")
    @TableField("sort")
    private Integer sort;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;



}
