package com.qm.ep.rebate.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 *  
 * 维护已发布政策信息
 *  
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Schema(description = "数据:维护已发布政策信息")
@Data
public class PolicyPublishedDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-政策编号")
    private String vpolicycode;
    @Schema(description = "数据-政策名称")
    private String vpolicyname;
    @Schema(description = "数据-是否调整 0/1")
    private String isadjust;
    @Schema(description = "数据-调整后编号")
    private String adjustvpolicycode;
    @Schema(description = "数据-部门 存id，逗号分割")
    private String department;
    @Schema(description = "数据-经办人 存id，逗号分割")
    private String agent;
    @Schema(description = "数据-发布日期")
    private Date publishdate;
    @Schema(description = "数据-发布开始期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishstartdate;
    @Schema(description = "数据-发布结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishenddate;
    @Schema(description = "数据-备注")
    private String remark;
    @Schema(description = "数据-暂不需配置")
    private String norequired;
    @Schema(description = "数据-停用")
    private String stop;
    @Schema(description = "数据-停用时间")
    private Date stopdate;
    @Schema(description = "数据-创建")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createon;
    @Schema(description = "数据-创建者")
    private String createby;
    @Schema(description = "数据-更新")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateon;
    @Schema(description = "数据-更新作者")
    private String updateby;
    @Schema(description = "数据-DTSTAMP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
    @Schema(description = "数据-任务实例编码")
    private String taskInstanceCode;
    @Schema(description = "数据-任务流实例编码")
    private String taskFlowInstanceCode;
    @Schema(description = "数据-政策阶段")
    private String stage;
    @Schema(description = "数据-政策周期")
    private String policycycle;
}