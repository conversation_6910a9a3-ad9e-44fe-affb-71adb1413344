package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 预算冻结表
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_entry_account")
@Schema(description = "数据:预算冻结表")
public class BgtEntryAccountDO extends Model<BgtEntryAccountDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-政策id")
    @TableField("policy_id")
    private String policyId;

    @Schema(description = "数据-正式计算历史id")
    @TableField("history_id")
    private String historyId;

    @Schema(description = "数据-入账数据传输EP时间")
    @TableField("entry_transfer_time")
    private Date entryTransferTime;

    @Schema(description = "数据-车系入账金额")
    @TableField("entry_amount")
    private BigDecimal entryAmount;

    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    @Schema(description = "数据-冻结的车系的预算金额")
    @TableField("bgt_amount")
    private BigDecimal bgtAmount;

    @Schema(description = "数据-入账时的差额（取返利项目池子余额）")
    @TableField("diff")
    private BigDecimal diff;

    @Schema(description = "数据-冻结状态（0-冻结，1-解冻）")
    @TableField("freeze_status")
    private String freezeStatus;

    @Schema(description = "数据-审批状态（55-驳回，53-财务审核通过）")
    @TableField("approve_status")
    private String approveStatus;


    @Schema(description = "数据-结算状态（0-未结算，1-已结算）")
    @TableField("settle_status")
    private String settleStatus;

    @Schema(description = "数据-解冻时间（财务审核通过或驳回时间）")
    @TableField("unfreeze_time")
    private Date unfreezeTime;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
