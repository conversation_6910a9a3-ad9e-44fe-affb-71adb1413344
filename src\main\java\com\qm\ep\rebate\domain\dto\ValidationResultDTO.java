package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-验证结果 DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResultDTO {

    @Schema(description = "数据-还行")
    private Boolean ok;
    @Schema(description = "数据-味精")
    private String msg;
    @Schema(description = "数据-数据")
    private Object data;

    public ValidationResultDTO(Boolean ok) {
        this(ok, null, null);
    }

    public static ValidationResultDTO successValidationResult(String msg) {
        return new ValidationResultDTO(true, msg, null);
    }

    public static ValidationResultDTO errorValidationResult(String msg) {
        return new ValidationResultDTO(false, msg, null);
    }

    public static ValidationResultDTO successValidationResult(String msg, Object data) {
        return new ValidationResultDTO(true, msg, data);
    }

    public static ValidationResultDTO errorValidationResult(String msg, Object data) {
        return new ValidationResultDTO(false, msg, data);
    }
}
