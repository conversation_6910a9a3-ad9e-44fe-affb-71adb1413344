package com.qm.ep.rebate.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qm.ep.rebate.domain.bean.AuditApproveDO;
import com.qm.ep.rebate.domain.bean.AuditTrailDO;
import com.qm.ep.rebate.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.bean.approve.TaskInstanceDO;
import com.qm.ep.rebate.domain.dto.approve.ProcessHistoryQueryDTO;
import com.qm.ep.rebate.domain.vo.RebateCalculatorVO;
import com.qm.ep.rebate.service.*;
import com.qm.ep.rebate.service.approve.ProcessService;
import com.qm.ep.rebate.service.approve.TaskInstanceService;
import com.qm.ep.rebate.remote.http.base.ApiGatewayService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.DateUtils;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "审批流轨迹")
@RestController
@RequestMapping("/task")
public class TaskInstanceController {

    @Resource
    private TaskInstanceService taskInstanceService;

    @Resource
    private AuditApproveService auditApproveService;

    @Resource
    private PolicyService policyService;

    @Resource
    private AuditTrailService auditTrailService;



    @Resource
    private ExecFormalCalcHistoryService execFormalCalcHistoryService;

    @Resource
    private ProcessService processService;

    @Resource
    private ApiGatewayService apiGatewayService;

    @Resource
    private DictionaryService dictionaryService;







    @Operation(summary = "获取审批轨迹", description = "[author: 10200571]")
    @GetMapping("/list")
    public JsonResultVo<List<TaskInstanceDO>> getList(@RequestParam String taskFlowInstanceCode){
        JsonResultVo<List<TaskInstanceDO>> result = new JsonResultVo<>();
        QmQueryWrapper<RebateCalculatorVO> queryWrapper = new QmQueryWrapper<>();
        queryWrapper.eq("task_flow_instance_code",taskFlowInstanceCode);
        List<TaskInstanceDO> List = taskInstanceService.list();
        result.setData(List);
        return result;
    }


    @Operation(summary = "获取审批轨迹", description = "[author: 10200571]")
    @GetMapping("/getApproveList")
    public JsonResultVo<Map<String ,Object>> getApproveList(@RequestParam String policyId,@RequestParam String id){


        PolicyDO policyDO = policyService.getById(policyId);
        JsonResultVo<Map<String ,Object>> resultVo = new JsonResultVo<>();
        if (Objects.isNull(policyDO)){
            resultVo.setCode(500);
            resultVo.setMsg("无法获取政策信息");
            return resultVo;
        }

        QmQueryWrapper<ExecFormalCalcHistoryDO> qw = new QmQueryWrapper<>();
        qw.eq("policyId",policyId);
        if (StringUtils.isNotEmpty(id)){
            qw.eq("id",id);
        }
        List<ExecFormalCalcHistoryDO> formalCalcHistoryDOS = execFormalCalcHistoryService.list(qw);
        QmQueryWrapper<AuditApproveDO> queryWrapper = new QmQueryWrapper<>();
        //查询出这个政策所有入账的信息
        List<String> bizIds = formalCalcHistoryDOS.stream().map(ExecFormalCalcHistoryDO::getId).collect(Collectors.toList());
        bizIds.add(policyId);
        queryWrapper.in("biz_id",bizIds);
        //根据入账信息查询所有提交的审批数据
        List<AuditApproveDO> list = auditApproveService.list(queryWrapper);
        List<JSONObject> s_list =Lists.newArrayList();
        Map<String, Object> resultMap = Maps.newHashMap();

        JSONObject data = new JSONObject();
        if (!list.isEmpty()){
            //去重审批流
            Set<String> pidSet = Sets.newHashSet();
            List<String> pids = list.stream().filter(it->StringUtils.isNotEmpty(it.getProcessInstanceId())&&pidSet.add(it.getProcessInstanceId())).map(AuditApproveDO::getProcessInstanceId).collect(Collectors.toList());
            //根据流程号去 调用 审批流数据
            Map<String,JSONArray> p_map = getProcessHistory(pids).blockingGet();
            //开始分析一下当前审批归属
            Map<String,List<AuditApproveDO>> a_map = list.stream().collect(Collectors.groupingBy(AuditApproveDO::getBizId));
            //先获取 政策信息的配置
            List<String> p_approves =  new ArrayList<>();
            if(Objects.nonNull(a_map) && Objects.nonNull(a_map.get(policyId))){
                p_approves =  a_map.get(policyId).stream().map(AuditApproveDO::getProcessInstanceId).collect(Collectors.toList());
            }
            //构造政策审批的审批流


            data.put("id",policyId);
            data.put("subTime",policyDO.getPolicyFinish());
            List<JSONObject> dds =getProcess(p_map,p_approves);
            List<JSONObject> newList = dds.stream().filter(it->StringUtils.isNotEmpty(it.getString("time"))).sorted(Comparator.comparing(it-> it.getString("time"))).collect(Collectors.toList());
            newList.addAll(dds.stream().filter(it->StringUtils.isEmpty(it.getString("time"))).collect(Collectors.toList()));
            data.put("list",newList);

            resultMap.put("policy",data);


            //构造下面所有的入账的审批数据
            for (String key: a_map.keySet()){
                if (!StringUtils.equals(key,policyId)){
                    //获取入账的所有审批记录
                    List<String> p_ids = a_map.get(key).stream().map(AuditApproveDO::getProcessInstanceId).collect(Collectors.toList());
                    JSONObject data1 = new JSONObject();
                    data1.put("id",key);
                    data1.put("subTime",a_map.get(key).get(0).getCreateOn());
                    //获取入账信息的审批流
                    data1.put("list",getProcess(p_map,p_ids).stream().filter(it->StringUtils.isNotEmpty(it.getString("time"))).sorted(Comparator.comparing(it-> it.getString("time"))).collect(Collectors.toList()));
                    s_list.add(data1);
                }
            }



        }

        //比对一下 是否有些审批属于老数据 新老数据兼容
        List<String> oldIds = Lists.newArrayList();
        for (String oldId:bizIds){
            Optional<AuditApproveDO> doOptional = list.stream().filter(it->it.getBizId().equals(oldId)).findFirst();
            if (!doOptional.isPresent()){
                oldIds.add(oldId);
            }
        }
        if (!oldIds.isEmpty()){
            //查询老数据
            List<AuditTrailDO> auditTrailDOS = auditTrailService.getAllList(oldIds);
            Map<String,List<AuditTrailDO>>  auditMap = auditTrailDOS.stream().collect(Collectors.groupingBy(AuditTrailDO::getObjectId));
            if (!auditMap.isEmpty()){
                List<AuditTrailDO> p_ados= auditMap.get(policyId);

                //老政策审批数据 处理
                if (Objects.nonNull(p_ados)&&!p_ados.isEmpty()){

                    data.put("id",policyId);
                    data.put("subTime",policyDO.getPolicyFinish());
                    data.put("list",p_ados.stream().map(item->{
                        JSONObject o = new JSONObject();
                        o.put("approver",item.getUserName());
                        o.put("taskDefinitionKey","");
                        o.put("durationTime",1);
                        o.put("level",1);
                        o.put("remark",item.getFullMessage());
                        o.put("time", DateUtils.format(item.getTime(),"yyyy-MM-dd HH:mm:ss"));
                        o.put("title",item.getTypeDesc());
                        return o;
                    }).sorted(Comparator.comparing(it-> it.getString("time"))).collect(Collectors.toList()));
                    resultMap.put("policy",data);
                }

                //老入账审批处理

                for (String key:auditMap.keySet()){
                    if (!StringUtils.equals(key,policyId)){
                        List<AuditTrailDO> p_idos= auditMap.get(policyId);
                        if (Objects.nonNull(p_idos)&&!p_idos.isEmpty()){
                            JSONObject data1 = new JSONObject();
                            data1.put("id",policyId);
                            data1.put("subTime",policyDO.getPolicyFinish());
                            data1.put("list",p_idos.stream().map(item->{
                                JSONObject o = new JSONObject();
                                o.put("approver",item.getUserName());
                                o.put("taskDefinitionKey","");
                                o.put("durationTime",1);
                                o.put("time", DateUtils.format(item.getTime(),"yyyy-MM-dd HH:mm:ss"));
                                o.put("level",1);
                                o.put("remark",item.getFullMessage());
                                o.put("title",item.getTypeDesc());
                                return o;
                            }).sorted(Comparator.comparing(it-> it.getString("time"))).collect(Collectors.toList()));
                            s_list.add(data1);
                        }
                    }
                }
            }
        }

        resultMap.put("list",s_list);
        resultVo.setData(resultMap);
        resultVo.setDataList(Lists.newArrayList());

        return resultVo;



    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @GetMapping("/test")
    public String test( ){
       return processService.createDemo()
//               .observeOn(Schedulers.io())
               .flatMap(it->processService.createDemo())
//               .subscribeOn(Schedulers.io())
               .blockingGet();
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    Single<Map<String,JSONArray>> getProcessHistory(List<String> pIds){
        return Flowable.fromIterable(pIds)
                .parallel()
                .runOn(Schedulers.io())
                .flatMap(this::getProcessHistoryAction)
                .sequential()
                .toList()
                .map(it->{
                  Map<String,JSONArray> map = Maps.newHashMap();
                  for (Map<String,JSONArray> item:it){
                      for (String key: item.keySet()){
                          map.put(key,item.get(key));
                      }
                  }
                  return map;
                });
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    Flowable<Map<String,JSONArray>> getProcessHistoryAction(String pId){
        return Flowable.unsafeCreate(sub->{
            Map<String,JSONArray> map = Maps.newHashMap();
            try {
                //获取审批记录
                String mesage = processService.getProcessHistory(apiGatewayService.getToken(), ProcessHistoryQueryDTO.builder().procInstId(pId).build());
                map.put(pId,JSONArray.parseArray(mesage));
            }catch (Exception e){
                map.put(pId,new JSONArray());
            }
            sub.onNext(map);
            sub.onComplete();

        });
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private List<JSONObject> getProcess(Map<String,JSONArray> p_map,List<String> p_approves){
        //整理下数据将 Map<String,JSONArray> 整理成单元的审批数据
      return    p_approves.stream()
                .map(it-> {
                    List<JSONObject> data = Lists.newArrayList();
                    JSONArray array = p_map.get(it);
                    if (Objects.nonNull(array)){
                        for (int i=0;i<array.size();i++){
                            JSONObject item = array.getJSONObject(i);
                            data.add(item);
                        }
                    }

                    return data;
                }).collect(ArrayList::new,ArrayList::addAll,(v1,v2)->{});
    }



    


}
