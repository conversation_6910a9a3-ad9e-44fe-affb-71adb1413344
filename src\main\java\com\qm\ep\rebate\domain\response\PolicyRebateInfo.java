package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "返利看板响应对象")
@Getter
@Setter
public class PolicyRebateInfo {
    /**
     * 政策编码
     */
    @Schema(description = "政策编码")
    private String policyCode;

    /**
     * 入账时间
     */
    @Schema(description = "入账时间")
    private String applyTime;

    /**
     * 结算金额
     */
    @Schema(description = "结算金额")
    private BigDecimal settlementAmount;



    /**
     * 政策id
     */
    @Schema(description = "政策名字")
    private String policyName;


    /**
     * 第几次申请入账
     */
    @Schema(description = "第几次申请入账")
    private int applyNumberTh;
}





