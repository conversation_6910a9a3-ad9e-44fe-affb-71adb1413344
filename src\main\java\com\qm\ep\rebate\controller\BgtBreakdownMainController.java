package com.qm.ep.rebate.controller;


import com.faw.supplychain.common.utils.JsonUtil;
import com.qm.ep.rebate.domain.bean.BgtBreakdownMainDO;
import com.qm.ep.rebate.domain.request.BgtBreakdownMainRequest;
import com.qm.ep.rebate.domain.vo.BgtBreakdownMainVO;
import com.qm.ep.rebate.remote.request.RemoteBgtRequest;
import com.qm.ep.rebate.remote.response.RemoteBgtResponse;
import com.qm.ep.rebate.service.BgtBreakdownMainService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.domain.LoginKeyDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *  
 * 预算分解主表 前端控制器
 *  
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@Tag(name = "/bgtBreakdown", description = "[author: 10200571]")
@RestController
@Slf4j
@RequestMapping("/bgtBreakdown")
public class BgtBreakdownMainController extends BaseController {

    @Resource
    private BgtBreakdownMainService bgtBreakdownMainService;

    @Operation(summary = "查询预算分解主表列表", description = "[author: 10200571]")
    @PostMapping("/bgtBreakdownMainList")
    public JsonResultVo<QmPage<BgtBreakdownMainDO>> bgtBreakdownMainList(@RequestBody BgtBreakdownMainRequest bgtBreakdownMainRequest) {
        log.info("查询预算分解主表列表Controller: bgtBreakdownMainList Begin");
        JsonResultVo<QmPage<BgtBreakdownMainDO>> jsonResultVo = new JsonResultVo<>();

        QmPage<BgtBreakdownMainDO> bgtBreakdownMainDOS = bgtBreakdownMainService.getBgtBreakdownMainList(bgtBreakdownMainRequest);

        jsonResultVo.setData(bgtBreakdownMainDOS);
        jsonResultVo.setMsg("返回成功");

        log.info("查询预算分解主表列表Controller: bgtBreakdownMainList End");
        return jsonResultVo;
    }

    @Operation(summary = "预算分解及调整明细查询-常规", description = "[author: 10200571]")
    @PostMapping("/bgtBreakdownMainDetailList")
    public JsonResultVo<BgtBreakdownMainVO> bgtBreakdownMainDetailList(@RequestBody BgtBreakdownMainRequest breakdownMainRequest) {
        log.info("预算分解及调整明细查询-常规Controller: bgtBreakdownMainDetailList Begin");


        LoginKeyDO userInfo = getUserInfo();
        JsonResultVo<BgtBreakdownMainVO> jsonResultVo = new JsonResultVo<>();
        BgtBreakdownMainVO bgtBreakdownMainVO = bgtBreakdownMainService.bgtBreakdownMainDetailList(breakdownMainRequest, userInfo);

        jsonResultVo.setData(bgtBreakdownMainVO);

        log.info("预算分解及调整明细查询-常规Controller: bgtBreakdownMainDetailList End");
        return jsonResultVo;
    }

    @Operation(summary = "预算分解及调整明细查询-专项", description = "[author: 10200571]")
    @PostMapping("/bgtBreakdownMainDetailSpList")
    public JsonResultVo<BgtBreakdownMainVO> bgtBreakdownMainDetailSpList(@RequestBody BgtBreakdownMainRequest bgtBreakdownMainRequest) {
        log.info("预算分解及调整明细查询-专项Controller: bgtBreakdownMainDetailSpList Begin");
        LoginKeyDO userInfo = getUserInfo();
        JsonResultVo<BgtBreakdownMainVO> jsonResultVo = new JsonResultVo<>();
        BgtBreakdownMainVO bgtBreakdownMainVO = bgtBreakdownMainService.getBgtBreakdownMainSpList(bgtBreakdownMainRequest, userInfo);

        jsonResultVo.setData(bgtBreakdownMainVO);

        log.info("预算分解及调整明细查询-专项Controller: bgtBreakdownMainDetailSpList End");
        return jsonResultVo;
    }

    /**
     * {"year":"2024","quarter":"1"}：季度度分解任务触发
     * {"year":"2024"}：年度分解任务触发
     *
     * @param bgtBreakdownMainVO bgt分解主vo
     * @return {@link JsonResultVo}<{@link BgtBreakdownMainVO}>
     */
    @Operation(summary = "定时任务——预算分解初始化", description = "[author: 10200571]")
    @PostMapping("/bgtBreakdownInit")
    public JsonResultVo<BgtBreakdownMainVO> bgtBreakdownInit(@RequestBody BgtBreakdownMainVO bgtBreakdownMainVO) {
        log.info("预算分解初始化Controller: bgtBreakdownInit Begin");

        JsonResultVo<BgtBreakdownMainVO> jsonResultVo = new JsonResultVo<>();

        BgtBreakdownMainVO bgtBreakdownInit = bgtBreakdownMainService.bgtBreakdownInit(bgtBreakdownMainVO);

        jsonResultVo.setData(bgtBreakdownInit);

        log.info("预算分解初始化Controller: bgtBreakdownInit End");
        return jsonResultVo;
    }

    @Operation(summary = "预算分解及调整明细保存与发布-常规", description = "[author: 10200571]")
    @PostMapping("/bgtBreakdownMainDetailSave")
    public JsonResultVo<Boolean> bgtBreakdownMainDetailSave(@RequestBody BgtBreakdownMainRequest breakdownMainRequest) {
        return bgtBreakdownMainService.bgtBreakdownMainDetailSave(breakdownMainRequest);
    }

    @Operation(summary = "发送任务给政策信息维护专员", description = "[author: 10200571]")
    @PostMapping("/triggerTaskForBgtPolicyMaintainer")
    public JsonResultVo<Boolean> triggerTaskForBgtPolicyMaintainer(@RequestBody BgtBreakdownMainDO breakdownMainDO) {
        log.info("发送任务给政策信息维护专员-Controller: triggerTaskForBgtPolicyMaintainer Begin");

        JsonResultVo<Boolean> jsonResultVo = new JsonResultVo<>();
        Boolean result = bgtBreakdownMainService.triggerTaskForBgtPolicyMaintainer(breakdownMainDO);

        jsonResultVo.setData(result);

        log.info("发送任务给政策信息维护专员-Controller: triggerTaskForBgtPolicyMaintainer End");
        return jsonResultVo;
    }


    @Operation(summary = "财务EOM审批通过下发数据及创建新副本", description = "[author: 10200571]")
    @PostMapping("/approveFromEom")
    public JsonResultVo<BgtBreakdownMainDO> approveFromEom(@RequestBody Map<String, String> input) {
        log.info("EOM审批通过下发数据及创建新副本-Controller: triggerTaskForBgtPolicyMaintainer Begin");

        JsonResultVo<BgtBreakdownMainDO> jsonResultVo = new JsonResultVo<>();
        BgtBreakdownMainDO result = bgtBreakdownMainService.approveFromEom(input.get("auditId"));

        jsonResultVo.setData(result);

        log.info("EOM审批通过下发数据及创建新副本-Controller: triggerTaskForBgtPolicyMaintainer End");
        return jsonResultVo;
    }


    @Operation(summary = "查询季度预算分解点数使用情况", description = "[author: 10200571]")
    @PostMapping("/queryQuarterBgtPoint")
    public JsonResultVo<List<RemoteBgtResponse>> queryQuarterBgtPoint(@RequestBody RemoteBgtRequest request) {

        log.info("查询季度预算分解点数使用情况-Controller: queryQuarterBgtPoint Begin.request:{}", JsonUtil.toJsonString(request));

        JsonResultVo<List<RemoteBgtResponse>> jsonResultVo = new JsonResultVo<>();
        List<RemoteBgtResponse> result = bgtBreakdownMainService.queryQuarterBgtPoint(request);

        jsonResultVo.setData(result);

        log.info("查询季度预算分解点数使用情况-Controller: queryQuarterBgtPoint End.response:{}", JsonUtil.toJsonString(result));
        return jsonResultVo;
    }
}
