package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REPORT_JOIN")
@Schema(description = "数据:报表明细关联表对象")
public class ReportJoinDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-报表ID")
    @TableField("REPORT_ID")
    private String reportId;

    @Schema(description = "数据-关联类型")
    @TableField("JOIN_TYPE")
    private String joinType;

    @Schema(description = "数据-计算方案合并标识")
    @TableField("JOIN_TABLE")
    private String joinTable;

    @Schema(description = "数据-关联对象数据源")
    @TableField(exist = false)
    private CalcObjectSourceDO joinTableSource;

    @Schema(description = "数据-关联条件")
    @TableField(exist = false)
    private List<ReportJoinOnDO> joinOns;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;



}
