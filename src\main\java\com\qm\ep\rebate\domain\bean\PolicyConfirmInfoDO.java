package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy_confirm_info")
@Schema(description = "数据实体")
public class PolicyConfirmInfoDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "数据-政策id")
    @TableField("VPOLICYID")
    private String vpolicyid;

    @Schema(description = "数据-政策编号")
    @TableField("VPOLICYCODE")
    private String vpolicycode;

    @Schema(description = "数据-政策经办人")
    @TableField("AGENT")
    private String agent;

    @Schema(description = "数据-任务实例编码")
    @TableField("TASKINSTANCECODE")
    private String taskinstancecode;

    @Schema(description = "数据-任务流实例编码")
    @TableField("TASKFLOWINSTANCECODE")
    private String taskflowinstancecode;

    @Schema(description = "数据-创建时间")
    @TableField("CREATETIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createtime;

}