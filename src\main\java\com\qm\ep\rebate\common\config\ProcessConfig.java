package com.qm.ep.rebate.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023-07-3 16:33
 */
@Configuration
@RefreshScope
@Data
public class ProcessConfig {

    @Value("${process.config.tenantLimitId:r2sc8ufe}")
    private String tenantLimitId;

    @Value("${process.config.appsource:SA-0214}")
    private String appsource;

    @Value("${process.config.category:SA-0214.SA-0214.SA-0214-msa-a01-rebate}")
    private String category;

    @Value("${process.config.sysCode:faw_swzc}")
    private String sysCode;

    @Value("${process.config.procCode:faw_swzc.SA-0214-msa-a01-rebate}")
    private String procCode;

}
