package com.qm.ep.rebate.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：2023/8/30 14:11
 */
@Schema(description = "政策发布配置")
@Data
public class PolicyPublishConfigGetDTO {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-主表ID")
    private String policyId;

    @Schema(description = "数据-经销商代码")
    private String dealerCode;

    @Schema(description = "数据-是否可见 1是0否")
    private Integer visible;

    @Schema(description = "数据-允许试算 1是0否")
    private Integer calc;

    @Schema(description = "aak,std,invoice")
    private String trailType;

    @Schema(description = "数据-时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-经销商名称")
    private String dealerName;

    @Schema(description = "数据-经销商属性")
    private String vattr;

    @Schema(description = "数据-经销商类型")
    private String dealerType;

    @Schema(description = "数据-建档日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dcreate;

    @Schema(description = "数据-开业日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dopen;

    @Schema(description = "数据-大区")
    private String region;

    @Schema(description = "数据-省份")
    private String provice;

    @Schema(description = "数据-城市")
    private String city;

}
