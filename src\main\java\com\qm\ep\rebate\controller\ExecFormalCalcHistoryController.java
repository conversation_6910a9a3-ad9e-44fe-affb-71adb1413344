package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.qm.ep.rebate.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebate.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebate.domain.bean.sys.SysOrgDO;
import com.qm.ep.rebate.domain.dto.ExecCalcHistoryDTO;
import com.qm.ep.rebate.domain.vo.SqlStructureVO;
import com.qm.ep.rebate.enumerate.AccountEntryStateEnum;
import com.qm.ep.rebate.service.ExecFormalCalcHistoryService;
import com.qm.ep.rebate.service.ExecFormalCalcResultService;
import com.qm.ep.rebate.service.SysPersonOrgService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/execFormalCalcHistory")
@Tag(description = "计算对象计算历史", name = "计算对象计算历史")
@Slf4j
public class ExecFormalCalcHistoryController {

    @Resource
    ExecFormalCalcHistoryService historyService;

    @Resource
    ExecFormalCalcResultService resultService;

    @Operation(summary = "查询计算对象计算历史列表", description = "[author: 10200571]")
    @PostMapping("/resultList")
    public JsonResultVo<QmPage<ExecFormalCalcResultDO>> resultList(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        JsonResultVo<QmPage<ExecFormalCalcResultDO>> result = new JsonResultVo<>();
        QmPage<ExecFormalCalcResultDO> page = historyService.queryCalcResult(execCalcHistoryDTO);
        result.setData(page);
        return result;
    }

    @Operation(summary = "查询计算对象计算字段列表", description = "[author: 10200571]")
    @PostMapping("/resultFields")
    public JsonResultVo<List<String>> resultFields(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        ExecFormalCalcHistoryDO execCalcHistoryDO =null;
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.eq(ExecFormalCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId());
        List<ExecFormalCalcHistoryDO> list = historyService.list(lambdaWrapper);
        if(CollectionUtils.isNotEmpty(list)) {
            execCalcHistoryDO = list.get(0);
        } else {
            throw new QmException("未找到计算历史记录！");
        }
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
        ret.setData(sqlStructureVO.getFields());
        return ret;
    }


}
