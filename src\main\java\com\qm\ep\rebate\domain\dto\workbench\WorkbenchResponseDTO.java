package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "数据:实体类-Workbench 响应 DTO")
@Data
@Builder
public class WorkbenchResponseDTO {

    @Schema(description = "数据-完成任务响应")
    private CompleteTaskResponseDTO completeTaskResponse;

    @Schema(description = "数据-触发任务响应")
    private TriggerTaskResponseDTO triggerTaskResponse;

}
