package com.qm.ep.rebate.domain.dto.budget.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "数据:车系信息")
@Data
public class SeriesAmountOutDTO {

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-预算分解金额")
    private BigDecimal sumAmount;

    @Schema(description = "数据-已使用的预算金额 - 实际结算")
    private BigDecimal usedAmount;

    @Schema(description = "数据-预算余额 - 占用")
    private BigDecimal amountBalance;

    @Schema(description = "数据-余额")
    private BigDecimal balanceAmount;

}
