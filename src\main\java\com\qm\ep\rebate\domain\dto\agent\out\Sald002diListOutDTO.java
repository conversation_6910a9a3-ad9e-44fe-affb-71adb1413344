package com.qm.ep.rebate.domain.dto.agent.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "数据:报账单发票对象")
public class Sald002diListOutDTO {

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-报账单主键")
    private String sald002Id;

    @Schema(description = "数据-发票号码")
    private String invoiceNo;

    @Schema(description = "数据-发票编码")
    private String invoiceCode;

    @Schema(description = "数据-发票类型，0纸质发票，1电子发票")
    private String invoiceType;

    @Schema(description = "数据-不含税金额")
    private BigDecimal excTaxAmount;

    @Schema(description = "数据-税额")
    private BigDecimal taxAmount;

    @Schema(description = "数据-含税金额")
    private BigDecimal invoiceAmount;

    @Schema(description = "数据-税率")
    private String taxRate;

    @Schema(description = "数据-开票人")
    private String invoicBy;

    @Schema(description = "数据-开票时间")
    private String invoicTime;

    @Schema(description = "数据-文件id")
    private String fileId;

    @Schema(description = "数据-文件名")
    private String fileName;

    @Schema(description = "数据-文件url")
    private String fileUrl;

    @Schema(description = "数据-文件类型")
    private String fileType;

    @Schema(description = "数据-云原生id")
    private String apiFileId;

    @Schema(description = "数据-是否发送财务共享,1是，0否")
    private String isSend;

    @Schema(description = "数据-验真结果,1是，0否")
    private String isReal;

    @Schema(description = "数据-创建者")
    private String CREATEBY;

    @Schema(description = "数据-创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String CREATEON;

    @Schema(description = "数据-更新者")
    private String UPDATEBY;

    @Schema(description = "数据-更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String UPDATEON;

    @Schema(description = "数据-时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private String DTSTAMP;

}
