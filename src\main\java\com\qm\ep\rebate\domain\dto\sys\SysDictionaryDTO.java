package com.qm.ep.rebate.domain.dto.sys;

import com.qm.ep.rebate.domain.bean.sys.SysDictChildDO;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:数据对象")
@ToString(callSuper = true)
public class SysDictionaryDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-字典代码")
    private String dictCode;

    @Schema(description = "数据-字典名称")
    private String dictName;

    @Schema(description = "数据-字典子表")
    private List<SysDictChildDO> dictChildren;

}