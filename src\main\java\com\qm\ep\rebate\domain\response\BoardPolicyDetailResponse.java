package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;


@Schema(description = "数据:董事会政策细节回应")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BoardPolicyDetailResponse  {

    /**
     * 政策所处节点
     */
    @Schema(description = "政策所处节点")
    private String policyNode;

    /**
     * 计划完成时间
     */
    @Schema(description = "计划完成时间")
    private String planFinishTime;

    /**
     * 政策逾期时长
     */
    @Schema(description = "政策逾期时长")
    private int policyDelay;

    /**
     * 结算周期
     */
    @Schema(description = "结算周期")
    private int settleCount;

    /**
     * 政策名称
     */
    @Schema(description = "政策名称")
    private String policyName;

    /**
     * 创建人部门
     */
    @Schema(description = "创建人部门")
    private String creatorPart;

    /**
     * 政策创建人
     */
    @Schema(description = "政策创建人")
    private String creator;

    /**
     * 是否是新增的
     */
    @Schema(description = "是否是新增的")
    private String isAdd;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BoardPolicyDetailResponse that = (BoardPolicyDetailResponse) o;
        return Objects.equals(policyName, that.policyName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(policyName);
    }
}
