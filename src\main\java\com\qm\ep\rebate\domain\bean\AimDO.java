package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("aim")
@Schema(description = "数据:业务目标分解主表")
public class AimDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-组织代码")
    @TableField("orgCode")
    private String orgCode;

    @Schema(description = "数据-业务目标版本")
    @TableField("aimVersion")
    private String aimVersion;

    @Schema(description = "数据-下发通知方式")
    @TableField("notifyMethod")
    private String notifyMethod;

    @Schema(description = "数据-关联通知单号")
    @TableField("notifyNo")
    private String notifyNo;

    @Schema(description = "数据-状态")
    @TableField("status")
    private String status;

    @Schema(description = "数据-分解详情")
    @TableField(exist=false)
    private List<AimDetailDO> details;
    @Schema(description = "数据-下发对象")
    @TableField(exist=false)
    private List<AimUserDO> users;
    @Schema(description = "数据-用户名")
    @TableField(exist=false)
    private String userNames;
    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;
    @Schema(description = "数据-下达日期")
    @TableField(value = "sendDate")
    private Date sendDate;
}
