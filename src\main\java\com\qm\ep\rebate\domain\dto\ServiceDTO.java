package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "重新部署rebate-布完就删")
@Data
public class ServiceDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

}