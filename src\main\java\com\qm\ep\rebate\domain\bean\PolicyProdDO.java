package com.qm.ep.rebate.domain.bean;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 *
 * 政策对应产品
 *
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy_prod")
@Schema(description = "数据:政策对应产品")
public class PolicyProdDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;


    @Schema(description = "数据-主表ID")
    @TableField("POLICY_ID")
    private String policyId;

    @Excel(name = "品牌", orderNum = "1")
    @Schema(description = "数据-品牌")
    @TableField("BRAND")
    private String brand;

    @Excel(name = "产品代码", orderNum = "1")
    @Schema(description = "数据-产品代码")
    @TableField("PRODUCT_CODE")
    private String productCode;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Excel(name = "技术项组合实例代码（产品代码）", orderNum = "1")
    @Schema(description = "数据-技术项组合实例代码（产品代码）")
    @TableField(value = "VPRODUCTCODE", exist = false)
    private String vproductcode;

    @Schema(description = "数据-产品代码简称")
    @TableField(value = "VPRODUCTTEXT", exist = false)
    private String vproducttext;

    @Excel(name = "产品系列代码", orderNum = "1")
    @Schema(description = "数据-产品系列代码")
    @TableField(value = "VPDTSERIES", exist = false)
    private String vpdtseries;

    @Excel(name = "产品系列名称", orderNum = "1")
    @Schema(description = "数据-产品系列名称")
    @TableField(value = "VPDTSERIESTEXT", exist = false)
    private String vpdtseriestext;

    @Schema(description = "数据-选装名称")
    @TableField(value = "VOPTIONSTEXT", exist = false)
    private String voptionstext;

    @Schema(description = "数据-销售车型")
    @TableField(value = "VSALTYPE", exist = false)
    private String vsaltype;

    @Schema(description = "数据-销售车型名称")
    @TableField(value = "VSALTYPETEXT", exist = false)
    private String vsaltypetext;

    @Schema(description = "数据-车身颜色名称")
    @TableField(value = "VCOLORTEXT", exist = false)
    private String vcolortext;

    @Schema(description = "数据-内饰+颜色名称")
    @TableField(value = "VCABTYPETEXT", exist = false)
    private String vcabtypetext;

    @Schema(description = "数据-失败原因")
    @TableField(value = "reseaon", exist = false)
    private String reseaon;
}
