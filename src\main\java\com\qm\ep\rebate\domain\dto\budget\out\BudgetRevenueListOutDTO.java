package com.qm.ep.rebate.domain.dto.budget.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:收入明细")
@Data
public class BudgetRevenueListOutDTO {
    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-销量明细")
    private List<RevenueMonthSaleListOutDTO> saleList;
}
