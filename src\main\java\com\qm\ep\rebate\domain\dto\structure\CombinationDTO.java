package com.qm.ep.rebate.domain.dto.structure;

import com.qm.ep.rebate.domain.bean.CombinationDetailDO;
import com.qm.ep.rebate.domain.bean.CombinationMainDO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "方案合并")
@Data
public class CombinationDTO {

    @Schema(description = "数据-主结构")
    private CombinationMainDO main;

    @Schema(description = "数据-详情结构")
    private List<CombinationDetailDO> details;

}