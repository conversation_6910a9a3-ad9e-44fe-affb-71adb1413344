package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("global_budget_result")
@Schema(description = "数据:总体预算编制结果对象")
public class GlobalBudgetResultDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-主表ID")
    @TableField("budgetId")
    private String budgetId;

    @Schema(description = "数据-项目")
    @TableField("`project`")
    private String project;

    @Schema(description = "数据-1月")
    @TableField("`jan`")
    private String jan;

    @Schema(description = "数据-2月")
    @TableField("`feb`")
    private String feb;

    @Schema(description = "数据-3月")
    @TableField("`mar`")
    private String mar;

    @Schema(description = "数据-4月")
    @TableField("`apr`")
    private String apr;

    @Schema(description = "数据-5月")
    @TableField("`may`")
    private String may;

    @Schema(description = "数据-6月")
    @TableField("`jun`")
    private String jun;

    @Schema(description = "数据-7月")
    @TableField("`jul`")
    private String jul;

    @Schema(description = "数据-8月")
    @TableField("`aug`")
    private String aug;

    @Schema(description = "数据-9月")
    @TableField("`sep`")
    private String sep;

    @Schema(description = "数据-10月")
    @TableField("`oct`")
    private String oct;

    @Schema(description = "数据-11月")
    @TableField("`nov`")
    private String nov;

    @Schema(description = "数据-12月")
    @TableField("`dec`")
    private String dec;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
