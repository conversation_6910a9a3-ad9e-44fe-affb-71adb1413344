package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("BUSINESS_TASK")
@Schema(description = "数据因子任务")
public class BusinessTaskDO implements Serializable {

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "数据因子id")
    @TableField("table_id")
    private String tableId;

    @Schema(description = "数据因子名称")
    @TableField("table_name")
    private String tableName;

    @Schema(description = "任务类型")
    @TableField("task_type")
    private String taskType;

    @Schema(description = "域账号")
    @TableField("user_code")
    private String userCode;

    @Schema(description = "用户名")
    @TableField("user_name")
    private String userName;

    @Schema(description = "部门名称")
    @TableField("department")
    private String department;

    @Schema(description = "任务编码")
    @TableField("taskinsanceCode")
    private String taskinsanceCode;

    @Schema(description = "任务流程编码")
    @TableField("taskFlowinstanceCode")
    private String taskFlowinstanceCode;

    @Schema(description = "任务状态")
    @TableField("task_status")
    private String taskStatus;

    @Schema(description = "发送时间")
    @TableField("send_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date sendTime;

    @Schema(description = "关闭时间")
    @TableField("close_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date closeTime;

    @Schema(description = "创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

}
