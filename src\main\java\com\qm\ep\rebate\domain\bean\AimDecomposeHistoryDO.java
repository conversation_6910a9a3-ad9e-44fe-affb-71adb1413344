package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 
 *  
 *
 * <AUTHOR>
 * @since 2023-01-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("aim_decompose_history")
@Schema(description = "数据实体")
public class AimDecomposeHistoryDO implements Serializable {


    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据-目标详情ID")
    @TableField("detailId")
    private String detailId;

    @Schema(description = "数据-excel数据是否落库，1：上传中， 2：上传出错，3：完成")
    @TableField("status")
    private String status;

    @Schema(description = "数据-分解合计")
    @TableField("breakdownTotal")
    private Integer breakdownTotal;

    @Schema(description = "数据-版本")
    @TableField("version")
    private String version;

    @Schema(description = "数据-出错log，便于跟踪问题")
    @TableField("log")
    private String log;

    @Schema(description = "数据-是否锁定，每个目标详情，只能锁定一个版本。0：未锁定，1：锁定")
    @TableField("isLock")
    private Integer isLock;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;


}
