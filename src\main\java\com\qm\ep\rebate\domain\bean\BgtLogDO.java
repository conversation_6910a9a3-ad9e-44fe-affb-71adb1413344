package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 预算分解主表
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_log")
@Schema(description = "数据:预算分解主表")
public class BgtLogDO extends Model<BgtLogDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-classItem")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-政策主键")
    @TableField("policy_id")
    private String policyId;
    @Schema(description = "数据-细节主键")
    @TableField("detail_id")
    private String detailId;

    @Schema(description = "数据-01-支出，02-收入")
    @TableField("log_type")
    private String logType;

    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    @Schema(description = "数据-数据BGT点")
    @TableField("bgt_point")
    private BigDecimal bgtPoint;
    @Schema(description = "数据-数据BGT 金额")
    @TableField("bgt_amount")
    private BigDecimal bgtAmount;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
