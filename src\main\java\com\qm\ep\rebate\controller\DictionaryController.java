package com.qm.ep.rebate.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.DictChildDO;
import com.qm.ep.rebate.domain.bean.DictMainDO;
import com.qm.ep.rebate.domain.dto.DictChildDTO;
import com.qm.ep.rebate.domain.dto.DictInfoDTO;
import com.qm.ep.rebate.domain.vo.DictionaryVo;
import com.qm.ep.rebate.service.DictChildService;
import com.qm.ep.rebate.service.DictionaryService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dictionary")
@Tag(name = "字典")
public class DictionaryController extends BaseController {

    @Resource
    private DictionaryService dictionaryService;
    @Resource
    private DictChildService dictChildService;

    @Operation(summary = "获取字典", description = "[author: 10200571]")
    @GetMapping("/getAllDict")
    public JsonResultVo<Map<String, List<DictionaryVo>>> selectAllDictionary() {
        return dictionaryService.selectAllDictionary();
    }

    @Operation(summary = "获取字典分项", description = "[author: 10200571]")
    @PostMapping("/getPageByDict")
    public JsonResultVo<QmPage<DictChildDO>> getPageByDict(@RequestBody DictChildDTO dictChildDTO) {
        return dictionaryService.getPageByDict(dictChildDTO);
    }

    @Operation(summary = "删除", description = "[author: 10200571]")
    @GetMapping("/deleleByDicts")
    public JsonResultVo deleleByDicts(String dicts, String ncompany) {
        return dictionaryService.deleleByDicts(dicts, ncompany);
    }

    @Operation(summary = "修改", description = "[author: 10200571]")
    @PostMapping("/update")
    public JsonResultVo<DictMainDO> updateInfo(@RequestBody DictInfoDTO dictInfoDTO) {
        return dictionaryService.updateInfo(dictInfoDTO, getUserInfo());
    }

    @Operation(summary = "查询字典列表（子表）", description = "[author: 10200571]")
    @PostMapping("/getDictList")
    public JsonResultVo<List<DictChildDO>> selectDictChildSpaCLass(@RequestBody DictChildDTO tempDTO) {
        LoginKeyDO loginKey = getUserInfo();
        //定义查询构造器
        QmQueryWrapper<DictChildDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<DictChildDO> lambdaWrapper = queryWrapper.lambda();
        //字典项
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVdict())) {
            lambdaWrapper.eq(DictChildDO::getVdict, tempDTO.getVdict());
        }
        //公司
        lambdaWrapper.eq(DictChildDO::getNcompany, loginKey.getCompanyId());
        //停用标识
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getCstop())) {
            lambdaWrapper.eq(DictChildDO::getCstop, tempDTO.getCstop());
        }
        if (tempDTO.getVdicts() != null && !tempDTO.getVdicts().isEmpty()) {
            lambdaWrapper.in(DictChildDO::getVdict, tempDTO.getVdicts());
        }
        List<DictChildDO> list = dictChildService.list(queryWrapper);
        JsonResultVo<List<DictChildDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "导入", description = "[author: 10200571]")
    @PostMapping("/commit")
    public JsonResultVo<List<DictChildDO>> commit(@RequestParam MultipartFile file) {
        return dictionaryService.commit(file);

    }

    @Operation(summary = "导入后数据处理", description = "[author: 10200571]")
    @PostMapping("/commitAfter")
    public JsonResultVo<List<DictChildDO>> commitAfter(@RequestBody DictChildDTO tempDTO) {
        LoginKeyDO loginKey = getUserInfo();
        return dictionaryService.commitAfter(tempDTO, loginKey);
    }
}
