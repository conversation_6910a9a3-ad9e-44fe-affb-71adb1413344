package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:实体类-用户信息响应 DTO")
@Data
public class UserInfoResponseDTO {

    @Schema(description = "数据-云原生用户code")
    private String userCode;

    @Schema(description = "数据-云原生用户name")
    private String userName;

    @Schema(description = "数据-云原生用户id")
    private String id;

    @Schema(description = "数据-云原生用户登录名")
    private String loginName;

    @Schema(description = "数据-用户类型")
    private Integer userType;

    @Schema(description = "数据-使")
    private Integer enable;

    @Schema(description = "数据-删除")
    private Integer deleted;

    @Schema(description = "数据-名字")
    private String name;

    @Schema(description = "数据-移动")
    private String mobile;

    @Schema(description = "数据-电子邮件")
    private String email;

    @Schema(description = "数据-法典")
    private String code;

    @Schema(description = "数据-备注")
    private String remark;

    @Schema(description = "数据-员工 ID")
    private String staffId;

    @Schema(description = "数据-部门 ID")
    private String departmentId;

    @Schema(description = "数据-盐")
    private String salt;

    @Schema(description = "数据-密码")
    private String password;

    @Schema(description = "数据-性")
    private Integer gender;

    @Schema(description = "数据-生日")
    private String birthday;

    @Schema(description = "数据-昵称")
    private String nickName;

    @Schema(description = "数据-化身")
    private String avatar;

    @Schema(description = "数据-电话")
    private String tel;

    @Schema(description = "数据-微信")
    private String wechat;

    @Schema(description = "数据-QQ咨询")
    private String qq;

    @Schema(description = "数据-isadmin")
    private String isadmin;

    @Schema(description = "数据-停止时间")
    private String stopTime;

    @Schema(description = "数据-OpenID")
    private String openid;

    @Schema(description = "数据-移动preno")
    private String mobilepreno;

    @Schema(description = "数据-奥布吉德")
    private String objid;

    @Schema(description = "数据-businessid")
    private String businessid;

    @Schema(description = "数据-同步器")
    private String synchts;

    @Schema(description = "数据-idmid")
    private String idmid;

    @Schema(description = "数据-源")
    private String source;

    @Schema(description = "数据-叮叮当当 ID")
    private String dingTalkId;

    @Schema(description = "角色列表-主角色和子角色都在一个List里")
    private List<RoleDTO> roleVoList;

    @Schema(description = "数据-创建者")
    private String createdBy;

    @Schema(description = "数据-创建")
    private String created;

    @Schema(description = "数据-更新")
    private String updated;

    @Schema(description = "数据-更新者")
    private String updatedBy;

    @Schema(description = "数据-版本")
    private String version;

    @Schema(description = "数据-租户 ID")
    private String tenantId;

    @Schema(description = "数据-系统 ID")
    private String systemId;

    @Schema(description = "数据-用户类型")
    private String userTypes;

    @Schema(description = "数据-电子邮件")
    private String emails;

    @Schema(description = "数据-办公电话")
    private String officetel;

    @Schema(description = "数据-链接添加器")
    private String linkaddr;

    @Schema(description = "数据-证书类型")
    private String certType;

    @Schema(description = "数据-证书编号")
    private String certNo;

    @Schema(description = "数据-是员工")
    private String isStaff;

    @Schema(description = "数据-组织名称")
    private String orgName;

    @Schema(description = "数据-BU 名称")
    private String buName;

    @Schema(description = "数据-主要工作列表")
    private List<Object> mainJobList;

    @Schema(description = "数据-PT职位列表")
    private List<Object> ptJobList;

    @Schema(description = "数据-用户 ID")
    private String userId;

    @Schema(description = "数据-组织类型代码")
    private String orgTypeCode;

    @Schema(description = "数据-系统短名称")
    private String systemShortName;

    @Schema(description = "数据-系统名称")
    private String systemName;
}
