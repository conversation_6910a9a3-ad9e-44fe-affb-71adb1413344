package com.qm.ep.rebate.domain.dto.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 审批流数据传输对象
 *
 * <AUTHOR>
 * @date 2024/04/20
 */
@Schema(description = "数据:实体类-审批流数据传输对象")
@Data
@Builder
public class Deliver {
    /**
     * 业务主键，必填
     */
    @Schema(description = "数据-业务主键，必填")
    private String bizId;

    /**
     * 审核授权主键，必填
     */
    @Schema(description = "数据-审核授权主键，必填")
    private String approveId;
    /**
     * 审核类型，非必填
     */
    @Schema(description = "数据-审核类型，非必填")
    private String approveType;
    /**
     * 审核人，必填（节点名称为key，审核人域账号为value）
     */
    @Schema(description = "数据-审核人，必填（节点名称为key，审核人域账号为value）")
    private Map<String, String> assignactors;
    /**
     * 提交人域账号，必填
     */
    @Schema(description = "数据-提交人域账号，必填")
    private String startSubmitCode;
    /**
     * url，非必填
     */
    @Schema(description = "数据-数据url，非必填")
    private String url;


    /**
     * 待办任务实例流,非必填
     */
    @Schema(description = "数据-待办任务实例流,非必填")
    private String taskInstanceFlow;
    /**
     * 待办任务实例Code,非必填
     */
    @Schema(description = "数据-待办任务实例Code,非必填")
    private String taskInstanceCode;

    /**
     * 数据实体,非必填
     */
    @Schema(description = "数据-政策实体,非必填")
    private Object obj;

    /**
     * 模板名称,，必填
     */
    private String templateName;
}
