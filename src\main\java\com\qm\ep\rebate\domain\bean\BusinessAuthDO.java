package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("BUSINESS_AUTH")
@Schema(description = "数据:BusinessAuthDO对象")
public class BusinessAuthDO implements Serializable {
    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-数据源表名")
    @TableField("TABLE_ID")
    private String tableId;

    @Schema(description = "数据-停用标识")
    @TableField("USER_ID")
    private String userId;

    @Schema(description = "数据-停用时间")
    @TableField("TABLE_AUTH")
    private Integer tableAuth;

    @Schema(description = "数据-停用原因")
    @TableField("DATA_AUTH")
    private Integer dataAuth;

    @Schema(description = "数据-部门名称")
    @TableField("VDEPT_NAME")
    private String vdeptName;

    @Schema(description = "数据-人员名称")
    @TableField("VPERSON_NAME")
    private String vpersonName;
    @Schema(description = "数据-人员代码")
    @TableField("VPERSON_CODE")
    private String vpersoncode;

}
