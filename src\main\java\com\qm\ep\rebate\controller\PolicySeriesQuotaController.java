package com.qm.ep.rebate.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.PolicyDTO;
import com.qm.ep.rebate.domain.dto.SeriesQuotaDTO;
import com.qm.ep.rebate.domain.dto.TotalQuotaDTO;
import com.qm.ep.rebate.service.ClassItemYearService;
import com.qm.ep.rebate.service.PolicyService;
import com.qm.ep.rebate.service.SeriesQuotaService;
import com.qm.ep.rebate.service.TotalQuotaService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Tag(name ="车系额度")
@RestController
@RequestMapping("/seriesQuota")
public class PolicySeriesQuotaController extends BaseController {

    @Autowired
    private SeriesQuotaService seriesQuotaService;

    @Autowired
    private ClassItemYearService classItemYearService;

    @Autowired
    private TotalQuotaService totalQuotaService;

    @Autowired
    private PolicyService policyService;


    /**
     *使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<SeriesQuotaDO> save(@RequestBody SeriesQuotaDO tempDO){
        JsonResultVo<SeriesQuotaDO> resultObj = new JsonResultVo<>();
        SeriesQuotaDO seriesQuotaDO = seriesQuotaService.getById(tempDO.getId());
        if(BootAppUtil.isNullOrEmpty(tempDO.getSeries())){
            throw new QmException("车系不能为空");
        }
        if( BootAppUtil.isNullOrEmpty(tempDO.getQuota())){
            throw new QmException("车系额度不能为空");
        }
        boolean flag;

        if(seriesQuotaDO == null){
            // 新增
            if(!seriesQuotaService.check(tempDO).isEmpty()){
                throw new QmException("当前车系已存在，请勿重复输入！");
            }
            tempDO.setId(IdWorker.getIdStr());
            flag = seriesQuotaService.save(tempDO);
        }else{
            flag = seriesQuotaService.updateById(tempDO);
        }
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }
    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteByIds")
    public JsonResultVo<SeriesQuotaDO> deleteByIds(@RequestBody SeriesQuotaDO tempDO){
        JsonResultVo<SeriesQuotaDO> resultObj = new JsonResultVo<>();
        boolean flag = seriesQuotaService.removeByIds(tempDO.getIds());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }
    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/table")
    @DS(DataSourceType.W)
    public JsonResultVo<QmPage<SeriesQuotaDO>> table(@RequestBody SeriesQuotaDTO tempDTO){
        //定义查询构造器
        QmQueryWrapper<SeriesQuotaDO> queryWrapper = new QmQueryWrapper<>();
        QmPage<SeriesQuotaDO> list = seriesQuotaService.table(queryWrapper,tempDTO);
        JsonResultVo<QmPage<SeriesQuotaDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    /**
     *使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author: 10200571]")
    @PostMapping("/saveClassItemYear")
    public JsonResultVo<ClassItemYearDO> saveClassItem(@RequestBody ClassItemYearDO tempDO){
        JsonResultVo<ClassItemYearDO> resultObj = new JsonResultVo<>();
        if(BootAppUtil.isNullOrEmpty(tempDO.getClassItemCode())){
            throw new QmException("返利项目代码不能为空");
        }
        if( BootAppUtil.isNullOrEmpty(tempDO.getYear())){
            throw new QmException("年份不能为空");
        }
        boolean flag;
        // 新增
        if(!classItemYearService.check(tempDO).isEmpty()){
            throw new QmException("当前项目年份已存在，请勿重复输入！");
        }
        tempDO.setId(IdWorker.getIdStr());
        flag = classItemYearService.save(tempDO);
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }

    @Operation(summary = "返利项目下拉菜单", description = "[author: 10200571]")
    @PostMapping("/dictItemCode")
    public JsonResultVo<List<Map>> dictItemCode(){
        JsonResultVo<List<Map>>  resultObj = new JsonResultVo<>();
        List<Map> mapList = classItemYearService.DictItemCode();
        resultObj.setData(mapList);
        return resultObj;
    }


    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/totalQuotaTable")
    @DS(DataSourceType.W)
    public JsonResultVo<QmPage<TotalQuotaDO>> totalQuotaTable(@RequestBody TotalQuotaDTO tempDTO){
        //定义查询构造器
        QmQueryWrapper<TotalQuotaDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<TotalQuotaDO> lambdaWrapper = queryWrapper.lambda();

        if (!BootAppUtil.isNullOrEmpty(tempDTO.getClassItemCode())) {
            lambdaWrapper.eq(TotalQuotaDO::getClassItemCode, tempDTO.getClassItemCode());
        }
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getYear())) {
            lambdaWrapper.eq(TotalQuotaDO::getYear, tempDTO.getYear());
        }

        QmPage<TotalQuotaDO> list = totalQuotaService.table(queryWrapper,tempDTO);

        for (TotalQuotaDO tempDo : list.getItems()) {
            String classItemCode = tempDo.getClassItemCode();
            String year = tempDo.getYear();
            String series = tempDo.getSeries();
            Double realE = 0.0;
            QmQueryWrapper<PolicyDO> policyQueryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<PolicyDO> policyLambdaWrapper = policyQueryWrapper.lambda();
            policyLambdaWrapper.eq(PolicyDO::getClassItemCode, classItemCode);
            policyLambdaWrapper.eq(PolicyDO::getVfinishstate, "30");
            PolicyDTO policyDTO = new PolicyDTO();
            QmPage<PolicyDO> policyList = policyService.table(policyQueryWrapper, policyDTO);
            for (PolicyDO policyDo : policyList.getItems()) {
                String policyId = policyDo.getId();
                int seriesIndex = -1;
                int realEIndex = -1;
                List<ExecFormalCalcHistoryDO> historyList =  totalQuotaService.searchHistory(policyId,year);
                for (ExecFormalCalcHistoryDO historyDO : historyList) {
                    String historyId = historyDO.getId();
                    String sqlStructure = historyDO.getSqlStructure();
                    JSONObject sqlStructureObject = JSON.parseObject(sqlStructure);
                    JSONArray fields = sqlStructureObject.getJSONArray("fields");

                    for(int i = 1; i <= fields.size(); i++){
                        if(fields.getString(i - 1).equals("系列") ){
                            seriesIndex = i;
                        }
                        if(fields.getString(i - 1).equals("返利金额")){
                            realEIndex = i;
                        }
                    }
                    if(seriesIndex == -1 || realEIndex == -1){
                        break;
                    }
                    String realEField = "field" + String.valueOf(realEIndex);
                    String seriesField = "field" + String.valueOf(seriesIndex);
                    Double tempRealE = totalQuotaService.queryRealE(historyId, realEField,seriesField, series);
                    if(tempRealE != null){realE = realE + tempRealE;}
                }
            }
            tempDo.setRealityE(realE);
            tempDo.setDifference(tempDo.getQuota() - realE);
        }
        JsonResultVo<QmPage<TotalQuotaDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    /**
     *使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author: 10200571]")
    @PostMapping("/saveTotalQuota")
    public JsonResultVo<TotalQuotaDO> saveTotalQuota(@RequestBody TotalQuotaDO tempDO){

        Boolean flag;
        if(tempDO.getId() == null){
            tempDO.setId(IdWorker.getIdStr());
            flag = totalQuotaService.save(tempDO);
        }else {
            flag = totalQuotaService.updateById(tempDO);
        }
        JsonResultVo<TotalQuotaDO> resultObj = new JsonResultVo<>();
        if (flag) {
            resultObj.setData(tempDO);
        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }

}
