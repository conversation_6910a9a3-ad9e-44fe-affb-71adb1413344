package com.qm.ep.rebate.common.config;


import com.qm.ep.rebate.interceptor.AuthorizationInterceptor;
import com.qm.ep.rebate.interceptor.EpAuthorizationInterceptor;
import com.qm.ep.rebate.interceptor.LogInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * 请求拦截器配置
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Configuration
public class WebConfigMvc implements WebMvcConfigurer {

    @Bean
    public AuthorizationInterceptor getAuthorizationInterceptor() {
        return new AuthorizationInterceptor();
    }

    /**
     * 验签
     * @return
     */
    @Bean
    public EpAuthorizationInterceptor getEpAuthorizationInterceptor() {
        return new EpAuthorizationInterceptor();
    }

    @Bean
    public LogInterceptor getLogInterceptor() {
        return new LogInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 日志traceId
        registry.addInterceptor(getLogInterceptor()).addPathPatterns("/**");

        // 拦截解析Authorization 这是黑名单
        registry.addInterceptor(getAuthorizationInterceptor()).addPathPatterns(
                "/common/getUserInfo"
        )
        //这是白名单
//        .excludePathPatterns(
//                "/v3/**",
//                "/error",
//                "/public/**",
//                "/schedule/**",
//                "/swagger-ui/**"
//        )
        ;

        //ep 拦截解析Authorization 这是Ep的黑名单
        registry.addInterceptor(getEpAuthorizationInterceptor()).addPathPatterns(
                "/systemConfig/list"
        );
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 所有接口、是否发送Cookie、支持域、支持方法 等设置
        registry.addMapping("/**")
                .allowCredentials(true)
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE")
                .allowedHeaders("*")
                .exposedHeaders("*");
    }
}
