package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 云原生部门信息
 */
@Schema(description = "云原生部门信息")
@Data
public class IworkCenterDeptDTO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "部门编码")
    private String code;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "父部门id，0为没有父部门")
    private String parentId;



    @Schema(description = "父级部门树")
    private List<IworkCenterDeptDTO> parentTree;

    @Schema(description = "子部门")
    private List<IworkCenterDeptDTO> children;

}
