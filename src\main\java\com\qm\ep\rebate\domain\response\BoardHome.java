package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:")
@Data
public class BoardHome {
    /**
     * 异常总数（异常）
     */
    @Schema(description = "异常总数（异常）")
    private int totalExceptionCount;
    /**
     * 政策总数（全部）
     */
    @Schema(description = "政策总数（全部）")
    private int totalCount;
    /**
     * 日报日期
     */
    @Schema(description = "日报日期")
    private String sealDate;
    /**
     * 政策发布逾期栏（总览）
     */
    @Schema(description = "政策发布逾期栏（总览）")
    private BoardPolicyResponse publish;
    /**
     * 政策兑付逾期栏（总览）
     */
    @Schema(description = "政策兑付逾期栏（总览）")
    private BoardPolicyResponse pay;
    /**
     * 政策获取率（总览）
     */
    @Schema(description = "政策获取率（总览）")
    private BoardPolicyAcquireRateResponse boardPolicyAcquireRateResponse;

    /**
     * 政策结算栏（总览）
     */
    @Schema(description = "政策结算栏（总览）")
    private BoardPolicySettleResponse settle;

    /**
     * 政策发布逾期栏（详情）
     */
    @Schema(description = "政策发布逾期栏（详情）")
    private List<BoardPolicyDetailResponse> publishDetails;
    /**
     * 政策兑付逾期栏（详情）
     */
    @Schema(description = "政策兑付逾期栏（详情）")
    private List<BoardPolicyDetailResponse> payDetails;

    /**
     * 政策结算栏（详情）
     */
    @Schema(description = "政策结算栏（详情）")
    private List<BoardPolicySettleDetailResponse> settleDetails;

    /**
     * 政策获取率（详情）
     */
    @Schema(description = "政策获取率（详情）")
    private List<BoardPolicyAcquireResponse> acquireDetails;

}
