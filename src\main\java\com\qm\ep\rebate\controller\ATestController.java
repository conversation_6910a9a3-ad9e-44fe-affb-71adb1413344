package com.qm.ep.rebate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.dto.workbench.TriggerInfoDTO;
import com.qm.ep.rebate.domain.dto.workbench.TriggerTaskResponseDTO;
import com.qm.ep.rebate.enumerate.RebateCodeEnum;
import com.qm.ep.rebate.mapper.PolicyMapper;
import com.qm.ep.rebate.remote.http.EomServiceHttpClient;
import com.qm.ep.rebate.remote.request.EomRequest;
import com.qm.ep.rebate.remote.response.EomResponse;
import com.qm.ep.rebate.service.PolicyService;
import com.qm.ep.rebate.service.SystemConfigService;
import com.qm.ep.rebate.service.workbench.WorkbenchService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/aTest")
@Tag(name = "测试Controller", description = "[author: 10200571]")
public class ATestController extends BaseController {

    @Autowired
    private PolicyService policyService;

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private EomServiceHttpClient eomServiceHttpClient;


    @Autowired
    private WorkbenchService workbenchService;

    @Autowired
    private SystemConfigService systemConfigService;



    @Operation(summary = "创建维护信息任务")
    @PostMapping("/testCreatePolicyPublishTask")
    @SneakyThrows
    public JsonResultVo<String> testCreatePolicyPublishTask(String submitCode) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        String preCode = systemConfigService.getValueByCode("YSFJJTZ");

        TriggerInfoDTO triggerInfoDTO = TriggerInfoDTO.builder()
                .userCode(submitCode)
                .bizId(IdWorker.get32UUID())
                .description(RebateCodeEnum.XXWH.getDesc() + "-" + "测试创建维护信息任务")
                .build();
        List<TriggerInfoDTO> triggerInfoList = new ArrayList<>();
        triggerInfoList.add(triggerInfoDTO);

        TriggerTaskResponseDTO triggerTaskResponseDTO = workbenchService.triggerMiddleTask(preCode, RebateCodeEnum.XXWH.getCode(), triggerInfoList);
        if(triggerTaskResponseDTO.getTaskInstanceInfoList().isEmpty()){
            throw new Exception("创建维护信息任务失败");
        }
        ret.setData("testCreatePolicyPublishTask success");
        return ret;
    }

    @Operation(summary = "创建维护信息任务")
    @PostMapping("/getDimensionInfoByTargetCode")
    public JsonResultVo<EomResponse> getDimensionInfoByTargetCode() {
        JsonResultVo<EomResponse> ret = new JsonResultVo<>();
        EomResponse dimensionInfoByTargetCode = eomServiceHttpClient.getDimensionInfoByTargetCode(EomRequest.builder().targetCode("BZ2411000001").build());
        ret.setData(dimensionInfoByTargetCode);
        return ret;
    }

    @Operation(summary = "政策刷数据")
    @PostMapping("/flushPolicyData")
    public JsonResultVo<String> flushPolicyData() {
        JsonResultVo<String> ret = new JsonResultVo<>();
        LambdaQueryWrapper<PolicyDO> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.isNull(PolicyDO::getSubmitCode);
        List<PolicyDO> list1 = policyService.list(lambdaWrapper);
        for (PolicyDO policyDO : list1) {
            String createBy = policyDO.getCreateBy();
            LambdaQueryWrapper<PolicyDO> tempWrapper = new LambdaQueryWrapper<>();
            tempWrapper.eq(StringUtils.isNoneBlank(createBy), PolicyDO::getCreateBy, createBy);
            tempWrapper.isNotNull(PolicyDO::getSubmitCode);
            List<PolicyDO> list = policyService.list(tempWrapper);
            if (list.isEmpty()){
                continue;
            }
            PolicyDO tempPolicyDO = list.get(0);
            if (tempPolicyDO.getSubmitCode() != null){
                String submitCode = tempPolicyDO.getSubmitCode();
                String submitBy = tempPolicyDO.getSubmitBy();
                policyDO.setSubmitBy(submitBy);
                policyDO.setSubmitCode(submitCode);
                policyService.updateById(policyDO);
            }

        }
        return ret;
    }







}
