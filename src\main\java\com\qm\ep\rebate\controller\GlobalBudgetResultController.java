package com.qm.ep.rebate.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.GlobalBudgetDO;
import com.qm.ep.rebate.domain.bean.GlobalBudgetResultDO;
import com.qm.ep.rebate.domain.dto.GlobalBudgetResultDTO;
import com.qm.ep.rebate.domain.dto.GlobalBudgetResultFilterDTO;
import com.qm.ep.rebate.service.GlobalBudgetResultService;
import com.qm.ep.rebate.service.GlobalBudgetService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/globalBudgetResult")
@Tag(name = "总体预算编制结果")
public class GlobalBudgetResultController extends BaseController {

    @Resource
    private GlobalBudgetService globalBudgetService;

    @Resource
    private GlobalBudgetResultService globalBudgetResultService;

    @Operation(summary = "查询总体预算编制列表", description = "[author: 10200571]")
    @PostMapping("/list")
    public JsonResultVo<List<GlobalBudgetResultDO>> list(@RequestBody GlobalBudgetResultDTO globalBudgetResultDTO){
        JsonResultVo<List<GlobalBudgetResultDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<GlobalBudgetResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<GlobalBudgetResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(GlobalBudgetResultDO::getBudgetId, globalBudgetResultDTO.getBudgetId());
        List<GlobalBudgetResultDO> data = globalBudgetResultService.list(queryWrapper);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "根据模型计算结果组织预算编制数据", description = "[author: 10200571]")
    @PostMapping("/generateResult")
    public JsonResultVo<List<GlobalBudgetResultDTO>> generateResult(@RequestBody GlobalBudgetResultDTO globalBudgetResultDTO){
        JsonResultVo<List<GlobalBudgetResultDTO>> ret = new JsonResultVo<>();
        String historyId = "";
        if(StrUtil.isBlank(globalBudgetResultDTO.getBudget().getModelCalcVersion())) {
            GlobalBudgetDO budget = globalBudgetService.getById(globalBudgetResultDTO.getBudget().getId());
            historyId = budget.getModelCalcVersion();
        } else {
            historyId = globalBudgetResultDTO.getBudget().getModelCalcVersion();
        }
        List<GlobalBudgetResultDTO> data = globalBudgetResultService.generateResult(historyId, globalBudgetResultDTO.getFilter());
        ret.setData(data);
        return ret;
    }

    @Operation(summary = "组织总体预算明细查询过滤条件可选项", description = "[author: 10200571]")
    @PostMapping("/generateResultFilterData")
    public JsonResultVo<GlobalBudgetResultFilterDTO> generateResultFilterData(@RequestBody GlobalBudgetResultDTO globalBudgetResultDTO){
        JsonResultVo<GlobalBudgetResultFilterDTO> ret = new JsonResultVo<>();
        String historyId = "";
        if(StrUtil.isBlank(globalBudgetResultDTO.getBudget().getModelCalcVersion())) {
            GlobalBudgetDO budget = globalBudgetService.getById(globalBudgetResultDTO.getBudget().getId());
            historyId = budget.getModelCalcVersion();
        } else {
            historyId = globalBudgetResultDTO.getBudget().getModelCalcVersion();
        }
        GlobalBudgetResultFilterDTO data = globalBudgetResultService.generateResultFilterData(historyId);
        ret.setData(data);
        return ret;
    }

}
