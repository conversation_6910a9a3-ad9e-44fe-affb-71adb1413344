package com.qm.ep.rebate.domain.dto.structure;

import com.baomidou.mybatisplus.annotation.TableField;
import com.qm.ep.rebate.domain.bean.FormulaDO;
import com.qm.ep.rebate.domain.bean.PlanConditionsDO;
import com.qm.ep.rebate.domain.bean.PlanDetailDO;
import com.qm.ep.rebate.domain.bean.PlanMainDO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "计算方案")
public class PlanDTO {

    @Schema(description = "数据-主结构")
    private PlanMainDO main;
    /**
     * 公式内容
     */
    @Schema(description = "数据-公式内容")
    private List<FormulaDO> formulaContents;

    @Schema(description = "数据-详情")
    private List<PlanDetailDO> details;

    @Schema(description = "数据-筛选条件")
    private List<PlanConditionsDO> conditions;

    @Schema(description = "是否经销商可见")
    private String isVisible;

    @Schema(description = "是否政策结果")
    private String isPolicyResult;

}