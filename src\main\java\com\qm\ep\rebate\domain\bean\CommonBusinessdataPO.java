package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * 业务数据底表
 *
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Getter
@Setter
@TableName("common_businessdata")
public class CommonBusinessdataPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 底表名
     */
    @TableField("tablename")
    private String tablename;

    /**
     * 数据1
     */
    @TableField("field1")
    private String field1;

    /**
     * 数据
     */
    @TableField("field2")
    private String field2;

    /**
     * 数据
     */
    @TableField("field3")
    private String field3;

    /**
     * 数据
     */
    @TableField("field4")
    private String field4;

    /**
     * 数据
     */
    @TableField("field5")
    private String field5;

    /**
     * 数据
     */
    @TableField("field6")
    private String field6;

    /**
     * 数据
     */
    @TableField("field7")
    private String field7;

    /**
     * 数据
     */
    @TableField("field8")
    private String field8;

    /**
     * 数据
     */
    @TableField("field9")
    private String field9;

    /**
     * 数据
     */
    @TableField("field10")
    private String field10;

    /**
     * 数据
     */
    @TableField("field11")
    private String field11;

    /**
     * 数据
     */
    @TableField("field12")
    private String field12;

    /**
     * 数据
     */
    @TableField("field13")
    private String field13;

    /**
     * 数据
     */
    @TableField("field14")
    private String field14;

    /**
     * 数据
     */
    @TableField("field15")
    private String field15;

    /**
     * 数据
     */
    @TableField("field16")
    private String field16;

    /**
     * 数据
     */
    @TableField("field17")
    private String field17;

    /**
     * 数据
     */
    @TableField("field18")
    private String field18;

    /**
     * 数据
     */
    @TableField("field19")
    private String field19;

    /**
     * 数据
     */
    @TableField("field20")
    private String field20;

    /**
     * 数据
     */
    @TableField("field21")
    private String field21;

    /**
     * 数据
     */
    @TableField("field22")
    private String field22;

    /**
     * 数据
     */
    @TableField("field23")
    private String field23;

    /**
     * 数据
     */
    @TableField("field24")
    private String field24;

    /**
     * 数据
     */
    @TableField("field25")
    private String field25;

    /**
     * 数据
     */
    @TableField("field26")
    private String field26;

    /**
     * 数据
     */
    @TableField("field27")
    private String field27;

    /**
     * 数据
     */
    @TableField("field28")
    private String field28;

    /**
     * 数据
     */
    @TableField("field29")
    private String field29;

    /**
     * 数据
     */
    @TableField("field30")
    private String field30;

    /**
     * 数据
     */
    @TableField("field31")
    private String field31;

    /**
     * 数据
     */
    @TableField("field32")
    private String field32;

    /**
     * 数据
     */
    @TableField("field33")
    private String field33;

    /**
     * 数据
     */
    @TableField("field34")
    private String field34;

    /**
     * 数据
     */
    @TableField("field35")
    private String field35;

    /**
     * 数据
     */
    @TableField("field36")
    private String field36;

    /**
     * 数据
     */
    @TableField("field37")
    private String field37;

    /**
     * 数据
     */
    @TableField("field38")
    private String field38;

    /**
     * 数据
     */
    @TableField("field39")
    private String field39;

    /**
     * 数据
     */
    @TableField("field40")
    private String field40;

    /**
     * 数据
     */
    @TableField("field41")
    private String field41;

    /**
     * 数据
     */
    @TableField("field42")
    private String field42;

    /**
     * 数据
     */
    @TableField("field43")
    private String field43;

    /**
     * 数据
     */
    @TableField("field44")
    private String field44;

    /**
     * 数据
     */
    @TableField("field45")
    private String field45;

    /**
     * 数据
     */
    @TableField("field46")
    private String field46;

    /**
     * 数据
     */
    @TableField("field47")
    private String field47;

    /**
     * 数据
     */
    @TableField("field48")
    private String field48;

    /**
     * 数据
     */
    @TableField("field49")
    private String field49;

    /**
     * 数据
     */
    @TableField("field50")
    private String field50;

    /**
     * 数据
     */
    @TableField("field51")
    private String field51;

    /**
     * 数据
     */
    @TableField("field52")
    private String field52;

    /**
     * 数据
     */
    @TableField("field53")
    private String field53;

    /**
     * 数据
     */
    @TableField("field54")
    private String field54;

    /**
     * 数据
     */
    @TableField("field55")
    private String field55;

    /**
     * 数据
     */
    @TableField("field56")
    private String field56;

    /**
     * 数据
     */
    @TableField("field57")
    private String field57;

    /**
     * 数据
     */
    @TableField("field58")
    private String field58;

    /**
     * 数据
     */
    @TableField("field59")
    private String field59;

    /**
     * 数据
     */
    @TableField("field60")
    private String field60;

    /**
     * 数据
     */
    @TableField("field61")
    private String field61;

    /**
     * 数据
     */
    @TableField("field62")
    private String field62;

    /**
     * 数据
     */
    @TableField("field63")
    private String field63;

    /**
     * 数据
     */
    @TableField("field64")
    private String field64;

    /**
     * 数据
     */
    @TableField("field65")
    private String field65;

    /**
     * 数据
     */
    @TableField("field66")
    private String field66;

    /**
     * 数据
     */
    @TableField("field67")
    private String field67;

    /**
     * 数据
     */
    @TableField("field68")
    private String field68;

    /**
     * 数据
     */
    @TableField("field69")
    private String field69;

    /**
     * 数据
     */
    @TableField("field70")
    private String field70;

    /**
     * 数据
     */
    @TableField("field71")
    private String field71;

    /**
     * 数据
     */
    @TableField("field72")
    private String field72;

    /**
     * 数据
     */
    @TableField("field73")
    private String field73;

    /**
     * 数据
     */
    @TableField("field74")
    private String field74;

    /**
     * 数据
     */
    @TableField("field75")
    private String field75;

    /**
     * 数据
     */
    @TableField("field76")
    private String field76;

    /**
     * 数据
     */
    @TableField("field77")
    private String field77;

    /**
     * 数据
     */
    @TableField("field78")
    private String field78;

    /**
     * 数据
     */
    @TableField("field79")
    private String field79;

    /**
     * 数据
     */
    @TableField("field80")
    private String field80;

    /**
     * 数据
     */
    @TableField("field81")
    private String field81;

    /**
     * 数据主键
     */
    @TableField("fieldId")
    private String fieldId;


}
