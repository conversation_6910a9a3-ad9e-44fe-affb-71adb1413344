package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * 
 *
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
@Schema(description = "数据:实体类-    ")
@Getter
@Setter
@TableName("bgt_series_sale_count")
public class BgtSeriesSaleCountPO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 年
     */
    @Schema(description = "数据-年")
    @TableField("sale_year")
    private String saleYear;

    /**
     * 月
     */
    @Schema(description = "数据-月")
    @TableField("sale_month")
    private String saleMonth;

    /**
     * 车系
     */
    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    /**
     * 销量（aak/std）
     */
    @Schema(description = "数据-销量（aak/std）")
    @TableField("sale_count")
    private Long saleCount;

    /**
     * 销售类型（00-aak，01-std, 99-平均指导价手动录入）
     */
    @Schema(description = "数据-销售类型（00-aak，01-std, 99-平均指导价手动录入）")
    @TableField("sale_type")
    private String saleType;

    /**
     * 版本号
     */
    @Schema(description = "数据-版本号")
    @TableField("version")
    private String version;

    /**
     * 使用状态（0-未使用，1-正在使用）
     */
    @Schema(description = "数据-使用状态（0-未使用，1-正在使用）")
    @TableField("use_status")
    private String useStatus;

    /**
     * 创建者
     */
    @Schema(description = "数据-创建者")
    @TableField("CREATEBY")
    private String createby;

    /**
     * 创建日期
     */
    @Schema(description = "数据-创建日期")
    @TableField("CREATEON")
    private LocalDateTime createon;

    /**
     * 更新者
     */
    @Schema(description = "数据-更新者")
    @TableField("UPDATEBY")
    private String updateby;

    /**
     * 更新时间
     */
    @Schema(description = "数据-更新时间")
    @TableField("UPDATEON")
    private LocalDateTime updateon;

    /**
     * 时间戳
     */
    @Schema(description = "数据-时间戳")
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;


}
