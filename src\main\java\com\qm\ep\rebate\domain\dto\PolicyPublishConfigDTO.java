package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
*
* 政策发布配置
*
* <AUTHOR>
* @since 2023-08-28
*/
@Schema(description = "政策发布配置")
@Data
public class PolicyPublishConfigDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-政策ID")
    private String policyId;
    @Schema(description = "数据-经销商代码")
    private String dealerCode;
    @Schema(description = "数据-经销商名称")
    private String dealerName;

    @Schema(description = "数据-合作伙伴 1全部2指定3不指定")
    private String  vapplyorg;

    @Schema(description = "数据-年月")
    private String  yearMonth;
}