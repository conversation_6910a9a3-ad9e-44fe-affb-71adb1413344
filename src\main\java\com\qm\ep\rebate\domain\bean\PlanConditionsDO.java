package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("PLANCONDITIONS")
@Schema(description = "数据:计算方案筛选条件表对象")
public class PlanConditionsDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-计算方案主表主键")
    @TableField("PLANID")
    private String planId;

    @Schema(description = "数据-字段名")
    @TableField("FIELDNAME")
    private String fieldName;

    @Schema(description = "数据-字段来源表")
    @TableField("FIELDNAMEFROM")
    private String fieldNameFrom;

    @Schema(description = "数据-运算方式")
    @TableField("CALTYPE")
    private String calType;

    @Schema(description = "数据-关联逻辑")
    @TableField("CONTYPE")
    private String conType;

    @Schema(description = "数据-筛选方式")
    @TableField("FILTRATETYPE")
    private String filtrateType;

    @Schema(description = "数据-筛选条件字段")
    @TableField("FILTRATE")
    private String filtrate;

    @Schema(description = "数据-筛选条件字段来源表")
    @TableField("FILTRATEFROM")
    private String filtrateFrom;

    @Schema(description = "数据-前括号")
    @TableField("BRACKETBEFORE")
    private String bracketBefore;

    @Schema(description = "数据-后括号")
    @TableField("BRACKETAFTER")
    private String bracketAfter;

    @Schema(description = "数据-排序")
    @TableField("sort")
    private Integer sort;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;

}
