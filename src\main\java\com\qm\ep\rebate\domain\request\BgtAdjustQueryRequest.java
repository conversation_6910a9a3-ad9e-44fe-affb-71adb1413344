package com.qm.ep.rebate.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "数据:数据BGT 调整查询请求")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BgtAdjustQueryRequest {

    /**
     * 申请单主表id
     */
    @Schema(description = "申请单主表id")
    private String applyMainId;


    /**
     * 生成的新的版本的预算分解的主表id
     */
    @Schema(description = "生成的新的版本的预算分解的主表id")
    private String breakdownMainId;

    @Schema(description = "商业主键")
    private String bizId;



}
