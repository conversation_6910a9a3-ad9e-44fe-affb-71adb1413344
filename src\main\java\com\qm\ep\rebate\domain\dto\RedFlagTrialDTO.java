package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "数据:红旗伙伴试算对象")
@ToString(callSuper = true)
public class RedFlagTrialDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-红旗伙伴试算主表ID")
    private String id;

    @Schema(description = "数据-政策主键")
    private String policyId;

    @Schema(description = "数据-政策名称")
    private String policyName;

    @Schema(description = "数据-红旗伙伴试算配置名称")
    private String factorName;

    @Schema(description = "数据-计奖")
    private String reward;

    @Schema(description = "数据-计量")
    private String metering;

    @Schema(description = "数据-数据源取值")
    private List<String> dataSource;

    @Schema(description = "数据-汇总对象")
    private List<String> summaryObject;

    @Schema(description = "数据-输出对象")
    private List<OutputFieldDTO> conValueObject;

    @Schema(description = "数据-关联数据源")
    private List<RedFlagTrialJoinDTO> dependents;

    @Schema(description = "数据-筛选条件列表")
    private List<RedFlagTrialConditionsDTO> filterCondition;

    @Schema(description = "数据-创建者")
    private String createby;

    @Schema(description = "数据-创建")
    private String createon;

    @Schema(description = "数据-更新作者")
    private String updateby;

    @Schema(description = "数据-更新")
    private String updateon;

}

