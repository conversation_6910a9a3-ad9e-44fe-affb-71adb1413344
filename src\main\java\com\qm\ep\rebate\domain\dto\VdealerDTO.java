package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *  
 * 经销商基础信息（服务冗余存储）
 *  
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
@Schema(description = "数据:经销商基础信息（服务冗余存储）")
@Data
public class VdealerDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-npolicyid")
    private String npolicyid;
    @Schema(description = "数据-经销商代码")
    private String vdealer;
    @Schema(description = "数据-经销商名称")
    private String vdealername;
    @Schema(description = "数据-经销商简称")
    private String vdealershortname;
    @Schema(description = "数据-整车销售组织")
    private String norgan;
    @Schema(description = "数据-品牌")
    private String brand;
    @Schema(description = "数据-年月")
    private String yearMonth;
    @Schema(description = "数据-经销商类型")
    private List<String> vdealerType;
    @Schema(description = "数据-经销商状态")
    private List<String> vdealerStatus;
    @Schema(description = "数据-代理制标识")
    private String agentFlag;
    @Schema(description = "大区")
    private List<String> area;
    @Schema(description = "省份")
    private List<String> province;
    @Schema(description = "城市")
    private List<String> city;


}