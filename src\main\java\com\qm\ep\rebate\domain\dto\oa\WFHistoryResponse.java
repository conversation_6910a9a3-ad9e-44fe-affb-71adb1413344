package com.qm.ep.rebate.domain.dto.oa;

import lombok.Data;

import java.util.List;

@Data
public class WFHistoryResponse {
    /**
     * 审批节点列表
     */
    private List<Rate> rates;

    /**
     * 审批节点实体类
     */
    @Data
    public static class Rate {
        /**
         * 节点级别
         */
        private String level;

        /**
         * 节点标题
         */
        private String title;

        /**
         * 审批人
         */
        private String approver;

        /**
         * 审批时间
         */
        private String time;

        /**
         * 审批状态
         */
        private String state;

        /**
         * 备注
         */
        private String remark;

        /**
         * 链接
         */
        private String link;

        /**
         * 开始时间
         */
        private String startTime;

        /**
         * 审批人代码
         */
        private String approvercode;

        /**
         * 节点ID
         */
        private String id;

        /**
         * 任务定义键
         */
        private String taskDefinitionKey;

        /**
         * 持续时间
         */
        private int durationTime;

        /**
         * 子节点
         */
        private Object childNode;

        /**
         * 描述
         */
        private String describe;

        /**
         * 子流程
         */
        private Object subProcess;

        /**
         * 抄送任务
         */
        private Object copyTask;

        /**
         * 签名
         */
        private Object autograph;

        /**
         * 注解
         */
        private Object annotations;

        /**
         * 自定义描述
         */
        private String customDesc;
    }
}
