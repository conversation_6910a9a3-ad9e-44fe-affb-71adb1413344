package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.FunctionEnum;
import com.qm.ep.rebate.enumerate.NullValueEnum;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("COMMON_FORMULA")
@Schema(description = "通用公式对象")
public class FormulaDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "数据-对象ID")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "数据-对象类型")
    @TableField("objectType")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "数据-公式别名")
    @TableField("`name`")
    private String name;

    @Schema(description = "数据-公式内容列表")
    @TableField(exist = false)
    private List<FormulaItemDO> items;

    @Schema(description = "数据-保留小数位")
    @TableField("`decimal`")
    private Integer decimal;

    @Schema(description = "数据-分母为零时计算规则")
    @TableField("rule")
    private NullValueEnum rule;

    @Schema(description = "数据-取值类型")
    @TableField("fetchType")
    private FunctionEnum fetchType;

    @Schema(description = "数据-创建时间")
    @TableField(value = "createOn", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-创建者")
    @TableField(value = "createBy", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-更新")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "updateBy", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-时间戳")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;

}
