package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "数据:实体类-完成任务 DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompleteTaskDTO {

    @Schema(description = "数据-接口幂等参数")
    private String requestId;

    @Schema(description = "数据-能力中心ClientId")
    private String clientId;

    @Schema(description = "数据-当前业务单元编码")
    private String bizUnitCode;

    @Schema(description = "数据-当前事件编码")
    private String eventCode;

    /**
     * 当前业务单元任务实例编码, String(64),
     * (currentTaskInstanceCode-优先查找,
     * currentBizId-其次查找, 必填其中之一
     * 查询任务实例并关闭)
     */
    @Schema(description = "数据-当前业务单元任务实例编码")
    private String currentTaskInstanceCode;

    /**
     * 当前业务单元,能力中心侧业务ID, String(64),
     * (currentTaskInstanceCode-优先查找,
     * currentBizId-其次查找, 必填其中之一查询
     * 任务实例并关闭)
     */
    @Schema(description = "数据-能力中心侧业务ID")
    private String currentBizId;

    /**
     * 任务关闭人云原生用户code, String(64), 不传默认system
     */
    @Schema(description = "数据-任务关闭人")
    private String userCode;

    /**
     * 任务关闭人云原生用户name, String(64), 不传默认system
     */
    @Schema(description = "数据-任务关闭人")
    private String userName;
}
