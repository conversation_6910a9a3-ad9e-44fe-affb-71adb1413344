package com.qm.ep.rebate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.NotificationUserDO;
import com.qm.ep.rebate.domain.dto.NotificationDTO;
import com.qm.ep.rebate.service.NotificationUserService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/notificationUser")
@Tag(name = "通知用户")
public class NotificationUserController extends BaseController {

    @Resource
    private NotificationUserService notificationUserService;

    @Operation(summary = "获取通知的直接接收人", description = "[author: 10200571]")
    @PostMapping("/getReceiverUsers")
    public JsonResultVo<List<NotificationUserDO>> getReceiverUsers(@RequestBody NotificationDTO notificationDTO){
        JsonResultVo<List<NotificationUserDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<NotificationUserDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<NotificationUserDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(NotificationUserDO::getNotifyId, notificationDTO.getId());
        lambdaWrapper.isNull(NotificationUserDO::getDeliverUserCode);
        List<NotificationUserDO> data = notificationUserService.list(lambdaWrapper);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }
}
