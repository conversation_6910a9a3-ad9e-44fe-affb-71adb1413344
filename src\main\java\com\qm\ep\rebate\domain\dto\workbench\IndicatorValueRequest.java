package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 指标实际值同步 请求参数类
 *
 * <AUTHOR>
 * @since 2024-03-04 10:35
 */
@Schema(description = "数据:实体类-指标实际值同步 请求参数类")
@Data
@Builder
public class IndicatorValueRequest {
    /**
     * 所属部门代码
     */
    @Schema(description = "数据-所属部门代码")
    private String departmentCode;

    /**
     * 周报还是月报标识（0-月报，1-周报）
     */
    @Schema(description = "数据-周报还是月报标识")
    private String monthsWeekFlag;

    /**
     * 年周
     */
    @Schema(description = "数据-年周")
    private String yearWeek;

    /**
     * 指标所属年月（yyyy-MM）
     */
    @Schema(description = "数据-指标所属年月（yyyy-MM）")
    private String yearMonth;

    /**
     * 指标列表
     */
    @Schema(description = "数据-指标列表")
    private List<IndicatorValueScoreDTO> indicatorList;
}
