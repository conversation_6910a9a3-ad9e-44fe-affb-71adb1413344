package com.qm.ep.rebate.domain.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 项目名称：yiqi-project
 * 类 名 称：PolicyRebateDTO
 * 类 描 述：返利数据推送--售后服务Dto
 * 创建时间：2024/2/22 下午12:56
 * 创 建 人：cuihaochuan
 */

@Data
@Schema(description = "数据:政策返利")
public class PolicyRebateDTO {

    @Schema(description = "数据-时间，不传默认为上个月 yyyy-MM")
    private String createDate;

    @Schema(description = "数据-所属code，HB:华北区 DB:东北区 ZN:中南区 HN:华南区 HD:华东区 XB:西部区")
    private String ownerCode;

    @Schema(description = "数据-统计维度，1 大区 2 经销商，当前版本传1即可")
    private Integer type;

    @Schema(description = "数据-政策获取率 没有传-")
    private String policyRate;

    @Schema(description = "数据-大区排行 没有传0")
    private Integer policyRank;

    @Schema(description = "数据-政策获取率 没有传-")
    private String totalAmount;

    @Schema(description = "数据-车系明细")
    private List<PolicyRebateDetailDTO> seriesList;

    @Schema(description = "数据-返利明细")
    private List<PolicyRebateDetailDTO> rebateList;

    @Schema(description = "数据:实体类-扩展参数")
    @Data
    public static class ExtendParams{
        @Schema(description = "数据-返利金额")
        private String totalAmount;

        @Schema(description = "数据-税率")
        private String policyRate;

        @Schema(description = "数据-大区排名")
        private Integer policyRank;

        @Schema(description = "数据-时间，不传默认为上个月 yyyy-MM")
        private String createDate;

        @Schema(description = "数据-所属code，HB:华北区 DB:东北区 ZN:中南区 HN:华南区 HD:华东区 XB:西部区")
        private String ownerCode;

        @Schema(description = "数据-统计维度，1 大区 2 经销商，当前版本传1即可")
        private Integer type;
    }
}
