package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("notification_user")
@Schema(description = "数据:通知人对象")
public class NotificationUserDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-主表ID")
    @TableField("notifyId")
    private String notifyId;

    @Schema(description = "数据-接收人 组织代码")
    @TableField("receiverOrgCode")
    private String receiverOrgCode;

    @Schema(description = "数据-接收人 唯一用户代码")
    @TableField("receiverUserCode")
    private String receiverUserCode;

    @Schema(description = "数据-接收人 用户名称")
    @TableField("receiverUserName")
    private String receiverUserName;

    @Schema(description = "数据-转交人 组织代码")
    @TableField("deliverOrgCode")
    private String deliverOrgCode;

    @Schema(description = "数据-转交人 唯一用户代码")
    @TableField("deliverUserCode")
    private String deliverUserCode;

    @Schema(description = "数据-转交人 用户名称")
    @TableField("deliverUserName")
    private String deliverUserName;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
