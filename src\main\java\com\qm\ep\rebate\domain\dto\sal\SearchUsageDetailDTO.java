package com.qm.ep.rebate.domain.dto.sal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;


/**
 *  
 * 查询使用明细
 *
 *
 * <AUTHOR>
 * @since 2021-03-04
 */
@Schema(description = "数据:查询使用明细")
@Data
public class SearchUsageDetailDTO extends JsonParamDto {


    @Schema(description = "数据-公司ID")
    private String ncompanyid;

    @Schema(description = "数据-单位标识：1客户/2经销商")
    private String vunitflag;

    @Schema(description = "数据-客户ID")
    private String ncustomerid;

    @Schema(description = "数据-信用账户")
    private String vcreditacct;

    @Schema(description = "数据-信用账户名称")
    private String vcreditacctext;


    @Schema(description = "数据-金额")
    private Double nbalance;


    @Schema(description = "数据-操作员ID")
    private String noperatorid;

    @Schema(description = "数据-操作员编码")
    private String voperatorcode;

    @Schema(description = "数据-操作员名称")
    private String voperatorname;

    @Schema(description = "数据-操作日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date doperatedate;


    @Schema(description = "数据-备注")
    private String vremark;

    @Schema(description = "数据-单位ID")
    private String customer;

    @Schema(description = "数据-信用账户")
    private String[] credit;

    @Schema(description = "数据-组织机构")
    private String norgan;

    @Schema(description = "数据-起始日期")
    private String begin;

    @Schema(description = "数据-截止日期")
    private String end;

    @Schema(description = "数据-经销商代码")
    private String vdealer;

    @Schema(description = "数据-经销商代码名称")
    private String vdealertext;

    @Schema(description = "数据-发票Id")
    private String ninvoiceid;

    @Schema(description = "数据-发票号")
    private String vinvoiceno;

    @Schema(description = "数据-系列")
    private String vpdtseries;

    @Schema(description = "数据-经销商id")
    private String ndealerid;

}
