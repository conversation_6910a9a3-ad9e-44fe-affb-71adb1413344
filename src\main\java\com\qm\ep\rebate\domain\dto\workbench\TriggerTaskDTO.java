package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "数据:实体类-触发任务 DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TriggerTaskDTO {

    @Schema(description = "数据-接口幂等参数")
    private String requestId;

    @Schema(description = "数据-能力中心ClientId")
    private String clientId;

    @Schema(description = "数据-当前业务单元编码")
    private String bizUnitCode;

    @Schema(description = "数据-当前事件编码")
    private String eventCode;

    /**
     * 上游业务单元任务实例编码, String(64), (起始任务-可空;),
     * (中间任务:preTaskInstanceCode-优先查找, preBizId-其
     * 次查找,必填其中之一查询任务流实例山下文并创建当前业务单元任
     * 务实例);
     */
    @Schema(description = "数据-上游业务单元任务实例编码")
    private String preTaskInstanceCode;

    /**
     * (起始任务-可空;), (中间任务:preTaskInstanceCode-优先查找,
     * preBizId-其次查找, 必填其中之一查询任务流实例山下文并创建当前
     * 业务单元任务实例);
     */
    @Schema(description = "数据-上游业务单元,能力中心侧业务ID")
    private String preBizId;

    /**
     * 目标云原生用户集合,如果目标业务单元配置了<触发器用户>,则目标
     * 用户必须在<触发器用户>中; 如果目标业务单元没有配置<触发器用户>,
     * 则目标用户必须在目标业务单元所在的<角色用户>中;(触发器-将当前节
     * 点任务指派给目标用户;)
     */
    @Schema(description = "数据-目标云原生用户集合")
    private List<TriggerInfoDTO> triggerInfoList;

    /**
     * 任务创建人,云原生用户code, String(64), 不传默认system
     */
    @Schema(description = "数据-任务创建人")
    private String userCode;

    /**
     * 任务创建人,云原生用户name, String(64), 不传默认system
     */
    @Schema(description = "数据-任务创建人")
    private String userName;

}
