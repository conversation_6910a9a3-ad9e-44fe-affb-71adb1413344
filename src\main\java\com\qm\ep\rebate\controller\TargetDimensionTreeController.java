package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.TargetTreeMainDO;
import com.qm.ep.rebate.domain.dto.TargetDimensionTreeDTO;
import com.qm.ep.rebate.domain.vo.TargetDimensionTreeVO;
import com.qm.ep.rebate.service.TargetDimensionTreeService;
import com.qm.ep.rebate.service.TargetTreeMainService;
import com.qm.ep.rebate.infrastructure.util.SqlUtils;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * Controller
 * 政策基础信息表JsonResultVo
 *
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Tag(name = "指标维度树")
@RestController
@RequestMapping("/targetDimensionTree")
public class TargetDimensionTreeController extends BaseController {

    @Autowired
    private TargetDimensionTreeService targetDimensionTreeService;
    @Autowired
    private TargetTreeMainService targetTreeMainService;

    @Operation(summary = "获取指标维度树", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<TargetDimensionTreeVO>> table(@RequestBody TargetDimensionTreeDTO treeDTO) {
        JsonResultVo<QmPage<TargetDimensionTreeVO>> result = new JsonResultVo<>();
        QmPage<TargetDimensionTreeVO> page = new QmPage<>();
        if(StringUtils.isBlank(treeDTO.getOrg())){
            result.setData(page);
            return result;
        }
        QmQueryWrapper<TargetTreeMainDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<TargetTreeMainDO> targetTreeMainWrapper = queryWrapper.lambda();
        targetTreeMainWrapper.eq(TargetTreeMainDO::getOrg, treeDTO.getOrg())
                .eq(TargetTreeMainDO::getLevel, 1)
                .like(StringUtils.isNotBlank(treeDTO.getTargetName()),
                        TargetTreeMainDO::getTargetName, SqlUtils.escape(treeDTO.getTargetName()))
                .orderByDesc(TargetTreeMainDO::getCreateOn);
        //查询数据，使用table函数。
        QmPage<TargetTreeMainDO> pageDO = targetTreeMainService.table(queryWrapper, treeDTO);
        page.setCurrentPage(pageDO.getCurrentPage());
        page.setPageSize(pageDO.getPageSize());
        page.setTotal(pageDO.getTotal());
        page.setTotalPages(pageDO.getTotalPages());

        List<TargetTreeMainDO> items = pageDO.getItems();
        List<TargetDimensionTreeVO> list = new ArrayList<>();
        if(CollUtil.isNotEmpty(items)){
            items.forEach(mainDO -> {
                TargetDimensionTreeVO treeVO = new TargetDimensionTreeVO(mainDO);
                targetDimensionTreeService.setDimensionChildren(treeVO, mainDO.getDimensionList());

                LambdaQueryWrapper<TargetTreeMainDO> targetTreeChildrenWrapper = new QmQueryWrapper<TargetTreeMainDO>().lambda();
                targetTreeChildrenWrapper.like(TargetTreeMainDO::getTrace, mainDO.getId());
                List<TargetTreeMainDO> children = targetTreeMainService.list(targetTreeChildrenWrapper);
                targetDimensionTreeService.setChildren(treeVO, children);
                list.add(treeVO);
            });
        }
        page.setItems(list);
        result.setData(page);
        return result;
    }

}
