package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.dto.agent.in.Sald002GetInDTO;
import com.qm.ep.rebate.domain.dto.agent.in.GetSettleListInDTO;
import com.qm.ep.rebate.domain.dto.agent.in.Sald002ManageListInDTO;
import com.qm.ep.rebate.domain.dto.agent.out.Sald002ListOutDTO;
import com.qm.ep.rebate.domain.dto.agent.out.Sald001ListOutDTO;
import com.qm.ep.rebate.domain.dto.agent.out.Sald002OutDTO;
import com.qm.ep.rebate.domain.request.AgentSettlementMainRequest;
import com.qm.ep.rebate.domain.vo.sal.AgentSettlementMainVO;
import com.qm.ep.rebate.remote.feign.SalFeignClient;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Tag(name = "报账单")
@Slf4j
@RestController
@RequestMapping("/reimburse")
public class ReimburseController {

    @Autowired
    private SalFeignClient salFeignClient;

    @Operation(summary = "查询报账单列表", description = "[author: 10200571]")
    @PostMapping("/getList")
    public JsonResultVo<QmPage<Sald002ListOutDTO>> getList(@RequestBody Sald002ManageListInDTO inDTO) {
        return salFeignClient.manageGetList(inDTO);
    }

    @Operation(summary = "查询未占用的结算单", description = "[author: 10200571]")
    @PostMapping("/getSettleList")
    public JsonResultVo<List<Sald001ListOutDTO>> getSettleList(@RequestBody GetSettleListInDTO inDTO) {
        return salFeignClient.getSettleList(inDTO);
    }

    @Operation(summary = "按id查询报账单详情", description = "[author: 10200571]")
    @PostMapping("/getById")
    public JsonResultVo<Sald002OutDTO> getById(@RequestBody Sald002GetInDTO inDTO){
        return salFeignClient.manageGetById(inDTO);
    }

    @Operation(summary = "查询结算单明细", description = "[author: 10200571]")
    @PostMapping("/getDetailById")
    public JsonResultVo<AgentSettlementMainVO> getDetailById(@RequestBody AgentSettlementMainRequest agentSettlementMainRequest) {
        return salFeignClient.detailBySald001Id(agentSettlementMainRequest);
    }

    @Operation(summary = "获取服务器地址", description = "[author: 10200571]")
    @GetMapping("/getServerAddress")
    public JsonResultVo<String> getServerAddress() {
        return salFeignClient.getServerAddress();
    }

}
