package com.qm.ep.rebate.domain.dto.structure;

import com.qm.ep.rebate.domain.bean.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-数据DTO 部分")
@Data
public class SectionDTO {

    @Schema(description = "数据-主要")
    private FormulaMainDO main;
    @Schema(description = "数据-详")
    private List<FormulaDetailDO> details;
    @Schema(description = "数据-加入")
    private List<FormulaSectionJoinDO> joins;
    @Schema(description = "数据-条件")
    private List<FormulaSectionMainConditionDO> conditions;
    @Schema(description = "数据-作业")
    private List<FormulaSectionAssignmentDO> assignments;

}