package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.service.EntryAccountDataService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "入账计划任务")
@RestController
@RequestMapping("/policyConfigSchedule")
@Slf4j
public class EntryAccountScheduleController {

    @Resource
    EntryAccountDataService entryAccountDataService;

    @Operation(summary = "入账补偿", description = "[author: ********]")
    @PostMapping("/automaticAccount")
    public JsonResultVo<Integer> automaticAccount() {
        JsonResultVo<Integer> result = new JsonResultVo<>();
        entryAccountDataService.automaticAccount();
        return result;
    }

    @Operation(summary = "EP补偿", description = "[author: ********]")
    @PostMapping("/autoAccountToEP")
    public JsonResultVo<Integer> autoAccountToEP() {
        JsonResultVo<Integer> result = new JsonResultVo<>();
        entryAccountDataService.autoAccountToEP();
        return result;
    }

}
