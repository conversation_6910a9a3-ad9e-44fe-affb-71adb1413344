package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.ep.rebate.enumerate.DateFunctionEnum;
import com.qm.ep.rebate.enumerate.FormulaItemObjectTypeEnum;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("COMMON_FORMULA_ITEM")
@Schema(description = "通用公式内容对象")
public class FormulaItemDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-公式ID")
    @TableField("formulaId")
    private String formulaId;

    @Schema(description = "数据-值")
    @TableField("value")
    private String value;

    @Schema(description = "数据-类型")
    @TableField("`type`")
    private FormulaItemObjectTypeEnum type;

    @Schema(description = "数据-计算对象名称")
    @TableField("tableName")
    private String tableName;

    @Schema(description = "数据-计算对象字段名")
    @TableField("tableField")
    private String tableField;

    @Schema(description = "数据-日期规则")
    @TableField("rule")
    private DateFunctionEnum rule;

    @Schema(description = "数据-创建时间")
    @TableField(value = "createOn", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-创建者")
    @TableField(value = "createBy", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-更新")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "updateBy", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-时间戳")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

}
