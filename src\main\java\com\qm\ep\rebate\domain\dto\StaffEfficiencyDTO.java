package com.qm.ep.rebate.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.ep.rebate.enumerate.BoardStaffEfficiencyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 人员效能看板数据结果集
 */
@Schema(description = "数据:实体类-人员效能看板数据结果集")
@Data
public class StaffEfficiencyDTO {

    /**
     * id
     */
    @Schema(description = "数据-id")
    private Integer id;

    /**
     * 人员编码
     */
    @Schema(description = "数据-人员编码")
    private String userCode;

    /**
     * 人员名称
     */
    @Schema(description = "数据-人员名称")
    private String userName;

    /**
     * 能力项编码
     */
    @Schema(description = "数据-能力项编码")
    private String enableCode;

    /**
     * 能力项名称
     */
    @Schema(description = "数据-能力项名称")
    private String enableName;

    /**
     * 能力项星级
     */
    @Schema(description = "数据-能力项星级")
    private BigDecimal score;

    /**
     * l3流程编码id
     */
    @Schema(description = "数据-l3流程编码id")
    private String l3ProcessCode;

    /**
     * 流程名称
     */
    @Schema(description = "数据-流程名称")
    private String l3ProcessName;


    /**
     * 计算时间
     */
    @Schema(description = "数据-计算时间")
    private String scoreTime;

    /**
     * 总星级
     */
    @Schema(description = "数据-总星级")
    private BigDecimal overallEvaluation;

    /**
     * 创建时间
     */
    @Schema(description = "数据-创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    @Schema(description = "数据-董事会员工效率枚举")
    @JSONField(serialize = false)
    private BoardStaffEfficiencyEnum boardStaffEfficiencyEnum;

    public static StaffEfficiencyDTO buildDefault(BoardStaffEfficiencyEnum boardStaffEfficiencyEnum, String submitCode, String userName){
        StaffEfficiencyDTO staffEfficiencyDTO = new StaffEfficiencyDTO();
        staffEfficiencyDTO.setBoardStaffEfficiencyEnum(boardStaffEfficiencyEnum);
        staffEfficiencyDTO.setUserCode(submitCode);
        staffEfficiencyDTO.setScore(new BigDecimal("0"));
        staffEfficiencyDTO.setEnableCode(boardStaffEfficiencyEnum.getCode());
        staffEfficiencyDTO.setEnableName(boardStaffEfficiencyEnum.getDesc());
        staffEfficiencyDTO.setUserName(userName);
        return staffEfficiencyDTO;
    }
}
