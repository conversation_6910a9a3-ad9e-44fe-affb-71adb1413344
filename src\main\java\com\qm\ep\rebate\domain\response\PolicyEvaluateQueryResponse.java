package com.qm.ep.rebate.domain.response;

import com.qm.ep.rebate.domain.bean.PolicyEvaluatePO;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Schema(description = "政策评价表")
public class PolicyEvaluateQueryResponse {

    @Schema(description = "星级评价信息")
    private StarEvaluateInfo starEvaluateInfo;

    @Schema(description = "评价分布信息")
    private EvaluateDistributeInfo evaluateDistributeInfo;

    @Schema(description = "列表信息")
    private QmPage<PolicyEvaluatePO> policyPublishList;

    @Data
    @Schema(description = "星级评价信息")
    public static class StarEvaluateInfo {
        @Schema(description = "政策总数")
        private long policyCount;
        @Schema(description = "星级详情")
        private List<StarInfo> starList;
    }


    @Data
    @Schema(description = "星级详情")
    public static class StarInfo {
        @Schema(description = "星级：5-5颗星，1-1颗星，2-2颗星，3-3颗星，4-4颗星，0-没有星级")
        private Integer starValue;
        @Schema(description = "占比")
        private BigDecimal pointValue;
        @Schema(description = "星级数量")
        private int starCount;
    }



    @Data
    @Schema(description = "评价分布信息")
    public static class EvaluateDistributeInfo {
        @Schema(description = "政策总数")
        private long policyCount;
        @Schema(description = "评价详情")
        private List<DistributeInfo> distributeList;
    }


    @Data
    @Schema(description = "分布详情")
    public static class DistributeInfo {
        @Schema(description = "星级分布:政策分布-publish，政策结算-settle，预算使用-bgt，目标达成-aim，政策获取-acquire")
        private String distributeType;
        @Schema(description = "占比")
        private BigDecimal pointValue;
        @Schema(description = "分布数量")
        private long distributeCount;
    }

}
