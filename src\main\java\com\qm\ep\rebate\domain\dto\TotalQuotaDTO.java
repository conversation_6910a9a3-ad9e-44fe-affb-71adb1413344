package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Schema(description = "数据实体")
@Data
public class TotalQuotaDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-返利项目代码")
    private String classItemCode;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-年份")
    private String year;

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-总额")
    private String totalQuota;

    @Schema(description = "数据-额度")
    private String quota;

    @Schema(description = "数据-实际发生额")
    private String realityE;

    @Schema(description = "数据-差额")
    private String difference;

    @Schema(description = "数据-创建者")
    private String createBy;
    @Schema(description = "数据-创建时间")
    private Date createOn;
    @Schema(description = "数据-更新者")
    private String updateBy;
    @Schema(description = "数据-更新")
    private Date updateOn;
    @Schema(description = "数据-DTSTAMP")
    private Timestamp dtstamp;
}