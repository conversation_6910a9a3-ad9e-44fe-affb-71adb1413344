package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 销售车辆新增的返利计算结果
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Schema(description = "数据:销售车辆新增的返利计算结果")
@Data
public class ExtraRebateCalResult {

    /**
     * 再卖的台数(阶梯点)
     */
    @Schema(description = "再卖的台数(阶梯点)")
    private int addedAAK;
    @Schema(description = "预估回扣")
    private long estimatedRebate;
    @Schema(description = "增加返佣")
    private long addedRebate;
    @Schema(description = "平均回扣")
    private long averageRebate;
    @Schema(description = "增加平均回扣")
    private long addedAverageRebate;
    @Schema(description = "数据AAK 目标百分比")
    private int aakAimPercent;
    @Schema(description = "标准目标百分比")
    private int stdAimPercent;
}
