package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * The type Receive highlight request.
 */
@Schema(description = "数据:实体类-实体类")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveHighlightRequest {

    @Schema(description = "数据-中心代码")
    private String centerCode;

    @Schema(description = "数据-消息源")
    private String messageSource;


    @Schema(description = "数据-创建日期")
    private String createdDate;

    @Schema(description = "数据-运营亮点列表")
    private List<HighlightDTO> highlightDTOList;

    @Schema(description = "数据:实体类-运营亮点列表")
    @Data
    public static class HighlightDTO {
        @Schema(description = "数据-内容")
        private String content;

        @Schema(description = "数据-标题")
        private String title;

        @Schema(description = "数据-消息源")
        private String messageSource;
    }
}

