package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *  
 * 返利折让提报材料表
 *  
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Getter
@Setter
@TableName("rebate_extract_file")
public class RebateExtractFilePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 文件用途（F01-业务上传，F02-经销商上传）
     */
    @TableField(value = "file_use")
    private String fileUse;

    /**
     * 文件id
     */
    @TableField("object_key")
    private String objectKey;

    /**
     * 返利折让申请单id
     */
    @TableField("apply_id")
    private Long applyId;

    /**
     * 附件类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 附件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 是否ai审核
     */
    @TableField("can_ai")
    private String canAi;

    /**
     * ai审核结果 true false
     */
    @TableField("can_ai_result")
    private String canAiResult;

    /**
     * ai审核备注
     */
    @TableField("ai_remark")
    private String aiRemark;

    /**
     * 是否人工审核
     */
    @TableField("can_manual")
    private String canManual;

    /**
     * 人工审核备注
     */
    @TableField("manual_remark")
    private String manualRemark;

    /**
     * 附件审核通过结果
     */
    @TableField("can_pass")
    private String canPass;

    /**
     * 人工审核结果
     */
    @TableField("can_manual_result")
    private String canManualResult;
    /**
     * 创建者
     */
    @TableField("CREATEBY")
    private String createBy;

    /**
     * 创建日期
     */
    @TableField("CREATEON")
    private LocalDateTime createOn;

    /**
     * 更新者
     */
    @TableField("UPDATEBY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATEON")
    private LocalDateTime updateOn;

    /**
     * 时间戳
     */
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;

    /**
     * 上传人
     */
    @TableField("operator")
    private String operator;

    @TableField("traceId")
    private String traceId;


    @TableField("aiCount")
    private Integer aiCount;

    @TableField("pass_flag")
    private String passFlag;




}
