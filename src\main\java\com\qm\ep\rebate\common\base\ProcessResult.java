package com.qm.ep.rebate.common.base;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2023/7/4 11:26
 * @version: $
 */
@Data
@Builder
public class ProcessResult<T> {
    private String status;
    private String msgCode;
    private T msg;

    public ProcessResult() {
    }

    private ProcessResult(String status, String msgCode, T msg) {
        this.status = status;
        this.msgCode = msgCode;
        this.msg = msg;
    }
    public static <T> ProcessResult<T> success(T msg) {
        return new ProcessResult("1", "200",  msg);
    }

    public static <T> ProcessResult<T> faild(T msg) {
        return new ProcessResult("0", "500",  msg);
    }
}
