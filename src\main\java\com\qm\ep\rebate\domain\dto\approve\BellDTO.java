package com.qm.ep.rebate.domain.dto.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2023/7/4 16:58
 * @version: $
 */
@Schema(description = "数据:实体类-：")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BellDTO {

    /**
     * 内容
     */
    @Schema(description = "数据-内容")
    private String content;
    /**
     * 登录名
     */
    @Schema(description = "数据-登录名")
    private String loginName;
    /**
     * 租户id
     */
    @Schema(description = "数据-租户id")
    private String tenantId;
    /**
     * 标题
     */
    @Schema(description = "数据-标题")
    private String title;

}
