package com.qm.ep.rebate.common.config.async;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class AsyncThreadPoolConfig {

    @Value("${task.execution.pool.core-size:50}")
    private int corePoolSize;
    @Value("${task.execution.pool.max-size:100}")
    private int maxPoolSize;
    @Value("${task.execution.pool.queue-capacity:500}")
    private int queueCapacity;
    @Value("${task.execution.thread-name-prefix:task-}")
    private String namePrefix;
    @Value("${task.execution.pool.keep-alive:10}")
    private int keepAliveSeconds;

    @Bean("rebateBPMAsync")
    public Executor rebateBPMAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "rebateBPMAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);

        executor.setTaskDecorator(new AsyncContextDecorator());
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean("aimExecutor")
    public Executor aimExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "aim-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);

        executor.setTaskDecorator(new AsyncContextDecorator());
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

    @Bean("commonAsync")
    public Executor commonAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "通用线程池commonAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);

        executor.setTaskDecorator(new AsyncContextDecorator());
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }
}