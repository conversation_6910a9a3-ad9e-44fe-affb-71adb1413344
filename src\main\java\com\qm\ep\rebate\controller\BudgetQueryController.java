package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.dto.budget.in.BudgetGeneralListInDTO;
import com.qm.ep.rebate.domain.dto.budget.in.BudgetSpecialListInDTO;
import com.qm.ep.rebate.domain.dto.budget.in.BudgetPolicyListInDTO;
import com.qm.ep.rebate.domain.dto.budget.in.BudgetRevenueListInDTO;
import com.qm.ep.rebate.domain.dto.budget.out.*;
import com.qm.ep.rebate.service.BudgetQueryService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@Tag(name = "预算查询", description = "[author: 10200571]")
@RestController
@RequestMapping("/budgetQuery")
public class BudgetQueryController {

    @Resource
    private BudgetQueryService budgetQueryService;

    @Operation(summary = "预算查询 - 常规 - 金额", description = "[author: 10200571]")
    @PostMapping("/getGeneralAmountList")
    public JsonResultVo<QmPage<BgtBreakdownDetailOutDTO>> getGeneralAmountList(@RequestBody BudgetGeneralListInDTO inDTO) {
        JsonResultVo<QmPage<BgtBreakdownDetailOutDTO>> result = new JsonResultVo();
        result.setData(budgetQueryService.getBudgetAmountList(inDTO));
        return result;
    }

    @Operation(summary = "预算查询 - 常规 - 点数", description = "[author: 10200571]")
    @PostMapping("/getGeneralPointList")
    public JsonResultVo<QmPage<BgtBreakdownDetailOutDTO>> getGeneralPointList(@RequestBody BudgetGeneralListInDTO inDTO) {
        JsonResultVo<QmPage<BgtBreakdownDetailOutDTO>> result = new JsonResultVo();
        result.setData(budgetQueryService.getBudgetPointList(inDTO));
        return result;
    }

    @Operation(summary = "预算查询 - 专项 - 金额", description = "[author: 10200571]")
    @PostMapping("/getSpecialAmountList")
    public JsonResultVo<QmPage<BudgetSpecialListOutDTO>> getSpecialAmountList(@RequestBody BudgetSpecialListInDTO inDTO) {
        JsonResultVo<QmPage<BudgetSpecialListOutDTO>> result = new JsonResultVo();
        result.setData(budgetQueryService.getSpecialAmountList(inDTO));
        return result;
    }

    @Operation(summary = "查询关联政策", description = "[author: 10200571]")
    @PostMapping("/getPolicyList")
    public JsonResultVo<QmPage<BudgetPolicyListOutDTO>> getPolicyList(@RequestBody BudgetPolicyListInDTO inDTO) {
        JsonResultVo<QmPage<BudgetPolicyListOutDTO>> result = new JsonResultVo();
        result.setData(budgetQueryService.getPolicyList(inDTO));
        return result;
    }

    @Operation(summary = "查看政策详情", description = "[author: 10200571]")
    @PostMapping("/getPolicyDetail")
    public JsonResultVo<BudgetPolicyListOutDTO> getPolicyDetail(@RequestBody BudgetPolicyListInDTO inDTO) {
        return budgetQueryService.getPolicyDetail(inDTO);
    }

    @Operation(summary = "收入明细表", description = "[author: 10200571]")
    @PostMapping("/getRevenueDetailList")
    public JsonResultVo<RevenueListOutDTO> getRevenueDetailList(@RequestBody BudgetRevenueListInDTO inDTO) {
        JsonResultVo<RevenueListOutDTO> result = new JsonResultVo<>();
        result.setData(budgetQueryService.getRevenueDetailList(inDTO));
        return result;
    }

}
