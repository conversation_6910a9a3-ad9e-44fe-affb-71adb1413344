package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.domain.bean.BusinessConstructionDO;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:计算结果转业务底表")
@Data
public class TurnBusinessBottomTableDTO extends JsonParamDto {
    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-计算对象ID")
    private String objectId;

    @Schema(description = "数据-政策ID")
    private String policyId;

    @Schema(description = "数据-sql")
    private String structure;

    @Schema(description = "数据-计算类型")
    private String calculationType;

    @Schema(description = "数据-对象类型")
    private String objectType;

    @Schema(description = "数据-底表名称")
    private String tableName;

    @Schema(description = "数据-字段列名")
    private List<BusinessConstructionDO> fields;

    @Schema(description = "数据-所有的政策可用")
    private Integer openState;
}
