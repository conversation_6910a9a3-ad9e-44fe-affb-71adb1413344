package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.BgtApplyMainDO;
import com.qm.ep.rebate.domain.dto.budget.in.BgtApplyInfoSaveInDTO;
import com.qm.ep.rebate.domain.dto.budget.in.BgtApplyListInDTO;
import com.qm.ep.rebate.domain.dto.budget.out.BgtApplyInfoOutDTO;
import com.qm.ep.rebate.domain.dto.budget.out.BgtApplyListOutDTO;
import com.qm.ep.rebate.service.BgtApplyService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Tag(name = "预算申请及查询", description = "[author: 10200571]")
@RestController
@RequestMapping("/bgtApply")
public class BgtApplyController {

    @Resource
    BgtApplyService bgtApplyService;


    @Operation(summary = "预算申请列表", description = "[author: 10200571]")
    @PostMapping("/applyList")
    public JsonResultVo<QmPage<BgtApplyListOutDTO>> applyList(@RequestBody BgtApplyListInDTO inDTO) {
        JsonResultVo<QmPage<BgtApplyListOutDTO>> resultVo = new JsonResultVo<>();
        resultVo.setData(bgtApplyService.applyList(inDTO));
        return resultVo;
    }

    @Operation(summary = "预算申请 - 保存", description = "[author: 10200571]")
    @PostMapping("/applyInfoSave")
    public JsonResultVo<BgtApplyMainDO> applyInfoSave(@RequestBody BgtApplyInfoSaveInDTO inDTO) {
        JsonResultVo<BgtApplyMainDO> resultVo = new JsonResultVo<>();
        resultVo.setData(bgtApplyService.applyInfoSave(inDTO));
        return resultVo;
    }


    @Operation(summary = "预算申请 - 提交", description = "[author: 10200571]")
    @PostMapping("/applyInfoSubmit")
    public JsonResultVo<BgtApplyMainDO> applyInfoSubmit(@RequestBody BgtApplyInfoSaveInDTO inDTO) {
        JsonResultVo<BgtApplyMainDO> resultVo = new JsonResultVo<>();
        resultVo.setData(bgtApplyService.applyInfoSubmit(inDTO));
        return resultVo;
    }

    @Operation(summary = "查询预算申请信息", description = "[author: 10200571]")
    @GetMapping("/getApplyInfo")
    public JsonResultVo<BgtApplyInfoOutDTO> getApplyInfo(Integer id) {
        JsonResultVo<BgtApplyInfoOutDTO> resultVo = new JsonResultVo<>();
        resultVo.setData(bgtApplyService.getApplyInfo(id));
        return resultVo;
    }

    @Operation(summary = "预算总申请", description = "[author: 10200571]")
    @GetMapping("/getApplyInfoByMainId")
    public JsonResultVo<BgtApplyInfoOutDTO> getApplyInfoByMainId(String mainId) {
        JsonResultVo<BgtApplyInfoOutDTO> resultVo = new JsonResultVo<>();
        resultVo.setData(bgtApplyService.getApplyInfoByMainId(mainId));
        return resultVo;
    }

    @Operation(summary = "删除预算总申请", description = "[author: 10200571]")
    @GetMapping("/deleteApply")
    public JsonResultVo<Integer> deleteApply(Integer id) {
        JsonResultVo<Integer> resultVo = new JsonResultVo<>();
        resultVo.setData(bgtApplyService.deleteApply(id));
        return resultVo;
    }



}
