
package com.qm.ep.rebate.domain.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "数据:政策返利")
public class DealerPolicyRebateDTO {

    @Schema(description = "数据-经销商代码")
    private String dealerCode;

    @Schema(description = "数据-返利金额")
    private String totalAmount;

    @Schema(description = "数据-全国排名")
    private Integer nationwideRank;

    @Schema(description = "数据-大区排名")
    private Integer regionRank;

    @Schema(description = "数据-全国排名信息")
    private DealerRankDTO nationwideRankInfo;

    @Schema(description = "数据-大区排名信息")
    private DealerRankDTO regionRankInfo;

    @Schema(description = "数据-车系明细")
    private List<PolicyRebateDetailDTO> seriesList;

    @Schema(description = "数据-政策返利明细")
    private List<PolicyRebateDetailDTO> rebateList;

    @Schema(description = "数据:实体类-扩展参数")
    @Data
    public static class ExtendParams{
        @Schema(description = "数据-返利金额")
        private String totalAmount;

        @Schema(description = "数据-全国排名")
        private Integer nationwideRank;

        @Schema(description = "数据-大区排名")
        private Integer regionRank;
    }

    @Schema(description = "数据:实体类-经销商排名 DTO")
    @Data
    @Builder
    @AllArgsConstructor
    @NotEmpty
    public static class DealerRankDTO{
        @Schema(description = "数据-经销商代码")
        private String dealerCode;

        @Schema(description = "数据-经销商名称")
        private String dealerName;

        @Schema(description = "数据-价值")
        private String value;
    }
}

