package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.RebateExtractApproveRelationPO;
import com.qm.ep.rebate.domain.bean.sal.Mdac100DO;
import com.qm.ep.rebate.domain.request.RebateExtAppRelationQueryRequest;
import com.qm.ep.rebate.domain.request.RebateExtAppRelationSaveRequest;
import com.qm.ep.rebate.domain.request.RebateExtApproveSaveRequest;
import com.qm.ep.rebate.remote.request.OrgUserRequest;
import com.qm.ep.rebate.remote.response.OrgTreeNodeResponse;
import com.qm.ep.rebate.remote.response.OrgUserResponse;
import com.qm.ep.rebate.service.RebateExtractApproveRelationService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/rebateExtractApproveRelation")
@Tag(name = "区域审核Controller")
@Slf4j
public class RebateExtractApproveRelationController extends BaseController {

    @Resource
    private RebateExtractApproveRelationService relationService;


    @Operation(summary = "查询组织", description = "[author: 10200571]")
    @PostMapping("/getOrgTree")
    public JsonResultVo<List<OrgTreeNodeResponse>> getOrgTree() {
        JsonResultVo<List<OrgTreeNodeResponse>> result = new JsonResultVo<>();
        result.setData(relationService.getOrgTree());
        return result;
    }

    @Operation(summary = "按组织查询用户", description = "[author: 10200571]")
    @PostMapping("/getOrgUser")
    public JsonResultVo<List<OrgUserResponse>> getOrgUser(@RequestBody OrgUserRequest request) {
        JsonResultVo<List<OrgUserResponse>> result = new JsonResultVo<>();
        result.setData(relationService.getUserByOrgId(request));
        return result;
    }

    @Operation(summary = "查询经销商", description = "[author: 10200571]")
    @PostMapping("/getDealerList")
    public JsonResultVo<List<Mdac100DO>> getDealerList() {
        JsonResultVo<List<Mdac100DO>> result = new JsonResultVo<>();
        result.setData(relationService.getDealerList());
        return result;
    }

    @Operation(summary = "查询审核人员和经销商的关联关系", description = "[author: 10200571]")
    @PostMapping("/getApproveRelationList")
    public JsonResultVo<List<RebateExtractApproveRelationPO>> getApproveRelationList(@RequestBody RebateExtAppRelationQueryRequest request) {
        JsonResultVo<List<RebateExtractApproveRelationPO>> result = new JsonResultVo<>();
        result.setData(relationService.approveRelationList(request.getApproveType()));
        return result;
    }

    @Operation(summary = "保存审核人员和经销商的关联关系", description = "[author: 10200571]")
    @PostMapping("/saveApproveRelation")
    public JsonResultVo<String> saveApproveRelation(@RequestBody RebateExtAppRelationSaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(relationService.saveApproveRelation(request, getUserInfo()));
        return result;
    }

    @Operation(summary = "保存审核人员", description = "[author: 10200571]")
    @PostMapping("/saveApprove")
    public JsonResultVo<String> saveApprove(@RequestBody RebateExtApproveSaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(relationService.saveApprove(request, getUserInfo()));
        return result;
    }


}
