package com.qm.ep.rebate.domain.bean.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYSC009D")
@Schema(description = "数据:系统模块-字典子表")
public class SysDictChildDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-数据字典")
    @TableField("VDICT")
    private String dict;

    @Schema(description = "数据-代码")
    @TableField("VDICTCHILD")
    private String dictChild;

    @Schema(description = "数据-名称")
    @TableField("VDICTCHILDNAME")
    private String dictChildName;

    @Schema(description = "数据-扩展")
    @TableField("VEXTAND")
    private String extand;

    @Schema(description = "数据-扩展1")
    @TableField("VEXTAND1")
    private String extand1;

    @Schema(description = "数据-停用标识 1.停用 0.未停用")
    @TableField("CSTOP")
    private String stop;

    @Schema(description = "数据-停用日期")
    @TableField("DSTOP")
    private Date stopDate;

    @Schema(description = "数据-父项代码")
    @TableField("VPARENTCODE")
    private String parentCode;

    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String remark;

    @Schema(description = "数据-公司")
    @TableField("NCOMPANY")
    private Integer company;

    @Schema(description = "数据-预置标识")
    @TableField("VPREASSIGN")
    private String preassign;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新时间戳")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

}
