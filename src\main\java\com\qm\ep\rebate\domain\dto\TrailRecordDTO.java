package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 *  
 * 
 *
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Schema(description = "数据:实体类-    ")
@Data
public class TrailRecordDTO implements Serializable {


    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-密封日期")
    private String sealDate;

    @Schema(description = "数据-经销商代码")
    private String dealerCode;


}
