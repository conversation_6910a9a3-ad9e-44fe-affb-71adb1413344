package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.dto.budget.out.RevenueListOutDTO;
import com.qm.ep.rebate.domain.request.BgtSeriesSaleRequest;
import com.qm.ep.rebate.domain.request.BgtSeriesSaleSaveRequest;
import com.qm.ep.rebate.domain.response.BgtSeriesSaleResponse;
import com.qm.ep.rebate.service.BgtSeriesSaleCountService;
import com.qm.ep.rebate.service.BgtSeriesSaleService;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * 车系销量预测表 前端控制器
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Tag(name = "/bgtSeriesSale", description = "[author: 10200571]")
@RestController
@RequestMapping("/bgtSeriesSale")
public class BgtSeriesSaleController {

    @Autowired
    private BgtSeriesSaleCountService bgtSeriesSaleCountService;
    @Resource
    private BgtSeriesSaleService bgtSeriesSaleService;


    @Operation(summary = "金额转点数接口", description = "[author: 10200571]")
    @PostMapping("/amountToPoint")
    public JsonResultVo<List<BgtSeriesSaleResponse>> amountToPoint(@RequestBody BgtSeriesSaleRequest bgtSeriesSaleRequest) {
        JsonResultVo<List<BgtSeriesSaleResponse>> jsonResultVo = new JsonResultVo<>();

        List<BgtSeriesSaleResponse> bgtSeriesSaleDOS = bgtSeriesSaleService.amountToPoint(bgtSeriesSaleRequest);

        jsonResultVo.setData(bgtSeriesSaleDOS);
        jsonResultVo.setMsg("返回成功");
        return jsonResultVo;
    }

    @Operation(summary = "点数转金额接口", description = "[author: 10200571]")
    @PostMapping("/pointToAmount")
    public JsonResultVo<List<BgtSeriesSaleResponse>> pointToAmount(@RequestBody BgtSeriesSaleRequest bgtSeriesSaleRequest) {
        JsonResultVo<List<BgtSeriesSaleResponse>> jsonResultVo = new JsonResultVo<>();

        List<BgtSeriesSaleResponse> bgtSeriesSaleDOS = bgtSeriesSaleService.pointToAmount(bgtSeriesSaleRequest);

        jsonResultVo.setData(bgtSeriesSaleDOS);
        jsonResultVo.setMsg("返回成功");
        return jsonResultVo;
    }

    @Operation(summary = "取滚动管理的最新STD和AAK的更新时间", description = "[author: 10200571]")
    @GetMapping("/getNewesSTDAAKUpdDate")
    public JsonResultVo<Map<String, String>> getNewesSTDAAKUpdDate() {
        JsonResultVo<Map<String, String>> jsonResultVo = new JsonResultVo<>();

        Map<String, String> bgtSeriesSaleDOS = bgtSeriesSaleService.getNewesSTDAAKUpdDate();

        jsonResultVo.setData(bgtSeriesSaleDOS);
        jsonResultVo.setMsg("返回成功");
        return jsonResultVo;
    }

    @Operation(summary = "调用远程滚动管理接口摘取STD/AAK数据", description = "[author: 10200571]")
    @GetMapping("/getSTDAAKFromRollingPlan")
    @Deprecated
    public JsonResultVo<Map<String, String>> getSTDAAKFromRollingPlanTest() {
        JsonResultVo<Map<String, String>> jsonResultVo = new JsonResultVo<>();
        String msg = "返回成功";

        bgtSeriesSaleService.saveCurrSTDAAKDataFromRollingPlan();

        jsonResultVo.setMsg(msg);
        return jsonResultVo;
    }

    @Operation(summary = "更新平均指导价", description = "[author: 10200571]")
    @PostMapping("/saveAvgPrice")
    public JsonResultVo<Map<String, String>> saveAvgPrice(@RequestBody List<Map<String, String>> request) {
        JsonResultVo<Map<String, String>> jsonResultVo = new JsonResultVo<>();
        String msg = "返回成功";

        if (!request.isEmpty()) {
            bgtSeriesSaleService.saveAvgPrice(request);
        } else {
            msg = "无更新数据";
        }

        jsonResultVo.setMsg(msg);
        return jsonResultVo;
    }

    @Operation(summary = "初始化销量平均指导价收入", description = "[author: 10200571]")
    @PostMapping("/saveSTDAAKAvgPrice")
    @Deprecated
    public JsonResultVo<Map<String, String>> saveSTDAAKAvgPrice(@RequestBody List<Map<String, String>> request) {
        JsonResultVo<Map<String, String>> jsonResultVo = new JsonResultVo<>();
        String msg = "返回成功";

        if (!request.isEmpty()) {
            bgtSeriesSaleService.saveSTDAAKAvgPrice(request);
        } else {
            msg = "无更新数据";
        }

        jsonResultVo.setMsg(msg);
        return jsonResultVo;
    }


    @Operation(summary = "1月和 2-12月收入明细表滚动更新", description = "[author: 10200571]")
    @GetMapping("/roundSeriesSale")
    public JsonResultVo<String> roundSeriesSale(@RequestParam("month") String month) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();

        if (StringUtils.isBlank(month)) {
            month = DateUtil.getMonth(new Date());
        }
        // 先更新滚动销量
        bgtSeriesSaleCountService.buildSale(DateUtil.format(new Date(),"yyyy-MM-dd"));
        if ("1".equals(month)) {
            // TODO：1月复制上版本的数据
            bgtSeriesSaleService.roundMonth1SeriesSale();
        } else {
            // 2-12月会根据滚动收入和滚动销量更新收入明细表
            bgtSeriesSaleService.roundMonth2ToMonth12SeriesSale();
        }

        jsonResultVo.setMsg("返回成功");
        return jsonResultVo;
    }


    @Operation(summary = "收入明细1月保存与修改", description = "[author: 10200571]")
    @PostMapping("/saveMonth1")
    public JsonResultVo<String> saveMonth1(@RequestBody RevenueListOutDTO request) {
        return bgtSeriesSaleService.saveMonth1(request);
    }

    @Operation(summary = "收入明细1月发布", description = "[author: 10200571]")
    @PostMapping("/publishMonth1")
    public JsonResultVo<String> publishMonth1(@Valid @RequestBody BgtSeriesSaleSaveRequest request) {
        return bgtSeriesSaleService.publishMonth1(request.getUniqueKey());
    }

}
