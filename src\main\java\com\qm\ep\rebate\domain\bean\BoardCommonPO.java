package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *  
 * 
 *  
 *
 * <AUTHOR>
 * @since 2024-04-13
 */
@Schema(description = "数据:实体类-    ")
@Getter
@Setter
@TableName("board_common")
public class BoardCommonPO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 看板类型
     */


    @Schema(description = "数据-看板类型")
    @TableField("data_type")
    private String dataType;

    @Schema(description = "数据-行类型")
    @TableField("row_type")
    private String rowType;


    @Schema(description = "数据-组行")
    @TableField("group_row")
    private String groupRow;
    /**
     * 看板-额外数据
     */
    @Schema(description = "数据-看板-额外数据")
    @TableField("extra_data_json")
    private String extraDataJson;

    /**
     * 看板-核心数据
     */
    @Schema(description = "数据-看板-核心数据")
    @TableField("core_data_json")
    private String coreDataJson;

    /**
     * 看板-推送数据（extra_data_json+core_data_json组装而成）
     */
    @Schema(description = "数据-看板-推送数据（extra_data_json+core_data_json组装而成）")
    @TableField("push_data_json")
    private String pushDataJson;
    @Schema(description = "数据-商务时间")
    @TableField("biz_time")
    private String bizTime;

    /**
     * 时间戳
     */
    @Schema(description = "数据-时间戳")
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;

    @Schema(description = "数据:实体类-中 DTO")
    @Data
    public static  class MiddleDTO{
        @Schema(description = "数据-名字")
        private String name;
        @Schema(description = "数据-字段1")
        private Object field1;
        @Schema(description = "数据-字段2")
        private Object field2;
    }


}
