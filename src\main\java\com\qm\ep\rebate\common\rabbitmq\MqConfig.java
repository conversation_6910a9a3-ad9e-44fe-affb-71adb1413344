package com.qm.ep.rebate.common.rabbitmq;

import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;

public class MqConfig {

    @Bean
    @ConfigurationProperties(prefix = "mq.task-update-mq")
    public MqConfiguration taskUpdateMqConfiguration(){
        return new MqConfiguration();
    }

    @Bean
    public MqTemplate taskUpdateMq(MqConfiguration taskUpdateMqConfiguration) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(taskUpdateMqConfiguration.getHost());
        connectionFactory.setPort(taskUpdateMqConfiguration.getPort());
        connectionFactory.setUsername(taskUpdateMqConfiguration.getUser());
        connectionFactory.setPassword(taskUpdateMqConfiguration.getPassword());
        connectionFactory.setVirtualHost("/");
        // connectionFactory.setPublisherConfirms(true); //必须要设置

        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        MqTemplate mqTemplate = new MqTemplate();
        mqTemplate.setRabbitTemplate(rabbitTemplate);
        mqTemplate.setExchange(taskUpdateMqConfiguration.getExchange());
        mqTemplate.setQueue(taskUpdateMqConfiguration.getQueue());
        mqTemplate.setRouting_key(taskUpdateMqConfiguration.getRouting_key());
        return mqTemplate;
    }


}
