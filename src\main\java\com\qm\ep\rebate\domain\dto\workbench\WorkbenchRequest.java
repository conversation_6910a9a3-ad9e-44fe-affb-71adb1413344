package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@Schema(description = "数据:实体类-数据Workbench 请求")
@Data
@Builder
public class WorkbenchRequest {
    /**
     *     YSFJJTZ("YSFJJTZ", "预算分解及调整"),
     *     XXWH("XXWH","信息维护"),
     *     PZJSH("PZJSH","配置及审核"),
     *     FBJSJRZ("FBJSJRZ","发布计算及入账")
     */
    @Schema(description = "数据-)")
    @NotBlank(message = "code不能为空")
    private String code;
    @Schema(description = "数据-触发器信息列表")
    @NotEmpty(message = "triggerInfoList不能为空")
    private List<TriggerInfoDTO> triggerInfoList;
    @Schema(description = "数据-前任务实例代码")
    private String preTaskInstanceCode;
}
