package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.*;
import com.qm.ep.rebate.domain.vo.FormulaMainVO;
import com.qm.ep.rebate.domain.vo.FormulaVO;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.SourceTypeEnum;
import com.qm.ep.rebate.enumerate.YesOrNoEnum;
import com.qm.ep.rebate.mapper.FormulaMainMapper;
import com.qm.ep.rebate.remote.feign.RebateCalcFeignClient;
import com.qm.ep.rebate.remote.feign.RebateDataFeignClient;
import com.qm.ep.rebate.service.*;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.ep.rebate.infrastructure.util.RegexUtils;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import com.qm.tds.util.TableUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  
 * Controller
 * 计算公式主表JsonResultVo
 *  
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
@Tag(name = "计算公式主表", description = "[author: 10200571]")
@RestController
@RequestMapping("/formulaMain")
public class FormulaMainController extends BaseController {

    @Autowired
    private FormulaMainService formulaMainService;
    @Autowired
    private FormulaJoinService formulaJoinService;
    @Autowired
    private FormulaContentService formulaContentService;
    @Resource
    private FormulaService formulaService;
    @Resource
    private FormulaItemService formulaItemService;
    @Autowired
    private PolicyService policyService;
    @Resource
    private FormulaSectionJoinService formulaSectionJoinService;
    @Autowired
    private FormulaSectionAssignmentService formulaSectionAssignmentService;
    @Autowired
    private CalcObjectSourceService calcObjectSourceService;
    @Autowired
    private CalcObjectTargetService calcObjectTargetService;
    @Resource
    private SystemConfigService systemConfigService;
    @Autowired
    private SysPersonOrgService sysPersonOrgService;

    @Autowired
    private FormulaMainMapper formulaMainMapper;

    @Resource
    private RebateCalcFeignClient rebateCalcFeignClient;
    @Autowired
    private RebateDataFeignClient rebateDataFeignClient;

    @Autowired
    private PolicyFlagService policyFlagService;

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<FormulaMainDO> deleteById(@RequestBody FormulaMainDO tempDO) {
        JsonResultVo<FormulaMainDO> resultObj = new JsonResultVo<>();
        FormulaMainDO paramsDO = formulaMainService.getById(tempDO.getId());
        // 传入参数，objectid和policyId判断历史表中，是否在转换中（状态为3），转换中不可删除
        Map<String, Object> map = new HashMap<>();
        map.put("policyId", paramsDO.getPolicyid());
        map.put("objectId", tempDO.getId());
        JsonResultVo<Object> ret = rebateDataFeignClient.selectHistory(map);
        if (ret.isOk()) {
            Map<String, Object> data = (Map<String, Object>) ret.getData();
            boolean isOK = Boolean.parseBoolean(data.get("isOk").toString());
            if (!isOK) {
                resultObj.setMsgErr(data.get("msg").toString());
                return resultObj;
            }
        } else {
            resultObj.setMsgErr(ret.getMsg());
            return resultObj;
        }
        boolean flag = formulaMainService.deleteMain(tempDO.getId());
        if (flag) {
            // 删除 formulasection formuladetail formulaconditions
            formulaMainMapper.deleteDetail(tempDO.getId());
            formulaMainMapper.deleteFormulaItems(tempDO.getId());
            formulaMainMapper.deleteContent(tempDO.getId());
            formulaMainMapper.deleteJoin(tempDO.getId());
            formulaMainMapper.deleteJoinOn(tempDO.getId());
            List<String> formulaIds = new ArrayList<>();
            formulaIds.add(tempDO.getId());
            formulaSectionAssignmentService.deleteSectionAssignment(formulaIds);
            formulaMainMapper.deleteConditions(tempDO.getId());
            formulaMainMapper.deleteSectionJoin(tempDO.getId());
            formulaMainMapper.deleteSectionJoinOn(tempDO.getId());
            formulaMainMapper.deleteSectionMainConditions(tempDO.getId());
            formulaMainMapper.deleteBusiness(paramsDO.getPolicyid(), paramsDO.getFormulaName());

            if (RebateConstants.FORMULA_TYPE_FORMULA.equals(tempDO.getFormulaType())) {
                calcObjectSourceService.removeByForObjectId(tempDO.getId(), CalcObjectTypeEnum.FORMULA);
                calcObjectTargetService.removeByObjectId(tempDO.getId(), CalcObjectTypeEnum.FORMULA);
            } else {
                calcObjectSourceService.removeByForObjectId(tempDO.getId(), CalcObjectTypeEnum.SECTION);
            }
            // 删除相关试算历史
            List<String> calIds = new ArrayList<>();
            calIds.add(tempDO.getId());
            rebateCalcFeignClient.deleteByCalcObjectIds(calIds);

            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }

    @Operation(summary = "查询计算公式详情", description = "[author: 10200571]")
    @PostMapping("/formulaInfo")
    public JsonResultVo<FormulaVO> formulaInfo(@RequestBody FormulaDTO formulaDTO) {
        JsonResultVo<FormulaVO> jsonResultVo = new JsonResultVo<>();
        // 校验主键
        if (BootAppUtil.isNullOrEmpty(formulaDTO.getId())) {
            jsonResultVo.setMsgErr("查询失败");
            return jsonResultVo;
        }
        jsonResultVo.setData(formulaMainService.formulaInfo(formulaDTO.getId()));
        return jsonResultVo;
    }

    @Operation(summary = "计算公式复制", description = "[author: 10200571]")
    @PostMapping("/copyFormula")
    public JsonResultVo<String> copyFormula(@RequestBody FormulaDTO formulaDTO) {

        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        FormulaVO formulaVO = formulaMainService.formulaInfo(formulaDTO.getId());

        FormulaDTO insertDTO = formulaVO.convert2DTO();
        // 设置计算公式名
        insertDTO.setFormulaName(formulaDTO.getFormulaName());
        String msg = validateFormulaDTO(insertDTO);
        if (BootAppUtil.isnotNullOrEmpty(msg)) {
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String formulaId = formulaMainService.saveFormula(insertDTO, "copy");
        jsonResultVo.setData(formulaId);
        return jsonResultVo;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/getFormulaMain")
    @DS(DataSourceType.W)
    public JsonResultVo<QmPage<FormulaMainDO>> table(@RequestBody FormulaMainDTO tempDTO) {
        // 定义查询构造器
        QmQueryWrapper<FormulaMainDO> queryWrapper = new QmQueryWrapper<>();
        // 拼装实体属性查询条件
        LambdaQueryWrapper<FormulaMainDO> lambdaWrapper = queryWrapper.lambda();

        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getPolicyid()), FormulaMainDO::getPolicyid, tempDTO.getPolicyid());

        // 查询数据，使用table函数。
        QmPage<FormulaMainDO> list = formulaMainService.table(queryWrapper, tempDTO);
        // 拼装编辑权限
        List<FormulaMainDO> formulaList = list.getItems();
        if(CollectionUtils.isNotEmpty(formulaList)) {
            Map<String, String> editMap = policyFlagService.getFormulaEditFlagMap(tempDTO.getPolicyid(), formulaList);
            formulaList.forEach(item -> item.setIsEdit(editMap.get(item.getId())));
        }
        JsonResultVo<QmPage<FormulaMainDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "根据传入的实体信息进行查询 无分页", description = "[author: 10200571]")
    @PostMapping("/getFormulaListNoPage")
    @DS(DataSourceType.W)
    public JsonResultVo<List<FormulaMainDO>> getFormulaListNoPage(@RequestBody PolicyDTO policyDTO) {
        QmQueryWrapper<FormulaMainDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<FormulaMainDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(FormulaMainDO::getPolicyid, policyDTO.getId());
        List<FormulaMainDO> list = formulaMainService.list(queryWrapper);
        JsonResultVo<List<FormulaMainDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "根据传入的实体信息进行查询 无分页", description = "[author: 10200571]")
    @PostMapping("/getFormulaIdListNoPage")
    @DS(DataSourceType.W)
    public JsonResultVo<List<String>> getFormulaIdListNoPage(@RequestBody PolicyDTO policyDTO) {
        QmQueryWrapper<FormulaMainDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<FormulaMainDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(FormulaMainDO::getPolicyid, policyDTO.getId());
        List<FormulaMainDO> list = formulaMainService.list(queryWrapper);
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        List<String> ids = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            ids = list.stream().map(FormulaMainDO::getId).collect(Collectors.toList());
        }
        ret.setData(ids);
        return ret;
    }

    @Operation(summary = "查询计算公式模板列表", description = "[author: 10200571]")
    @PostMapping("/getFormulaTemplate")
    public JsonResultVo<QmPage<FormulaMainDO>> getFormulaTemplate(@RequestBody FormulaMainDTO tempDTO) {
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);
        LoginKeyDO userInfo = getUserInfo();

        QueryWrapper<PolicyDO> policyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyDO> policyLambdaWrapper = policyWrapper.lambda();
        policyLambdaWrapper.eq(PolicyDO::getIsTemplate, YesOrNoEnum.YES)
                .like(StringUtils.isNotEmpty(tempDTO.getPolicyName()), PolicyDO::getVpolicyname, tempDTO.getPolicyName());
        if ("bx".equals(company)) {
            List<String> createByList = sysPersonOrgService.queryPersonIdAtRelatedLevel(userInfo.getOperatorId());
            policyLambdaWrapper.in(PolicyDO::getCreateBy, createByList);
        }
        List<PolicyDO> policyDOList = policyService.list(policyWrapper);

        JsonResultVo<QmPage<FormulaMainDO>> ret = new JsonResultVo<>();
        if (CollUtil.isEmpty(policyDOList)) {
            QmPage<FormulaMainDO> page = new QmPage<>();
            ret.setData(page);
        } else {
            List<String> policyIdList = policyDOList.stream().map(PolicyDO::getId).collect(Collectors.toList());
            QmQueryWrapper<FormulaMainDO> queryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<FormulaMainDO> lambdaWrapper = queryWrapper.lambda();
            lambdaWrapper.eq(FormulaMainDO::getFormulaType, RebateConstants.FORMULA_TYPE_FORMULA)
                    .in(FormulaMainDO::getPolicyid, policyIdList)
                    .like(StringUtils.isNotEmpty(tempDTO.getFormulaName()), FormulaMainDO::getFormulaName, tempDTO.getFormulaName());
            QmPage<FormulaMainDO> page = formulaMainService.table(queryWrapper, tempDTO);
            ret.setData(page);
        }
        return ret;
    }

    /**
     * 获取公式取值表名
     */
    @Operation(summary = "获取公式取值", description = "[author: 10200571]")
    @PostMapping("/getFormulaValue")
    public JsonResultVo<List<FormulaMainDO>> getFormulaValue(@RequestBody FormulaMainDTO tempDTO) {
        List<FormulaMainDO> list = formulaMainMapper.getFormulaValueble(tempDTO.getPolicyid());
        JsonResultVo<List<FormulaMainDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    /**
     * 获取公式取值字段名
     */
    @Operation(summary = "获取公式取值字段名", description = "[author: 10200571]")
    @PostMapping("/getFieldnameList")
    public JsonResultVo<QmPage<FormulaMainDO>> getFieldnameList(@RequestBody FormulaMainDTO tempDTO) {
        JsonResultVo<QmPage<FormulaMainDO>> jsonResultVo = new JsonResultVo<>();
        QmQueryWrapper<FormulaMainDO> queryWrapper = new QmQueryWrapper<>();
        TableUtils.appendTableAdditional(queryWrapper, tempDTO, FormulaMainDO.class);
        IPage<FormulaMainDO> queryPage = TableUtils.convertToIPage(tempDTO);
        IPage<FormulaMainDO> fieldnameList = formulaMainMapper.getFieldnameList(queryPage, queryWrapper, tempDTO);
        QmPage<FormulaMainDO> qmPage = TableUtils.convertQmPageFromMpPage(fieldnameList);
        jsonResultVo.setData(qmPage);
        return jsonResultVo;
    }

    @Operation(summary = "区间保存", description = "[author: 10200571]")
    @PostMapping("/saveFormulaSection")
    public JsonResultVo<String> saveFormulaSection(@RequestBody FormulaSectionMainDTO tempDOs) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        String msg = validateFormulaConditionSectionDTO(tempDOs);
        if (BootAppUtil.isnotNullOrEmpty(msg)) {
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String sectionId = formulaMainService.saveSection(tempDOs);
        jsonResultVo.setData(sectionId);
        return jsonResultVo;
    }


    @Operation(summary = "区间复制", description = "[author: 10200571]")
    @PostMapping("/copyFormulaSection")
    public JsonResultVo<String> copyFormulaSection(@RequestBody FormulaSectionMainDTO tempDOs) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        FormulaMainVO formulaMainVO = formulaMainService.formulaSectionInfo(tempDOs.getId());
        FormulaSectionMainDTO insertData = formulaMainVO.convertToFormulaSectionMainDTO();
        insertData.setSectionName(tempDOs.getSectionName());
        String msg = validateFormulaConditionSectionDTO(insertData);
        if (BootAppUtil.isnotNullOrEmpty(msg)) {
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String sectionId = formulaMainService.saveSection(insertData);
        jsonResultVo.setData(sectionId);
        return jsonResultVo;
    }

    /**
     * 区间保存信息校验
     *
     * @param
     * @return
     */
    @Operation(summary = "区间保存信息校验", description = "[author: 10200571]")
    private String validateFormulaConditionSectionDTO(FormulaSectionMainDTO formulaConditionSectionDTO) {
        // 校验是否为空
        if (BootAppUtil.isNullOrEmpty(formulaConditionSectionDTO)) {
            return "保存失败";
        }
        // 校验政策主键是否合法
        if (BootAppUtil.isNullOrEmpty(formulaConditionSectionDTO.getForid())) {
            return "政策主键不能为空";
        }
        if (formulaConditionSectionDTO.getForid().length() > RebateConstants.ID_LENGTH_36) {
            return "政策主键长度过长";
        }
        // 校验公式名称是否合法
        if (BootAppUtil.isNullOrEmpty(formulaConditionSectionDTO.getSectionName())) {
            return "公式名称不能为空";
        }
        if (formulaConditionSectionDTO.getSectionName().length() > RebateConstants.FIELD_LENGTH_255) {
            return "公式名称长度过长";
        }
        return null;
    }

    @Operation(summary = "保存计算公式", description = "[author: 10200571]")
    @PostMapping("/saveFormula")
    public JsonResultVo<String> saveFormula(@RequestBody FormulaDTO formulaDTO) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        String msg = validateFormulaDTO(formulaDTO);
        if (BootAppUtil.isnotNullOrEmpty(msg)) {
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String formulaId = formulaMainService.saveFormula(formulaDTO, "save");
        jsonResultVo.setData(formulaId);
        return jsonResultVo;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFormulaDTO(FormulaDTO formulaDTO) {
        // 校验是否为空
        if (BootAppUtil.isNullOrEmpty(formulaDTO)) {
            return "保存失败";
        }
        return validateId(formulaDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateId(FormulaDTO formulaDTO) {
        // 校验主键是否合法
        if (BootAppUtil.isnotNullOrEmpty(formulaDTO.getId()) && formulaDTO.getId().length() > RebateConstants.ID_LENGTH_36) {
            return "主键长度过长";
        }
        // 校验政策主键是否合法
        if (BootAppUtil.isNullOrEmpty(formulaDTO.getPolicyId())) {
            return "政策主键不能为空";
        }
        if (formulaDTO.getPolicyId().length() > RebateConstants.ID_LENGTH_36) {
            return "政策主键长度过长";
        }
        return validateFormula(formulaDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFormula(FormulaDTO formulaDTO) {
        // 校验公式名称是否合法
        if (BootAppUtil.isNullOrEmpty(formulaDTO.getFormulaName())) {
            return "公式名称不能为空";
        }
        if (formulaDTO.getFormulaName().length() > RebateConstants.FIELD_LENGTH_255) {
            return "公式名称长度过长";
        }
        if (!RegexUtils.validateName(formulaDTO.getFormulaName())) {
            return "名称只能包含数字、字母或汉字";
        }
        // 校验公式描述是否合法
        if (BootAppUtil.isnotNullOrEmpty(formulaDTO.getDescription()) && formulaDTO.getDescription().length() > RebateConstants.FIELD_LENGTH_255) {
            return "公式描述长度过长";
        }
        return validateOutputColumn(formulaDTO);
    }


    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateOutputColumn(FormulaDTO formulaDTO) {
        // 校验主数据源是否合法
        if (formulaDTO.getDataSource() == null || formulaDTO.getDataSource().isEmpty()) {
            return "主数据源不能为空";
        }
        // 校验输出字段是否合法
        if (formulaDTO.getOutputColumn() == null || formulaDTO.getOutputColumn().isEmpty()) {
            return "输出字段不能为空";
        }
        /*if(String.join(RebateConstants.COMMA_SEPARATOR, formulaDTO.getOutputColumn()).length() > RebateConstants.FIELD_LENGTH_255){
            return "输出字段过多";
        }*/
        // 校验公式内容
        List<FormulaStructureDTO> formulaContentList = formulaDTO.getFormulaContent();
        if (formulaContentList == null || formulaContentList.isEmpty()) {
            return "公式内容不能为空";
        } else {
            return validateFormulaContent(formulaDTO);
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFormulaContent(FormulaDTO formulaDTO) {
        List<FormulaStructureDTO> formulaContentList = formulaDTO.getFormulaContent();
        List<String> formulaFieldNameList = formulaContentList.stream().map(FormulaStructureDTO::getName).collect(Collectors.toList());
        Set<String> formulaFieldNameSet = new HashSet<>(formulaFieldNameList);
        if (formulaFieldNameSet.size() < formulaFieldNameList.size()) {
            return "公式列名不可重复";
        }
        for (FormulaStructureDTO content : formulaContentList) {
            try {
                validateFormulaFieldName(content);
                validateFormulaContent(content);
                validateFormulaProp(content);
            } catch (QmException e) {
                return e.getMessage();
            }
        }
        // 校验筛选条件
        return validateFilterCondition(formulaDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFormulaFieldName(FormulaStructureDTO content) {
        // 校验公式列名
        if (BootAppUtil.isNullOrEmpty(content.getName())) {
            throw new QmException("公式列名不能为空");
        }
        if (content.getName().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("公式列名长度过长");
        }
        if (!RegexUtils.validateName(content.getName())) {
            throw new QmException("公式列名只能包含数字、字母或汉字");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFormulaContent(FormulaStructureDTO content) {
        // 校验公式是否合法
        if (CollUtil.isEmpty(content.getContent())) {
            throw new QmException("公式不能为空");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFormulaProp(FormulaStructureDTO content) {
        // 校验取值类型是否合法
        if (content.getFetchType() == null) {
            throw new QmException("取值类型不能为空");
        }
        // 校验分母为零时计算规则是否合法
        if (content.getRule() == null) {
            throw new QmException("分母为零时计算规则不能为空");
        }
        // 校验保留小数位是否合法
        if (content.getDecimal() == null) {
            throw new QmException("保留小数位不能为空");
        }
        if (content.getDecimal().compareTo(0) < 0) {
            throw new QmException("保留小数位必须大于或等于零");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFilterCondition(FormulaDTO formulaDTO) {
        List<FormulaConditionsDTO> filterConditionList = formulaDTO.getFilterCondition();
        if (filterConditionList != null && !filterConditionList.isEmpty()) {
            for (FormulaConditionsDTO condition : filterConditionList) {
                try {
                    validateFieldName(condition);
                    validateOperation(condition);
                    validateFilterMethod(condition);
                    validateCondition(condition);
                } catch (QmException e) {
                    return e.getMessage();
                }
            }
        }
        return validateDependentSource(formulaDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFieldName(FilterCondition condition) {
        // 校验字段名
        if (BootAppUtil.isNullOrEmpty(condition.getFieldName())) {
            throw new QmException("字段名不能为空");
        }
        if (condition.getFieldName().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("字段名长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateOperation(FilterCondition condition) {
        // 校验前括号
        if (condition.getFrontBracket() != null && condition.getFrontBracket().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("括号长度过长");
        }
        // 校验运算符
        if (BootAppUtil.isNullOrEmpty(condition.getOperation())) {
            throw new QmException("运算符不能为空");
        }
        if (condition.getOperation().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("运算符长度过长");
        }
        // 校验后括号
        if (condition.getBackBracket() != null && condition.getBackBracket().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("括号长度过长");
        }
        // 校验关联逻辑
        if (condition.getLogic() != null && condition.getLogic().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("关联逻辑长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFilterMethod(FilterCondition condition) {
        // 校验筛选方式
        if (BootAppUtil.isNullOrEmpty(condition.getFilterMethod())) {
            throw new QmException("筛选方式不能为空");
        }
        if (condition.getFilterMethod().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("筛选方式长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateCondition(FilterCondition condition) {
        // 校验条件值
        if (!RebateConstants.VALUE.equals(condition.getFilterMethod()) && BootAppUtil.isNullOrEmpty(condition.getCondition())) {
            // 当筛选方式不是字符串而且为空，则报错
            throw new QmException("条件值不能为空");
        }
        if (BootAppUtil.isnotNullOrEmpty(condition.getCondition()) && condition.getCondition().length() > RebateConstants.FIELD_LENGTH_255) {
            throw new QmException("条件值长度过长");
        }
        // 校验条件值格式
        if (RebateConstants.NUMBER.equals(condition.getFilterMethod()) && !NumberUtil.isNumber(condition.getCondition())) {
            throw new QmException("条件值格式有误");
        }
        if (RebateConstants.DATE.equals(condition.getFilterMethod()) && !DateUtil.validateDateTime(condition.getCondition(), RebateConstants.DATE_FORMAT_PATTERN)) {
            throw new QmException("条件值格式有误");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateDependentSource(FormulaDTO formulaDTO) {
        List<CalcObjectDTO> dataSource = formulaDTO.getDataSource();
        List<FormulaJoinDTO> dependents = formulaDTO.getDependents();
        if (dependents != null && !dependents.isEmpty()) {
            if (dataSource.size() > 1) {
                return "主数据源为两个或两个以上时，从数据源应该为空";
            }
            for (FormulaJoinDTO dependent : dependents) {
                if (dependent.getJoinType() == null) {
                    return "关联类型不能为空";
                }
                if (dependent.getTableObject() == null) {
                    return "从数据源不能为空";
                }
                List<FormulaJoinOnDTO> conditions = dependent.getConditions();
                if (conditions != null && !conditions.isEmpty()) {
                    try {
                        validateJoinCondition(conditions);
                    } catch (QmException e) {
                        return e.getMessage();
                    }
                } else {
                    return "关联条件不能为空";
                }
            }
        }
        return "";
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinCondition(List<FormulaJoinOnDTO> conditions) {
        for (FormulaJoinOnDTO condition : conditions) {
            validateFieldName(condition);
            validateOperation(condition);
            validateFilterMethod(condition);
            validateCondition(condition);
        }
    }

    @Operation(summary = "查询区间回显数据", description = "[author: 10200571]")
    @PostMapping("/sectionInfo")
    public JsonResultVo<FormulaMainVO> sectionInfo(@RequestBody FormulaDTO formulaDTO) {
        JsonResultVo<FormulaMainVO> jsonResultVo = new JsonResultVo<>();
        // 校验主键
        if (BootAppUtil.isNullOrEmpty(formulaDTO.getId())) {
            jsonResultVo.setMsgErr("查询失败");
            return jsonResultVo;
        }
        jsonResultVo.setData(formulaMainService.formulaSectionInfo(formulaDTO.getId()));
        return jsonResultVo;
    }


    @Operation(summary = "判断主、从数据源是否发生变更", description = "[author: 10200571]")
    @PostMapping("/getChange")
    public JsonResultVo getChange(@RequestBody FormulaMainDO formulaMainDO) {
        JsonResultVo<Object> res = new JsonResultVo<>();
        Map<String, Set<String>> tableWithFieldMap = formulaMainService.getChangedDataSourceName(formulaMainDO.getId());
        if (CollUtil.isNotEmpty(tableWithFieldMap)) {
            res.setData(tableWithFieldMap);
        }
        return res;
    }

    @Operation(summary = "校验计算公式模板", description = "[author: 10200571]")
    @PostMapping("/validateTemplate")
    public JsonResultVo validateTemplate(@RequestBody FormulaDTO formulaDTO) {
        JsonResultVo<Object> res = new JsonResultVo<>();
        Map<String, Set<String>> tableWithFieldMap = formulaMainService.getDifferentFromTemplate(formulaDTO.getPolicyId(), formulaDTO.getFormulaTemplateId());
        if (CollUtil.isNotEmpty(tableWithFieldMap)) {
            res.setData(tableWithFieldMap);
        }
        return res;
    }

    /**
     * @param :
     * @return {@link }
     * <AUTHOR>
     * @version 1.0
     *  date: 2022/10/24 16:14
     */
    @Operation(summary = "接口date: 2022/10/24 16:14", description = "[author: 10200571]")
    @PostMapping("/turnDataSource")
    public JsonResultVo turnDataSource() {
        JsonResultVo<Object> res = new JsonResultVo<>();
        /*
        1、主数据源
        main_table为空，从content里找，如果content为空，从main中formula中找
         */
        List<FormulaMainDO> mainDOList = formulaMainService.list();
        mainDOList.forEach(main -> {
            String policyId = main.getPolicyid();
            PolicyDO policyDO = policyService.getById(policyId);
            String policyName = policyDO == null ? "" : policyDO.getVpolicyname();
            String formulaId = main.getId();
            String mainTable = main.getMainTable();
            CalcObjectTypeEnum type = main.getFormulaType() == 1 ? CalcObjectTypeEnum.FORMULA : CalcObjectTypeEnum.SECTION;
            if (StringUtils.isNotEmpty(mainTable)) {
                if (mainTable.contains("{")) {
                    try {
                        List<CalcObjectSourceDO> sourceDOList = JSON.parseArray(mainTable, CalcObjectSourceDO.class);
                        sourceDOList.forEach(source -> {
                            source.setForPolicyId(policyId);
                            source.setForObjectId(formulaId);
                            source.setForType(type);
                            source.setForSourceType(SourceTypeEnum.MAIN);
                        });
                        calcObjectSourceService.saveBatch(sourceDOList);
                        List<String> idList = sourceDOList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                        main.setMainTable(CharSequenceUtil.join(",", idList));
                        formulaMainService.updateById(main);
                    } catch (Exception e) {
                        CalcObjectSourceDO calcObjectSourceDO = JSON.parseObject(mainTable, CalcObjectSourceDO.class);
                        calcObjectSourceDO.setForPolicyId(policyId);
                        calcObjectSourceDO.setForObjectId(formulaId);
                        calcObjectSourceDO.setForType(type);
                        calcObjectSourceDO.setForSourceType(SourceTypeEnum.MAIN);
                        calcObjectSourceService.save(calcObjectSourceDO);
                        main.setMainTable(calcObjectSourceDO.getId());
                        formulaMainService.updateById(main);
                    }
                }
            } else {
                // 在content中找
                LambdaQueryWrapper<FormulaContentDO> contentWrapper = new QmQueryWrapper<FormulaContentDO>().lambda();
                contentWrapper.eq(FormulaContentDO::getFormulaId, formulaId);
                List<FormulaContentDO> contentDOList = formulaContentService.list(contentWrapper);

                if (CollUtil.isNotEmpty(contentDOList)) {
                    List<CalcObjectSourceDO> sourceList = new ArrayList<>();
                    List<String> nameList = new ArrayList<>();
                    contentDOList.forEach(contentDO -> {
                        String content = contentDO.getContent();
                        JSONArray contentArr = JSON.parseArray(content);
                        for (int i = 0; i < contentArr.size(); i++) {
                            CalcObjectSourceDO calcObjectSourceDO = new CalcObjectSourceDO();
                            JSONObject jsonObject = contentArr.getJSONObject(i);
                            String tableName = jsonObject.getString("tableName");
                            if (StringUtils.isNotEmpty(tableName) && !nameList.contains(tableName)) {
                                nameList.add(tableName);
                                String type1 = jsonObject.getString("type");
                                if ("basic".equals(type1)) {
                                    calcObjectSourceDO.setForPolicyId(policyId);
                                    calcObjectSourceDO.setForObjectId(formulaId);
                                    calcObjectSourceDO.setForType(type);
                                    calcObjectSourceDO.setForSourceType(SourceTypeEnum.MAIN);
                                    calcObjectSourceDO.setObjectName(tableName);
                                    calcObjectSourceDO.setObjectType(CalcObjectTypeEnum.BASIC);
                                } else {
                                    calcObjectSourceDO.setForPolicyId(policyId);
                                    calcObjectSourceDO.setForObjectId(formulaId);
                                    calcObjectSourceDO.setForType(type);
                                    calcObjectSourceDO.setForSourceType(SourceTypeEnum.MAIN);
                                    calcObjectSourceDO.setPolicyId(policyId);
                                    calcObjectSourceDO.setPolicyName(policyName);
                                    calcObjectSourceDO.setObjectName(tableName);

                                    CalcObjectTypeEnum[] values = CalcObjectTypeEnum.values();
                                    for (CalcObjectTypeEnum value : values) {
                                        if (value.getCode().equals(type1)) {
                                            calcObjectSourceDO.setObjectType(value);
                                        }
                                    }
                                }
                                sourceList.add(calcObjectSourceDO);
                            }
                        }
                    });
                    calcObjectSourceService.saveBatch(sourceList);
                    List<String> idList = sourceList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                    main.setMainTable(CharSequenceUtil.join(",", idList));
                    formulaMainService.updateById(main);
                } else {
                    String formula = ""; // main.getFormula();
                    List<CalcObjectSourceDO> sourceList = new ArrayList<>();
                    List<String> nameList = new ArrayList<>();
                    if (StringUtils.isNotEmpty(formula)) {
                        JSONArray contentArr = JSON.parseArray(formula);
                        for (int i = 0; i < contentArr.size(); i++) {
                            CalcObjectSourceDO calcObjectSourceDO = new CalcObjectSourceDO();
                            JSONObject jsonObject = contentArr.getJSONObject(i);
                            String tableName = jsonObject.getString("tableName");
                            if (StringUtils.isNotEmpty(tableName) && !nameList.contains(tableName)) {
                                nameList.add(tableName);
                                String type1 = jsonObject.getString("type");
                                if ("basic".equals(type1)) {
                                    calcObjectSourceDO.setForPolicyId(policyId);
                                    calcObjectSourceDO.setForObjectId(formulaId);
                                    calcObjectSourceDO.setForType(type);
                                    calcObjectSourceDO.setForSourceType(SourceTypeEnum.MAIN);
                                    calcObjectSourceDO.setObjectName(tableName);
                                    calcObjectSourceDO.setObjectType(CalcObjectTypeEnum.BASIC);
                                } else {
                                    calcObjectSourceDO.setForPolicyId(policyId);
                                    calcObjectSourceDO.setForObjectId(formulaId);
                                    calcObjectSourceDO.setForType(type);
                                    calcObjectSourceDO.setForSourceType(SourceTypeEnum.MAIN);
                                    calcObjectSourceDO.setPolicyId(policyId);
                                    calcObjectSourceDO.setPolicyName(policyName);
                                    calcObjectSourceDO.setObjectName(tableName);

                                    CalcObjectTypeEnum[] values = CalcObjectTypeEnum.values();
                                    for (CalcObjectTypeEnum value : values) {
                                        if (value.getCode().equals(type1)) {
                                            calcObjectSourceDO.setObjectType(value);
                                        }
                                    }
                                }
                                sourceList.add(calcObjectSourceDO);
                            }
                        }
                        calcObjectSourceService.saveBatch(sourceList);
                        List<String> idList = sourceList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                        main.setMainTable(CharSequenceUtil.join(",", idList));
                        formulaMainService.updateById(main);
                    }
                }
            }
        });

        /*
        公式从数据源
         */
        List<FormulaJoinDO> joinList = formulaJoinService.list();
        joinList.forEach(joinDO -> {
            String formulaId = joinDO.getFormulaId();
            String policyId = joinDO.getPolicyId();
            String tableObject = joinDO.getTableObject();
            if (tableObject.contains("{")) {
                CalcObjectSourceDO calcObjectSourceDO = JSON.parseObject(tableObject, CalcObjectSourceDO.class);
                calcObjectSourceDO.setForPolicyId(policyId);
                calcObjectSourceDO.setForObjectId(formulaId);
                calcObjectSourceDO.setForType(CalcObjectTypeEnum.FORMULA);
                calcObjectSourceDO.setForSourceType(SourceTypeEnum.SUB);
                calcObjectSourceService.save(calcObjectSourceDO);
                joinDO.setTableObject(calcObjectSourceDO.getId());
                formulaJoinService.updateById(joinDO);
            }
        });


        /*
        区间从数据源
         */
        List<FormulaSectionJoinDO> sectionJoinList = formulaSectionJoinService.list();
        sectionJoinList.forEach(joinDO -> {
            String formulaId = joinDO.getSectionId();
            String policyId = joinDO.getPolicyId();
            String tableObject = joinDO.getTableObject();
            if (tableObject.contains("{")) {
                CalcObjectSourceDO calcObjectSourceDO = JSON.parseObject(tableObject, CalcObjectSourceDO.class);
                calcObjectSourceDO.setForPolicyId(policyId);
                calcObjectSourceDO.setForObjectId(formulaId);
                calcObjectSourceDO.setForType(CalcObjectTypeEnum.SECTION);
                calcObjectSourceDO.setForSourceType(SourceTypeEnum.SUB);
                calcObjectSourceService.save(calcObjectSourceDO);
                joinDO.setTableObject(calcObjectSourceDO.getId());
                formulaSectionJoinService.updateById(joinDO);
            }
        });

        return res;
    }

    @Operation(summary = "迁移公式内容数据", description = "[author: 10200571]")
    @PostMapping("/migrateFormulaData")
    public void migrateFormulaData() {
        List<FormulaContentDO> formulaContentDOS = formulaContentService.list();
        formulaContentDOS.forEach(f -> {
            FormulaDO formulaDO = new FormulaDO();
            formulaDO.setPolicyId(f.getPolicyId());
            formulaDO.setObjectId(f.getFormulaId());
            formulaDO.setObjectType(CalcObjectTypeEnum.FORMULA);
            formulaDO.setName(f.getName());
            formulaDO.setDecimal(f.getDecimal());
            formulaDO.setRule(f.getRule());
            formulaDO.setFetchType(f.getFetchType());
            formulaDO.setCreateOn(f.getCreateOn());
            formulaDO.setCreateBy(f.getCreateBy());
            formulaDO.setUpdateOn(f.getUpdateOn());
            formulaDO.setUpdateBy(f.getUpdateBy());
            formulaDO.setDtstamp(f.getDtstamp());
            formulaService.save(formulaDO);

            List<CommonFormulaItemDTO> formulaContents = JSONUtils.packingDOListFromJsonStr(f.getContent(), CommonFormulaItemDTO.class);
            formulaContents.forEach(i -> {
                FormulaItemDO formulaItemDO = new FormulaItemDO();
                formulaItemDO.setFormulaId(formulaDO.getId());
                formulaItemDO.setValue(i.getValue());
                formulaItemDO.setType(i.getType());
                formulaItemDO.setTableName(i.getTableName());
                formulaItemDO.setTableField(i.getTableField());
                formulaItemDO.setRule(i.getRule());
                formulaItemService.save(formulaItemDO);
            });
        });
    }
}
