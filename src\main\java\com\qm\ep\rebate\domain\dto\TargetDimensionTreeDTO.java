package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:指标维度树")
public class TargetDimensionTreeDTO extends JsonParamDto {

    @Schema(description = "数据-组织")
    private String org;

    @Schema(description = "数据-目标名称")
    private String targetName;

}