package com.qm.ep.rebate.domain.dto.oa;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:实体类-任务")
@Data
public class Task{
	@Schema(description = "数据-暂停状态")
    private int suspensionState;
	@Schema(description = "数据-删除")
    private boolean deleted;
	@Schema(description = "数据-最后")
    private boolean last;
	@Schema(description = "数据-标识链接已初始化")
    private boolean identityLinksInitialized;
	@Schema(description = "数据-服务代码")
    private String serviceCode;
	@Schema(description = "数据-应用程序房客主键")
    private String appTenantId;
	@Schema(description = "数据-房客主键")
    private String tenantId;
	@Schema(description = "数据-租户代码")
    private String tenantCode;
	@Schema(description = "数据-应用源")
    private String appSource;
	@Schema(description = "数据-优先权")
    private int priority;
	@Schema(description = "数据-第一")
    private boolean first;
	@Schema(description = "数据-校订")
    private int revision;
	@Schema(description = "数据-事件名称")
    private String eventName;
	@Schema(description = "数据-名字")
    private String name;
	@Schema(description = "数据-主键")
    private String id;
	@Schema(description = "数据-受让人")
    private String assignee;

}