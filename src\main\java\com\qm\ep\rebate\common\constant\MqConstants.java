package com.qm.ep.rebate.common.constant;

/**
 * <AUTHOR>
 */
public class MqConstants {

    private MqConstants() {
        throw new IllegalStateException("MQ 常量");
    }

    /**
     * 死信队列配置
     */
    public static final String RABBIT_DEADLETTER_EXCHANGE = "x-dead-letter-exchange";
    public static final String RABBIT_DEADLETTER_ROUTINGKEY = "x-dead-letter-routing-key";

    public static final String RABBIT_REBATE_DEADLETTER_EXCHANGE = "qm-rebate-deadletter-exchange";
    public static final String RABBIT_REBATE_DEADLETTER_ROUTINGKEY = "qm-rebate-deadletter-routingkey";
    public static final String RABBIT_REBATE_DEADLETTER_QUEUE = "qm-rebate-deadletter-queue";

    /**
     * 业务mq配置
     */
    public static final String RABBIT_SYS_DEALER_EXCHANGE = "qm.sys.dealer.exchange";
    public static final String RABBIT_PRODUCTVIEW_EXCHANGE = "qm.productview.exchange";

    public static final String QM_REBATE_QUEUE_UPDATE_DEALER = "qm.rebate.queue.update.dealer";
    public static final String QM_REBATE_QUEUE_UPDATE_MULTI_TEXT = "qm.rebate.queue.update.multi.text";
    public static final String QM_REBATE_QUEUE_UPDATE_PRODUCT = "qm.rebate.queue.update.product";
    public static final String RABBIT_REBATE_Worklist_EXCHANGE = "todo_center_exchange";
    public static final String RABBIT_REBATE_Worklist_ROUTINGKEY = "topic.todo_center.todo.basic";
    public static final String MIRROR_FORMAL_CALC_RESULT_EXCHANGE = "qm.rebatecalc.mirror.formal.calc.result.exchange";
    public static final String MIRROR_FORMAL_CALC_RESULT_ROUTING_KEY = "mirror_result";

}
