package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:数据DIC枚举")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DicEnum {
    @Schema(description = "数据DICT 项目列表")
    private List<BaseEnumVO> dictItemList;
}
