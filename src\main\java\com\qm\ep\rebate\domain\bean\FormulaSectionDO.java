package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 计算公式区间赋值表
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-计算公式区间赋值表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value ="formulasection")
public class FormulaSectionDO implements Serializable {
    /**
     *
     */
    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 计算公式主表id
     */
    @Schema(description = "数据-计算公式主表id")
    @TableField(value = "forid")
    private String forid;

    /**
     * 筛选字段
     */
    @Schema(description = "数据-筛选字段")
    @TableField(value = "fieldname")
    private String fieldname;

    /**
     * 运算1
     */
    @Schema(description = "数据-运算1")
    @TableField(value = "caltype1")
    private String caltype1;

    /**
     * 筛选方式1
     */
    @Schema(description = "数据-筛选方式1")
    @TableField(value = "filtratetype1")
    private String filtratetype1;

    /**
     * 筛选条件1
     */
    @Schema(description = "数据-筛选条件1")
    @TableField(value = "filtrate1")
    private String filtrate1;

    /**
     * 关联逻辑
     */
    @Schema(description = "数据-关联逻辑")
    @TableField(value = "contype")
    private String contype;

    /**
     * 运算2
     */
    @Schema(description = "数据-运算2")
    @TableField(value = "caltype2")
    private String caltype2;

    /**
     * 筛选方式2
     */
    @Schema(description = "数据-筛选方式2")
    @TableField(value = "filtratetype2")
    private String filtratetype2;

    /**
     * 筛选条件2
     */
    @Schema(description = "数据-筛选条件2")
    @TableField(value = "filtrate2")
    private String filtrate2;

    /**
     * 结果类型
     */
    @Schema(description = "数据-结果类型")
    @TableField(value = "resulttype")
    private String resulttype;

    /**
     * 结果值
     */
    @Schema(description = "数据-结果值")
    @TableField(value = "resultvalue")
    private String resultvalue;

}