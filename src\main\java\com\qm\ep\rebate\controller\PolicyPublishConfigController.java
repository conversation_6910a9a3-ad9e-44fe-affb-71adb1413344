package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.bean.PolicyPublishConfigDO;
import com.qm.ep.rebate.domain.dto.PolicyPublishConfigDTO;
import com.qm.ep.rebate.domain.dto.PolicyPublishConfigGetDTO;
import com.qm.ep.rebate.domain.dto.PolicyPublishConfigSaveDTO;
import com.qm.ep.rebate.mapper.PolicyMapper;
import com.qm.ep.rebate.service.PolicyPublishConfigService;
import com.qm.ep.rebate.service.PolicyService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 *
 * Controller
 * 政策发布配置JsonResultVo
 *
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Tag(name = "政策发布配置")
@RestController
@Slf4j
@RequestMapping("/policyPublishConfig")
public class PolicyPublishConfigController extends BaseController {

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private PolicyPublishConfigService policyPublishConfigService;

    @Autowired
    private PolicyService policyService;
    @Resource
    private TransactionDefinition transactionDefinition;
    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;


    /**
     *使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<PolicyPublishConfigDO> save(@RequestBody PolicyPublishConfigSaveDTO tempDTO){ JsonResultVo<PolicyPublishConfigDO> resultObj = new JsonResultVo<>();
        PolicyDO policyDO = policyService.getById(tempDTO.getPolicyId());
        if(policyDO == null){
            resultObj.setMsgErr("未找到对应得政策配置！");
            return resultObj;
        }
        policyDO.setCanTrial(tempDTO.getCanTrial());
        policyDO.setTrailType(tempDTO.getTrailType());
        policyService.updateById(policyDO);
        if(CollUtil.isNotEmpty(tempDTO.getList())){
            tempDTO.getList().forEach(item -> item.setPolicyId(tempDTO.getPolicyId()));
            //先删除后创建
            LambdaQueryWrapper<PolicyPublishConfigDO> policyWapper = new QmQueryWrapper<PolicyPublishConfigDO>().lambda();
            policyWapper.eq(PolicyPublishConfigDO::getPolicyId, tempDTO.getPolicyId());
            policyPublishConfigService.remove(policyWapper);

            policyPublishConfigService.saveBatch(tempDTO.getList());
        }
        resultObj.setMsg("保存成功！");
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<List<PolicyPublishConfigGetDTO>> table(@RequestBody PolicyPublishConfigDTO tempDTO) {
        JsonResultVo<List<PolicyPublishConfigGetDTO>> ret = new JsonResultVo<>();
        if (StrUtil.isBlank(tempDTO.getPolicyId())) {
            ret.setMsgErr("政策ID[policyId]不能传空");
            return ret;
        }

        PolicyDO policyDO = policyMapper.selectPolicyById(tempDTO.getPolicyId());
        if (policyDO == null) {
            ret.setMsgErr("未找到对应得政策配置！");
            return ret;
        }
        QmQueryWrapper<PolicyPublishConfigDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyPublishConfigDO> lambdaQueryWrapper = queryWrapper.lambda();
        lambdaQueryWrapper.eq(PolicyPublishConfigDO::getPolicyId, tempDTO.getPolicyId());
        List<PolicyPublishConfigDO> dos = policyPublishConfigService.list(queryWrapper);
        if (CollUtil.isEmpty(dos)) {
            // 手动提交事务
            TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
            try {
                boolean flag = policyPublishConfigService.insertIntoPolicyPublishConfig(tempDTO.getPolicyId());
                dataSourceTransactionManager.commit(transactionStatus);
                if (flag) {
                    log.info("插入PolicyPublishConfig成功！");
                }
            } catch (Exception e) {
                log.error("异常：", e);
                dataSourceTransactionManager.rollback(transactionStatus);
            }
        }
        tempDTO.setVapplyorg(policyDO.getVapplyorg());
        tempDTO.setYearMonth(DateUtil.format(DateUtil.date(), "yyyyMM"));
        List<PolicyPublishConfigGetDTO> list = policyPublishConfigService.getTable(tempDTO);

        for (PolicyPublishConfigGetDTO dto : list) {
            dto.setTrailType(policyDO.getTrailType());
        }
        ret.setData(list);
        return ret;
    }
}
