package com.qm.ep.rebate.domain.dto.commonui;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.HashMap;

@Schema(description = "数据:实体类-搜索帮助 DTO")
@Data
/**
 * 搜索帮助参数
 */
public class SearchHelpDTO extends JsonParamDto {
    /**
     * 搜索帮助名字
     */
    @Schema(description = "数据-搜索帮助名字")
    private String name;

    /**
     * 条件信息
     */
    @Schema(description = "数据-条件信息")
    private HashMap<String, Object> condition;

    /**
     * 固定条件
     */
    @Schema(description = "数据-固定条件")
    private HashMap<String, Object> fixCondition;

    /**
     * 表格检索条件
     */
    @Schema(description = "数据-表格检索条件")
    private String pageInfo;

    /**
     * 是否需要分页  1 需要分页  2 不需要分页
     */
    @Schema(description = "数据-是否需要分页  1 需要分页  2 不需要分页")
    private String pageTye;
}
