package com.qm.ep.rebate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.ManagePeriodDO;
import com.qm.ep.rebate.domain.dto.ManagePeriodDTO;
import com.qm.ep.rebate.domain.dto.ValidationResultDTO;
import com.qm.ep.rebate.service.ManagePeriodService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/managePeriod")
@Tag(name = "经营周期")
public class ManagePeriodController extends BaseController {

    @Resource
    private ManagePeriodService managePeriodService;

    @Operation(summary = "获取经营周期列表 无分页", description = "[author: 10200571]")
    @PostMapping("/getList")
    public JsonResultVo<List<ManagePeriodDO>> getList(@RequestBody ManagePeriodDTO managePeriodDTO){
        JsonResultVo<List<ManagePeriodDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<ManagePeriodDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ManagePeriodDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq( managePeriodDTO.getYear()!=null,ManagePeriodDO::getYear, managePeriodDTO.getYear());
        /* 大于等于当前年 */
        lambdaWrapper.ge(StrUtil.isNotBlank(managePeriodDTO.getNewYear()),ManagePeriodDO::getYear,managePeriodDTO.getNewYear());
        List<ManagePeriodDO> data = managePeriodService.list(queryWrapper);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<ManagePeriodDO> save(@RequestBody ManagePeriodDTO managePeriodDTO){
        JsonResultVo<ManagePeriodDO> ret = new JsonResultVo<>();

        ValidationResultDTO validationResult = managePeriodService.validateName(managePeriodDTO);
        if(!validationResult.getOk()) {
            ret.setMsgErr(validationResult.getMsg());
            return ret;
        }

        ManagePeriodDO managePeriodDO = new ManagePeriodDO();
        if(StrUtil.isNotBlank(managePeriodDTO.getId())) {
            managePeriodDO = managePeriodService.getById(managePeriodDTO.getId());
        }

        BeanUtil.copyProperties(managePeriodDTO, managePeriodDO);

        managePeriodService.saveOrUpdate(managePeriodDO);
        ret.setData(managePeriodDO);
        return ret;
    }


    @Operation(summary = "获取详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<ManagePeriodDO> getDetail(@RequestBody ManagePeriodDTO managePeriodDTO){
        JsonResultVo<ManagePeriodDO> ret = new JsonResultVo<>();
        ManagePeriodDO managePeriodDO = managePeriodService.getById(managePeriodDTO.getId());
        ret.setData(managePeriodDO);
        return ret;
    }

    @Operation(summary = "删除", description = "[author: 10200571]")
    @PostMapping("/stopOrCancelByIds")
    public JsonResultVo deleteByIds(@RequestBody ManagePeriodDTO managePeriodDTO) {
        JsonResultVo<Object> ret = new JsonResultVo<>();
        if(CollUtil.isEmpty(managePeriodDTO.getIds())) {
            ret.setMsgErr("操作失败！操作列表不能为空！");
        } else {
            QmQueryWrapper<ManagePeriodDO> queryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ManagePeriodDO> lambdaWrapper = queryWrapper.lambda();
            lambdaWrapper.in(ManagePeriodDO::getId, managePeriodDTO.getIds());
            List<ManagePeriodDO> list = managePeriodService.list(queryWrapper);

            for (ManagePeriodDO managePeriodDO : list) {
                managePeriodDO.setStop(managePeriodDTO.getStop());
                managePeriodDO.setStopDate(managePeriodDTO.getStop() ? new Date() : null);
            }
            boolean update = managePeriodService.saveOrUpdateBatch(list);
            ret.setData(update);
        }
        return ret;
    }

    @Operation(summary = "自动生成经营周期", description = "[author: 10200571]")
    @PostMapping("/autoGenerate")
    public JsonResultVo<Boolean> autoGenerate(@RequestBody ManagePeriodDTO managePeriodDTO) {
        JsonResultVo<Boolean> ret = new JsonResultVo<>();
        Boolean ok = managePeriodService.autoGenerate(managePeriodDTO.getYear());
        ret.setData(ok);
        return ret;
    }
}
