package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy_copy_path")
@Schema(description = "数据实体")
public class PolicyCopyPathDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策id")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "数据-政策名称")
    @TableField("policyName")
    private String policyName;

    @Schema(description = "数据-复制从政策主键")
    @TableField("copyFromPolicyId")
    private String copyFromPolicyId;

    @Schema(description = "数据-从策略名称复制")
    @TableField("copyFromPolicyName")
    private String copyFromPolicyName;

    @Schema(description = "数据-创建者")
    @TableField(value = "createBy", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "createOn", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "updateBy", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-数据DTSTAMP")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

}
