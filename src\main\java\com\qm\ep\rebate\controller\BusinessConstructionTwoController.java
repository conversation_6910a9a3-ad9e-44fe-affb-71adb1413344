package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.service.BusinessConstructionTwoService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/businessConstructionTwo")
@Tag(name = "业务结构2", description = "[author: 10200571]")
public class BusinessConstructionTwoController extends BaseController {

    @Autowired
    private BusinessConstructionTwoService businessConstructionTwoService;

    @Operation(summary = "获取取值字段", description = "[author: 10200571]")
    @GetMapping("/getDataColumn")
    public JsonResultVo<String> getDataColumn(String policyId, String tableNames) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setDataList(businessConstructionTwoService.getDataColumn(policyId, tableNames));
        return result;
    }
}
