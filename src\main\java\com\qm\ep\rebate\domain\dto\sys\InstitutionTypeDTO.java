package com.qm.ep.rebate.domain.dto.sys;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:业务机构 类型  Institution Type")
@Data
public class InstitutionTypeDTO extends JsonParamDto {
    @Schema(description = "数据-公司ID")
    private String ncompanyId;
    @Schema(description = "数据-业务类型")
    private String vorgtype;
    @Schema(description = "数据-组织ID")
    private String norgid;
    @Schema(description = "数据-人员ID")
    private String npersonid;
    @Schema(description = "数据-标识")
    private String cbs;

    @Schema(description = "数据-公司ID集合")
    private List<String> ncomps;

    @Schema(description = "数据-权限类型")
    private String vauthoritytype;
}
