package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *  
 * 预算分解主表
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Data
@Schema(description = "数据:预算分解主表")
public class BgtBreakdownMainResponse implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "数据-给工作台的bizId")
    private String bizId;

    @Schema(description = "数据-预算表常规数据下发id")
    private String bgtEomReId;

    @Schema(description = "数据-预算表专项数据下发id")
    private String bgtEomSpId;

    @Schema(description = "数据-滚动销售额标识")
    private String bgtSeriesSaleUniqueKey;

    @Schema(description = "数据-预算时间维度（0-年度，1-季度）")
    private String timeType;

    @Schema(description = "数据-年")
    private String breakdownYear;

    @Schema(description = "数据-季度")
    private String breakdownQuarter;

    @Schema(description = "数据-使用状态")
    private String useStatus;

    @Schema(description = "数据-触发类型（01-EOM下发新版本，02-运营自调）")
    private String triggerType;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    private String bgtType;

    @Schema(description = "数据-版本号")
    private String version;

    @Schema(description = "数据-待办任务实例编码")
    private String taskInstanceCode;

    @Schema(description = "数据-发布状态（0-草稿，1-发布）")
    private String publishedStatus;

    @Schema(description = "数据-创建人姓名")
    private String submitName;

    @Schema(description = "数据-创建人域账号")
    private String submitCode;

    @Schema(description = "数据-创建日期")
    private Date createOn;

    @Schema(description = "数据-创建人")
    private String createBy;

    @Schema(description = "数据-更新者")
    private String updateBy;

    @Schema(description = "数据-更新日期")
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    private Date dtstamp;

    @Schema(description = "数据-该分解预算总点数（常规）")
    private BigDecimal breakdownTotalPoint;

    @Schema(description = "数据-该分解使用的预算点数")
    private BigDecimal breakdownUsedPoint;

    @Schema(description = "数据-该分解预算总点数余额")
    private BigDecimal breakdownPointBalance;

    @Schema(description = "数据-该分解预算总金额（专项）")
    private BigDecimal breakdownTotalAmount;

    @Schema(description = "数据-该分解使用的预算金额")
    private BigDecimal breakdownUsedAmount;

    @Schema(description = "数据-该分解预算金额余额")
    private BigDecimal breakdownAmountBalance;

    @Schema(description = "数据-该分解预算常规总金额")
    private BigDecimal breakdownReAmount;

    @Schema(description = "数据-该分解预算专项总金额")
    private BigDecimal breakdownSpAmount;

    @Schema(description = "数据-该分解预算常规+专项总金额")
    private BigDecimal breakdownAllAmount;

    @Schema(description = "数据-时间类型显示用")
    private String timeTypeName;

    @Schema(description = "数据-任务流实例编码")
    private String taskFlowinstanceCode;

    @Schema(description = "数据-预算分解任务实例编码")
    private String taskDecInstanceCode;

    @Schema(description = "数据-申请单主表id（关联触发的分解）")
    private Integer applyMainId;
}
