package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("TRIAL_OBJECT_TARGET")
@Schema(description = "计算对象和指标维度关联表")
public class TrialObjectTargetDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "数据-计算对象ID")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "数据-计算对象类型")
    @TableField("objectType")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "数据-指标ID")
    @TableField("targetId")
    private String targetId;

    @Schema(description = "数据-指标维度ID")
    @TableField("dimensionId")
    private String dimensionId;
}
