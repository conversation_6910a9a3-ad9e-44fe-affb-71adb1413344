package com.qm.ep.rebate.domain.dto.budget.in;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "预算申请 - 车系")
public class BgtApplySeriesInDTO {

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-调整数值")
    private BigDecimal modifyValue;

    @Schema(description = "数据-变换类型（0-减少，1-增加）")
    private String modifyType;

}
