package com.qm.ep.rebate.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * 钉钉配置
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@Configuration
@RefreshScope
@Data
public class DingDingConfig {

    @Value("${ding.config.app-key:dingnidf2jrqutg4ullj}")
    private String appKey;

    @Value("${ding.config.app-secret:3KfWVW9ilT9y94oDblvKPYcLcnnAqcnQO1M7EYLtVOiF5nQ47YmBB9kNgHJxmI-J}")
    private String appSecret;

    @Value("${ding.config.robotToken.policyBoard:10c709fa0e41812f824501adc3d4527ffd0ca1364eee5924f15d0e671e2e19fa}")
    private String policyBoardRobotToken;

    @Value("${ding.config.robotSecret.policyBoard:SECb465d3327408487dcd795aaf1c2ec1c4369274605955deaed9e85c35a05fa692}")
    private String policyBoardRobotSecret;

    @Value("${ding.config.robotUrl.policyBoard:https://oapi.dingtalk.com/robot/send}")
    private String policyBoardRobotUrl;

    @Value("${ding.config.url:https://oapi.dingtalk.com/}")
    private String dingUrl;

}
