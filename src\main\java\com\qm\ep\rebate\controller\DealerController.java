package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.dto.sal.Mdac100OutDTO;
import com.qm.ep.rebate.remote.feign.SalFeignClient;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "经销商")
@Slf4j
@RestController
@RequestMapping("/dealer")
public class DealerController {

    @Autowired
    private SalFeignClient salFeignClient;

    @Operation(summary = "查询经销商列表", description = "[author: 10200571]")
    @PostMapping("/getDealerList")
    public JsonResultVo<List<Mdac100OutDTO>> getDealerList() {
        return salFeignClient.getDealerList();
    }

    @Operation(summary = "查询代理商列表", description = "[author: 10200571]")
    @PostMapping("/getAgentList")
    public JsonResultVo<List<Mdac100OutDTO>> getAgentList() {
        return salFeignClient.getAgentList();
    }

}
