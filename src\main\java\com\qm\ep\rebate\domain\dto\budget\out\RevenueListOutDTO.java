package com.qm.ep.rebate.domain.dto.budget.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:收入明细封装对象")
@Data
public class RevenueListOutDTO {

    @Schema(description = "数据-收入明细信息")
    private List<BudgetRevenueListOutDTO> revenueList;

    @Schema(description = "数据-合计信息")
    private BudgetRevenueListOutDTO sumRevenue;

    @Schema(description = "数据-发布状态 0 - 未发布 1 - 已发布")
    private String publishStatus;
}
