package com.qm.ep.rebate.domain.dto.structure;

import com.baomidou.mybatisplus.annotation.TableField;
import com.qm.ep.rebate.domain.bean.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "前提条件")
public class PremiseDTO {
    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-前提名称")
    private String premiseName;
    @Schema(description = "数据-主结构")
    private PremiseMainDO main;
    @Schema(description = "数据-详情")
    private List<PremiseDetailDO> details;
    @Schema(description = "数据-排名")
    private List<RankDO> ranks;
    @Schema(description = "数据-比值")
    private List<CompareDO> compares;
    @Schema(description = "数据-筛选条件")
    private List<PremiseConditionsDO> conditions;

    @Schema(description = "是否经销商可见")
    @TableField("is_visible")
    private String isVisible;

    @Schema(description = "是否政策结果")
    @TableField("is_policy_result")
    private String isPolicyResult;

}