package com.qm.ep.rebate.common.config.mp;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import lombok.SneakyThrows;

import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;

public class CommonEnumCodeSerializer implements ObjectSerializer {

    @SneakyThrows
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        Class<?> enumClass = Class.forName(fieldType.getTypeName());
        Method getCode = enumClass.getDeclaredMethod("getCode");
        String code = (String) getCode.invoke(object);
        serializer.out.writeString(code);
    }
}
