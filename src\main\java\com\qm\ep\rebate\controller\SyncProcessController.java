package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.dto.workbench.TaskInstanceInfoResponseDTO;
import com.qm.ep.rebate.domain.dto.workbench.TriggerInfoDTO;
import com.qm.ep.rebate.domain.dto.workbench.TriggerTaskResponseDTO;
import com.qm.ep.rebate.domain.dto.workbench.WorkbenchResponseDTO;
import com.qm.ep.rebate.enumerate.RebateCodeEnum;
import com.qm.ep.rebate.service.PolicyService;
import com.qm.ep.rebate.service.workbench.WorkbenchService;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Single;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static com.qm.ep.rebate.enumerate.RebateCodeEnum.PZJSH;

/**
 * 同步每一个流程的任务
 * <AUTHOR>
 */
@Tag(description = "/sync", name = "同步每一个流程的任务")
@RestController
@RequestMapping("/sync")
public class SyncProcessController {

    @Resource
    private PolicyService policyService;

    @Resource
    private WorkbenchService workbenchService;


    @Operation(summary = "控制器接口", description = "[author: ********]")
    @GetMapping("/processPolicy")
    public String processPolicy(String id) {
        QmQueryWrapper<PolicyDO> wrapper = new QmQueryWrapper<>();
        wrapper.isNull("taskFlowinstanceCode");
        List<PolicyDO> policy = policyService.list();
        processPolicyDo(policy)
                .map(it -> {
                    for (PolicyDO policyDO : it) {
                        policyDO.setPublishedId(id);
                    }
                    policyService.saveOrUpdateBatch(it);
                    return "success";
                }).subscribe();
        return id;
    }

    @Operation(summary = "控制器接口", description = "[author: ********]")
    public Single<List<PolicyDO>> processPolicyDo(List<PolicyDO> policyDOList) {
        return Flowable.fromIterable(policyDOList)
                .parallel()
//                .runOn(Schedulers.io())
                .flatMap(this::processPolicyDoAction)
                .sequential()
                .toList();

    }

    @Operation(summary = "控制器接口", description = "[author: ********]")
    private Flowable<PolicyDO> processPolicyDoAction(PolicyDO policyDO) {
        return Flowable.unsafeCreate(sub -> {
            List<TriggerInfoDTO> triggerInfoList = new ArrayList<>();
            TriggerInfoDTO triggerInfoDTO = new TriggerInfoDTO();
            // todo 查询域账户
            // List<UserIntegrateVO> userIntegrateVOS = policyService.getUserVo(policyDO.getCreateBy());
            if (true) {
                String personCode = "qiuming1";// userIntegrateVOS.get(0).getLoginAccount();
                triggerInfoDTO.setUserCode(personCode);
                triggerInfoDTO.setDescription("政策配置-政策名称：" + policyDO.getVpolicyname());
                triggerInfoList.add(triggerInfoDTO);
                TriggerTaskResponseDTO task = workbenchService.triggerStartTask(RebateCodeEnum.XXWH.getCode(), personCode, "");
                // 发布数据
                List<TaskInstanceInfoResponseDTO> taskList = task.getTaskInstanceInfoList();
//            log.info("返回的开始数据：{}条", taskList.size());
                if (!taskList.isEmpty()) {

                    TaskInstanceInfoResponseDTO taskInstanceInfoResponseDTO = taskList.get(0);
                    String taskinsanceCode = taskInstanceInfoResponseDTO.getTaskInstanceCode();

                    WorkbenchResponseDTO taskResponseDTO = workbenchService.triggerAndCloseTask(taskinsanceCode, RebateCodeEnum.XXWH.getCode(), PZJSH.getCode(), triggerInfoList);
                    TriggerTaskResponseDTO taskResponse = taskResponseDTO.getTriggerTaskResponse();
                    List<TaskInstanceInfoResponseDTO> infoList = taskResponse.getTaskInstanceInfoList();
                    if (!infoList.isEmpty()) {
                        policyDO.setTaskinsanceCode(infoList.get(0).getTaskInstanceCode());
                        policyDO.setTaskFlowinstanceCode(infoList.get(0).getTaskFlowInstanceCode());
                    }
                }

            }

            sub.onNext(policyDO);
            sub.onComplete();
        });
    }

//     @Operation(summary = "控制器接口", description = "[author: ********]")
//     private Single<List<PolicyPublicDto>> processPublish(List<PolicyPublishedDO> policyPublishedDOS) {
//         return Flowable.fromIterable(policyPublishedDOS)
//                 .parallel()
// //                .runOn(Schedulers.io())
//                 .flatMap(this::processAction)
//                 .sequential()
//                 .toList();
//
//     }


//     @Operation(summary = "控制器接口", description = "[author: ********]")
//     private Flowable<PolicyPublicDto> processAction(PolicyPublishedDO publishedDO) {
//         return Flowable.unsafeCreate(sub -> {
//             List<TriggerInfoDTO> triggerInfoList = new ArrayList<>();
//             String loginAccount = auditApproveMapper.getLoginName(publishedDO.getCreateby());
// //            Result<UserIntegrateVO> data =  userFeigner.findUser(UserIntegrateRequestVO.builder().userId(publishedDO.getCreateby()).build());
//             if ("tds50".equals(loginAccount)){
//                 loginAccount ="qiuming1";
//             }
//             if (StringUtils.isEmpty(loginAccount)){
//                 PolicyPublicDto dto = new PolicyPublicDto();
//                 dto.setPublishedDO(publishedDO);
//                 dto.setPolicyConfirmInfoDOList(new ArrayList<>());
//                 sub.onNext(dto);
//                 sub.onComplete();
//                 return;
//
//             }
//             triggerInfoList.add(TriggerInfoDTO.builder()
//                     .userCode(loginAccount)
//                     .bizId("POLICY_ID_PRE" + publishedDO.getId())
//                     .description("政策信息维护-政策名称：" + publishedDO.getVpolicyname())
//                     .build());
//             TriggerTaskResponseDTO triggerTaskResponseDTO = workbenchService.triggerStartTask(RebateCodeEnum.XXWH.getCode(), triggerInfoList);
//
//             List<TaskInstanceInfoResponseDTO> taskInstanceInfoResponseDTOList = triggerTaskResponseDTO.getTaskInstanceInfoList();
//
//             List<PolicyConfirmInfoDO> policyConfirmInfoDOList = new ArrayList<>();
//             for (TaskInstanceInfoResponseDTO item : taskInstanceInfoResponseDTOList) {
//                 PolicyConfirmInfoDO policyConfirmInfoDO = new PolicyConfirmInfoDO();
//                 policyConfirmInfoDO.setTaskflowinstancecode(item.getTaskFlowInstanceCode());
//                 policyConfirmInfoDO.setTaskinstancecode(item.getTaskInstanceCode());
//                 policyConfirmInfoDO.setAgent(item.getDistributeUserCode());
//                 policyConfirmInfoDO.setVpolicyid(item.getBizId().replace("POLICY_ID_PRE", ""));
//                 policyConfirmInfoDOList.add(policyConfirmInfoDO);
//                 publishedDO.setTaskflowinstancecode(item.getTaskFlowInstanceCode());
//             }
//             PolicyPublicDto dto = new PolicyPublicDto();
//             dto.setPublishedDO(publishedDO);
//             dto.setPolicyConfirmInfoDOList(policyConfirmInfoDOList);
//             sub.onNext(dto);
//             sub.onComplete();
//
//
//
//         });
//     }

    // @Data
    // class PolicyPublicDto {
    //     PolicyPublishedDO publishedDO;
    //     List<PolicyConfirmInfoDO> policyConfirmInfoDOList;
    // }

    // @Operation(summary = "控制器接口", description = "[author: ********]")
    // public static void main(String[] args) {
    //     List<String> ids = Lists.newArrayList();
    //     ids.add("1");
    //     ids.add("2");
    //     List<String> ids2 = Lists.newArrayList();
    //     ids2.add("3");
    //     ids2.add("4");
    //     List<List<String>> list = Lists.newArrayList();
    //     list.add(ids);
    //     list.add(ids2);
    //
    //     List<String> data = list.stream().collect(ArrayList::new, ArrayList::addAll,(k, v)-> {});
    //     for (String da :data){
    //         System.out.println(da);
    //     }
    // }

}
