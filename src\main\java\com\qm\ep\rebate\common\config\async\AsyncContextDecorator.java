package com.qm.ep.rebate.common.config.async;

import com.qm.ep.rebate.common.constant.CommonConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.TaskDecorator;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 线程上下文拷贝 TaskDecorator
 */
public class AsyncContextDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        ServletRequestAttributes context = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String tenantId = null;
        if (null != context) {
            HttpServletRequest request = context.getRequest();
            tenantId = request.getParameter(CommonConstants.TENANT_ID);
            if (StringUtils.isBlank(tenantId)) {
                tenantId = request.getHeader(CommonConstants.TENANT_ID);
            }
        }
        if(null == tenantId) {
            tenantId = HeaderContextHolder.getHeader(CommonConstants.TENANT_ID);
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(CommonConstants.TENANT_ID, tenantId);

        return () -> {
            try {
                RequestContextHolder.setRequestAttributes(context);
                HeaderContextHolder.setContext(headerMap);
                runnable.run();
            } finally {
                RequestContextHolder.resetRequestAttributes();
                HeaderContextHolder.removeContext();
            }
        };
    }
}