package com.qm.ep.rebate.domain.dto.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2023/7/4 16:58
 * @version: $
 */
@Schema(description = "数据:实体类-：")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessHistoryResultDTO {

    @Schema(description = "数据-水平")
    private String level;
    @Schema(description = "数据-标题")
    private String title;
    @Schema(description = "数据-州")
    private String state;
    @Schema(description = "数据-备注")
    private String remark;
    @Schema(description = "数据-时间")
    private Date time;
    @Schema(description = "数据-开始时间")
    private Date startTime;
    @Schema(description = "数据-批准代码")
    private String approvercode;
    @Schema(description = "数据-审批者")
    private String approver;
}
