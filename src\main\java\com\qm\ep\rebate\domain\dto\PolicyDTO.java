package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 *
 * 政策基础信息表
 *
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:政策基础信息表")
@Data
public class PolicyDTO extends JsonParamDto {

    /**
     * serialVersionUID
     */
    @Schema(description = "数据-serialVersionUID")
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @Schema(description = "数据-id")
    private String id;
    /**
     * policyId
     */
    @Schema(description = "数据-policyId")
    private String policyId;
    /**
     * vpolicycode
     */
    @Schema(description = "数据-政策代码")
    private String vpolicycode;
    /**
     * vpolicyname
     */
    @Schema(description = "数据-政策名称")
    private String vpolicyname;
    /**
     * vpolicydesc
     */
    @Schema(description = "数据-政策描述")
    private String vpolicydesc;
    /**
     * startTime
     */
    @Schema(description = "数据-创建开始时间")
    private Date startTime;
    /**
     * endTime
     */
    @Schema(description = "数据-创建结束时间")
    private Date endTime;
    /**
     * vapplyprdt
     */
    @Schema(description = "数据-关联车型")
    private String vapplyprdt;
    /**
     * vapplyorg
     */
    @Schema(description = "数据-关联经销商")
    private String vapplyorg;
    /**
     * vtscartype
     */
    @Schema(description = "数据-特殊车辆设定")
    private String vtscartype;
    /**
     * vfinishstates
     */
    @Schema(description = "数据-完成状态")
    private List<String> vfinishstates;
    /**
     * vsmttypes
     */
    @Schema(description = "数据-结算类型")
    private List<String> vsmttypes;
    /**
     * policyTypes
     */
    @Schema(description = "数据-政策类型")
    private List<String> policyTypes;
    /**
     * datetype
     */
    @Schema(description = "数据-日期类型")
    private String datetype;

    /**
     * orgCode
     */
    @Schema(description = "数据-orgCode")
    private String orgCode;
    /**
     * aimVersion
     */
    @Schema(description = "数据-aimVersion")
    private String aimVersion;
    /**
     * auditLevel
     */
    @Schema(description = "数据-auditLevel")
    private String auditLevel;
    /**
     * searchSelf
     */
    @Schema(description = "数据-searchSelf")
    private String searchSelf;

    /**
     * nodeStatus
     */
    @Schema(description = "数据-政策时间轴-节点状态")
    private List<String> nodeStatus;
    /**
     * dbegin
     */
    @Schema(description = "数据-dbegin")
    private String dbegin;
    /**
     * dend
     */
    @Schema(description = "数据-dend")
    private String dend;
    /**
     * vpolicycodehq
     */
    @Schema(description = "数据-vpolicycodehq")
    private String vpolicycodehq;

    /**
     * userCode
     */
    @Schema(description = "数据-userCode")
    private String userCode;
    /**
     * userType
     */
    @Schema(description = "数据-userType")
    private String userType;
    /**
     * skipFlag
     */
    @Schema(description = "数据-skipFlag")
    private String skipFlag;

    /**
     * predictMonth
     */
    @Schema(description = "数据-预估年月-bx")
    private  String predictMonth;
    /**
     * brands
     */
    @Schema(description = "数据-品牌-bx")
    private List<String> brands;
    /**
     * createByName
     */
    @Schema(description = "数据-创建人-bx")
    private  String createByName;

    /**
     * dealerCode
     */
    @Schema(description = "数据-经销商编码")
    private  String dealerCode;

    /**
     * bgtType
     */
    @Schema(description = "数据-bgtType")
    private String bgtType;
}