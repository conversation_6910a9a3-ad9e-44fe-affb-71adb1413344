package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 *
 *
 *
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("DICT_MAIN")
@Schema(description = "数据:数据对象")
public class DictMainDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-s_codeid")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-字典代码")
    @TableField("VDICT")
    private String vdict;

    @Schema(description = "数据-字典名称")
    @TableField("VDICTNAME")
    private String vdictname;


    @Schema(description = "数据-扩展标识")
    @TableField("CEXTAND")
    private Integer cextand;

    @Schema(description = "数据-显示标识")
    @TableField("CVISIBLE")
    private Integer cvisible;
    

    @Schema(description = "数据-模块")
    @TableField("VMODULE")
    private String vmodule;


    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-父项字典代码")
    @TableField(value = "VPARENTDICT", fill = FieldFill.INSERT_UPDATE)
    private String vparentdict;

    @Schema(description = "数据-更新时间")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "数据-父项字典名称")
    @TableField(value = "NAME", exist = false)
    private String name;

    @Schema(description = "数据-备注")
    @TableField("VREMARKS")
    private String vremarks;

    @Schema(description = "数据-停用标识")
    @TableField("VSTOP")
    private Integer vstop;

    @Schema(description = "数据-删除标识")
    @TableField("ISDEL")
    private Integer isdel;

    //这个字段给维护角色字典授权用的
    @Schema(description = "数据-字典代码")
    @TableField(value = "VDICTIONARYCODE", exist = false)
    private String vdictionarycode;

    @Schema(description = "数据-创建人姓名")
    @TableField(value = "CREATEBYNAME", exist = false)
    private String createbyname;

    @Schema(description = "数据-修改人姓名")
    @TableField(value = "UPDATEBYNAME", exist = false)
    private String updatebyname;

}
