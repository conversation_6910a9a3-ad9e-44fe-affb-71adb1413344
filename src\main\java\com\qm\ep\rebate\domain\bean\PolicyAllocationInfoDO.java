package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy_allocation_info")
@Schema(description = "数据:政策分配政策担当信息")
public class PolicyAllocationInfoDO {

    @Schema(description = "数据-主键")
    @TableField("ID")
    private Long id;

    @Schema(description = "数据-政策id")
    @TableField("VPOLICYID")
    private String vpolicyid;

    @Schema(description = "数据-政策编号")
    @TableField("VPOLICYCODE")
    private String vpolicycode;

    @Schema(description = "数据-政策名称")
    @TableField(exist = false)
    private String vpolicyname;

    @Schema(description = "数据-部门 存id，逗号分割")
    @TableField("DEPARTMENT")
    private String department;

    @Schema(description = "数据-部门名称  逗号分割 冗余存储")
    @TableField("DEPARTMENTNAME")
    private String departmentname;

    @Schema(description = "数据-政策担当 存code，逗号分割")
    @TableField("AGENT")
    private String agent;

    @Schema(description = "数据-政策担当名称 ，逗号分割 冗余存储")
    @TableField("AGENTNAME")
    private String agentname;

    @Schema(description = "数据-任务实例编码")
    @TableField("TASKINSTANCECODE")
    private String taskinstancecode;

    @Schema(description = "数据-任务流实例编码")
    @TableField("TASKFLOWINSTANCECODE")
    private String taskflowinstancecode;

    @Schema(description = "数据-政策经办人用户编码")
    @TableField("RESPONSIBILITYUSERCODE")
    private String responsibilityusercode;

    @Schema(description = "数据-创建时间")
    @TableField(value = "CREATETIME", fill = FieldFill.INSERT)
    private Date createtime;

    /**
     * 预算功能 - 新增字段
     */
    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    @TableField("budget_type")
    private String budgetType;

    @Schema(description = "数据-返利项目代码")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    @TableField("classItemName")
    private String classItemName;

    @Schema(description = "数据-政策担当待办任务状态（0-未关闭，1-已关闭）")
    @TableField("task_status")
    private String taskStatus;



    @Schema(description = "政策目标")
    @TableField(exist = false)
    private String policyTarget;

    @Schema(description = "其他目标名称")
    @TableField(exist = false)
    private String otherTargetName;

    @Schema(description = "其他目标值")
    @TableField(exist = false)
    private String otherTargetValue;

    @Schema(description = "政策目标车系")
    @TableField(exist = false)
    private String policyTargetSeries;

    @Schema(description = "政策车系")
    @TableField(exist = false)
    private List<String> series;

    @Schema(description = "执行开始时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @Schema(description = "执行结束时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @TableField(exist = false)
    @Schema(description = "经办人姓名 从系统表中获取")
    private String agentNameFromSysc000M;

}