package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.qm.ep.rebate.enumerate.JoinTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("FORMULA_SECTION_JOIN")
@Schema(description = "数据:计算区间关联表对象")
public class FormulaSectionJoinDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策主键")
    @TableField("POLICY_ID")
    private String policyId;

    @Schema(description = "数据-计算区间主键")
    @TableField("SECTION_ID")
    private String sectionId;

    @Schema(description = "数据-关联数据源ID")
    @TableField("TABLE_OBJECT")
    private String tableObject;

    @Schema(description = "数据-关联数据源对象")
    @TableField(exist=false)
    private CalcObjectSourceDO tableObjectSource;

    @Schema(description = "数据-关联关系")
    @TableField("JOIN_TYPE")
    private JoinTypeEnum joinType;

    @Schema(description = "数据-关联条件")
    @TableField(exist=false)
    private List<FormulaSectionJoinOnDO> joinOns;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    /**
     * 备注
     */
    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;

}
