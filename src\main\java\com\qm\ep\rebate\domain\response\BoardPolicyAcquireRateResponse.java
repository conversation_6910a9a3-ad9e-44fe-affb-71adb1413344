package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "数据:董事会政策获取利率响应")
@Data
public class BoardPolicyAcquireRateResponse {

    /**
     * 政策获取率百分比
     */
    @Schema(description = "政策获取率百分比")
    private String acquireRate;
    /**
     * 环比(较昨日)
     */
    @Schema(description = "环比(较昨日)")
    private String qqq;
    /**
     * 同比(较去年该日)
     */
    @Schema(description = "同比(较去年该日)")
    private String yqy;

    /**
     * 分子
     */
    @Schema(description = "分子")
    private Integer up;

    /**
     * 分母
     */
    @Schema(description = "分母")
    private Integer down;

}
