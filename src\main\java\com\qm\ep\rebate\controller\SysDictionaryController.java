package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.AuditTrailDO;
import com.qm.ep.rebate.domain.bean.sys.SysDictChildDO;
import com.qm.ep.rebate.domain.dto.AuditTrailDTO;
import com.qm.ep.rebate.domain.dto.sys.SysDictionaryDTO;
import com.qm.ep.rebate.enumerate.AuditPrefixEnum;
import com.qm.ep.rebate.service.AuditTrailService;
import com.qm.ep.rebate.service.DictionaryService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/useSys/dictionary")
@Tag(name = "系统模块字典")
public class SysDictionaryController extends BaseController {

    @Resource
    private DictionaryService dictionaryService;

    @Resource
    private AuditTrailService auditTrailService;


    @Operation(summary = "通过字典代码，获取所有字典子项", description = "[author: 10200571]")
    @PostMapping("/getDictChildrenByDictCode")
    public JsonResultVo<List<SysDictChildDO>> getDictChildrenByDictCode(@RequestBody SysDictionaryDTO sysDictionaryDTO) {
        JsonResultVo<List<SysDictChildDO>> ret = new JsonResultVo<>();
        List<SysDictChildDO> children = dictionaryService.getDictChildrenByDictCodeUseSys(sysDictionaryDTO.getDictCode());
        ret.setData(children);
        return ret;
    }

    @Operation(summary = "保存字典子表数据", description = "[author: 10200571]")
    @PostMapping("/saveDictChildren")
    public JsonResultVo saveDictChildren(@RequestBody SysDictionaryDTO sysDictionaryDTO) {
        JsonResultVo<Object> ret = new JsonResultVo<>();
        boolean ok = dictionaryService.saveDictChildrenUseSys(sysDictionaryDTO);
        ret.setData(ok);
        return ret;
    }

    @Operation(summary = "获取审核操作轨迹", description = "[author: 10200571]")
    @PostMapping("/list")
    public JsonResultVo<List<AuditTrailDO>> list(@RequestBody AuditTrailDTO auditTrailDTO){

        JsonResultVo<List<AuditTrailDO>> jsonResultVo = new JsonResultVo<>();

        List<AuditTrailDO> data = auditTrailService.getList(auditTrailDTO);
        List<SysDictChildDO> typeDesc = dictionaryService.getDictChildrenByDictCodeUseSys("POLICY_STATES");
        List<SysDictChildDO> type2Desc = dictionaryService.getDictChildrenByDictCodeUseSys("FIN_ENTRY_STATES");
        List<SysDictChildDO> rbtpolicyacct = dictionaryService.getDictChildrenByDictCodeUseSys("RBTPOLICYACCT");

        typeDesc.addAll(type2Desc);
        data.forEach(item->{
            if("accountMode".equals(item.getTypeDesc())){
                String name = rbtpolicyacct.stream().filter(dict -> item.getFullMessage().equals(dict.getDictChild()))
                        .map(SysDictChildDO::getDictChildName).collect(Collectors.toList()).get(0);
                item.setTypeDesc("入账方式变更");
                item.setFullMessage("将入账方式更改为："+name);
            }else{
                List<String> names = typeDesc.stream().filter(dict -> item.getTypeDesc().equals(dict.getDictChild()))
                        .map(SysDictChildDO::getDictChildName).collect(Collectors.toList());
                String name = names.isEmpty()?"":names.get(0);
                item.setTypeDesc(!BootAppUtil.isNullOrEmpty(item.getPrefix())?AuditPrefixEnum.CANCEL.getDesc() + name:name);
            }

        });
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

}
