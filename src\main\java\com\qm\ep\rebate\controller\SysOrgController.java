package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.qm.ep.rebate.domain.bean.sys.SysOrgDO;
import com.qm.ep.rebate.domain.bean.sys.SysPersonDO;
import com.qm.ep.rebate.domain.bean.sys.SysPersonOrgDO;
import com.qm.ep.rebate.domain.dto.sys.SysPersonDTO;
import com.qm.ep.rebate.domain.dto.sys.SysPersonOrgDTO;
import com.qm.ep.rebate.service.SysOrgService;
import com.qm.ep.rebate.service.SysPersonOrgService;
import com.qm.ep.rebate.service.SysPersonService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.domain.LoginKeyDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@Tag(name = "用户关联组织机构")
@RestController
@RequestMapping("/sysPersonOrg")
public class SysOrgController extends BaseController {

    @Resource
    private SysPersonOrgService sysPersonOrgService;
    @Resource
    private SysOrgService sysOrgService;
    @Resource
    private SysPersonService sysPersonService;

    @Operation(summary = "查询经销商列表", description = "[author: 10200571]")
    @PostMapping("/dealerTable")
    public JsonResultVo<QmPage<SysOrgDO>> dealerTable(@RequestBody SysPersonOrgDTO sysPersonOrgDTO){
        LoginKeyDO userInfo = getUserInfo();
        sysPersonOrgDTO.setPersonId(userInfo.getOperatorId());
        JsonResultVo<QmPage<SysOrgDO>> result = new JsonResultVo<>();
        QmPage<SysOrgDO> page = sysOrgService.queryDealerTable(sysPersonOrgDTO);
        result.setData(page);
        return result;
    }

    @Operation(summary = "查询用户所在机构", description = "[author: 10200571]")
    @GetMapping("/getPersonOrg")
    public JsonResultVo<SysOrgDO> getPersonOrg(String personId){
        JsonResultVo<SysOrgDO> result = new JsonResultVo<>();
        if(CharSequenceUtil.isBlank(personId)){
            return result;
        }
        SysOrgDO orgDO = sysPersonOrgService.queryPersonOrg(personId);
        result.setData(orgDO);
        return result;
    }

    @Operation(summary = "查询用户所属机构类型", description = "[author: 10200571]")
    @GetMapping("/getOrgType")
    public JsonResultVo<List<SysPersonOrgDO>> getOrgType(String personId){
        JsonResultVo<List<SysPersonOrgDO>> result = new JsonResultVo<>();
        if(CharSequenceUtil.isBlank(personId)){
            result.setData(Collections.emptyList());
            return result;
        }
        List<SysPersonOrgDO> orgTypeList = sysPersonOrgService.queryOrgType(personId);
        result.setData(orgTypeList);
        return result;
    }

    @Operation(summary = "判断是否是顶层人员", description = "[author: 10200571]")
    @PostMapping("/checkPersonAtTopLevel")
    public JsonResultVo<Boolean> checkPersonAtTopLevel(@RequestBody String personId){
        JsonResultVo<Boolean> result = new JsonResultVo<>();
        if(CharSequenceUtil.isBlank(personId)){
            result.setData(false);
            return result;
        }
        boolean isAdmin = sysPersonOrgService.checkPersonAtTopLevel(personId);
        result.setData(isAdmin);
        return result;
    }

    @Operation(summary = "判断是否是顶层人员", description = "[author: 10200571]")
    @PostMapping("/checkPersonAtBottomLevel")
    public JsonResultVo<Boolean> checkPersonAtBottomLevel(@RequestBody String personId){
        JsonResultVo<Boolean> result = new JsonResultVo<>();
        if(CharSequenceUtil.isBlank(personId)){
            result.setData(false);
            return result;
        }
        boolean isAdmin = sysPersonOrgService.checkPersonAtBottomLevel(personId);
        result.setData(isAdmin);
        return result;
    }

    @Operation(summary = "获取同一级用户列表", description = "[author: 10200571]")
    @PostMapping("/getSameLevelPerson")
    public JsonResultVo<List<String>> getPersonIdAtSameLevel(@RequestBody String personId){
        JsonResultVo<List<String>> result = new JsonResultVo<>();
        if(CharSequenceUtil.isBlank(personId)){
            result.setData(Collections.emptyList());
            return result;
        }
        List<String> personIdList = sysPersonOrgService.queryPersonIdAtSameLevel(personId);
        result.setData(personIdList);
        return result;
    }

    @Operation(summary = "获取相关层级用户列表", description = "[author: 10200571]")
    @GetMapping("/getRelatedLevelPerson")
    public JsonResultVo<List<String>> getPersonIdAtRelatedLevel(String personId){
        JsonResultVo<List<String>> result = new JsonResultVo<>();
        if(CharSequenceUtil.isBlank(personId)){
            result.setData(Collections.emptyList());
            return result;
        }
        List<String> personIdList = sysPersonOrgService.queryPersonIdAtRelatedLevel(personId);
        result.setData(personIdList);
        return result;
    }

    @Operation(summary = "获取最下层机构编码", description = "[author: 10200571]")
    @PostMapping("/getLeafOrg")
    public JsonResultVo<List<String>> getLeafOrg(@RequestBody String personId){
        JsonResultVo<List<String>> result = new JsonResultVo<>();
        if(CharSequenceUtil.isBlank(personId)){
            result.setData(Collections.emptyList());
            return result;
        }
        List<String> dealerCodeList = sysPersonOrgService.queryLeafOrg(personId);
        result.setData(dealerCodeList);
        return result;
    }

    @Operation(summary = "获取用户列表", description = "[author: 10200571]")
    @PostMapping("/userTable")
    public JsonResultVo<QmPage<SysPersonDO>> userTable(@RequestBody SysPersonDTO sysPersonDTO){
        JsonResultVo<QmPage<SysPersonDO>> result = new JsonResultVo<>();
        result.setData(sysPersonService.queryUserTable(sysPersonDTO));
        return result;
    }

    @Operation(summary = "获取用户列表2", description = "[author: 10200571]")
    @PostMapping("/getUserList")
    public JsonResultVo<List<SysPersonDO>> getUserList(@RequestBody List<String> personIdList){
        JsonResultVo<List<SysPersonDO>> result = new JsonResultVo<>();
        if(CollUtil.isEmpty(personIdList)){
            result.setData(Collections.emptyList());
            return result;
        }
        result.setData(sysPersonService.queryUserList(personIdList));
        return result;
    }



}
