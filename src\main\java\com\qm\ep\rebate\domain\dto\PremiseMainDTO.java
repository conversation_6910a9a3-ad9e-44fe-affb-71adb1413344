package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "前置条件主表")
@Data
public class PremiseMainDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-政策ID")
    private String policyId;

    @Schema(description = "数据-前置条件名称")
    private String premiseName;

    @Schema(description = "数据-前置条件描述")
    private String premiseDic;
}