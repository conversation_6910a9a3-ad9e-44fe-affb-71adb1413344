package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "ReportJoinDTO对象")
public class ReportJoinDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-报表ID")
    private String reportId;

    @Schema(description = "数据-关联类型")
    private String joinType;

    @Schema(description = "数据-计算方案标识")
    private String joinTable;

    @Schema(description = "数据-关联条件")
    private List<ReportJoinOnDTO> reportJoinOnDTOList;
}