package com.qm.ep.rebate.controller;

import com.alibaba.fastjson.JSONObject;
import com.qm.ep.rebate.domain.bean.commonui.SearchHelp;
import com.qm.ep.rebate.domain.bean.sal.SearchPaymentDetailDO;
import com.qm.ep.rebate.domain.bean.sal.SearchRebateDO;
import com.qm.ep.rebate.domain.bean.sal.SearchUsageDetailDO;
import com.qm.ep.rebate.domain.bean.sys.InstitutionDO;
import com.qm.ep.rebate.domain.dto.commonui.SearchHelpDTO;
import com.qm.ep.rebate.domain.dto.sal.SearchPaymentDetailDTO;
import com.qm.ep.rebate.domain.dto.sal.SearchRebateDTO;
import com.qm.ep.rebate.domain.dto.sal.SearchUsageDetailDTO;
import com.qm.ep.rebate.domain.dto.sys.InstitutionTypeDTO;
import com.qm.ep.rebate.service.SearchRebateService;
import com.qm.ep.rebate.infrastructure.util.QmPageUtil;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "查询返利往来账")
@RestController
@RequestMapping("/searchRebate")
public class SearchRebateController {

    @Autowired
    private SearchRebateService searchRebateService;

    @Operation(summary = "查询业务组织", description = "[author: 10200571]")
    @PostMapping(value = "/getTreeInstitutions")
    public JsonResultVo<List<InstitutionDO>> getTreeInstitutions(@RequestBody InstitutionTypeDTO institutionTypeDTO) {
        return searchRebateService.getTreeInstitutions(institutionTypeDTO);
    }

    @Operation(summary = "查询经销商表头", description = "[author: 10200571]")
    @PostMapping(value = "/getSearchHelpDef")
    public JsonResultVo<JSONObject> getSearchHelpDef(@RequestBody SearchHelp searchHelp) {
        return searchRebateService.getDealerInfoDef(searchHelp);
    }

    @Operation(summary = "查询经销商表体", description = "[author: 10200571]")
    @PostMapping(value = "/getSearchHelpData")
    public JsonResultVo getSearchHelpData(@RequestBody() SearchHelpDTO searchHelpDTO) throws Throwable {
        return searchRebateService.getDealerInfoData(searchHelpDTO);
    }

    @Operation(summary = "查询返利往来账", description = "[author: 10200571]")
    @PostMapping(value = "/rebateTable")
    public JsonResultVo<QmPage<SearchRebateDO>> table(@RequestBody SearchRebateDTO tempDTO) {
        return searchRebateService.rebateTable(tempDTO);
    }

    @Operation(summary = "查询收款明细", description = "[author: 10200571]")
    @PostMapping(value = "/getPaymentDetailList")
    public JsonResultVo<QmPageUtil<SearchPaymentDetailDO>> getPaymentDetailList(@RequestBody SearchPaymentDetailDTO tempDTO) {
        return searchRebateService.getPaymentDetailList(tempDTO);
    }

    @Operation(summary = "查询使用明细", description = "[author: 10200571]")
    @PostMapping(value = "/getUsageDetailList")
    public JsonResultVo<QmPageUtil<SearchUsageDetailDO>> getUsageDetailList(@RequestBody SearchUsageDetailDTO tempDTO) {
        return searchRebateService.getUsageDetailList(tempDTO);
    }

    @Operation(summary = "查询返利往来账 - 业务组织 - 获取personid", description = "[author: 10200571]")
    @GetMapping("/getPersonId")
    public JsonResultVo<String> getPersonId() {
        return searchRebateService.getPersonId();
    }

}
