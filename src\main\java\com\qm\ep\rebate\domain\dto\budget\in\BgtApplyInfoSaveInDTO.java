package com.qm.ep.rebate.domain.dto.budget.in;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "预算申请保存DTO")
public class BgtApplyInfoSaveInDTO {

    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-预算申请单名")
    private String applyName;

    @Schema(description = "数据-预算申请类型（0-常规调整，1-专项调整）")
    private String applyType;

    @Schema(description = "数据-申请数")
    private BigDecimal applyValue;

    @Schema(description = "数据-申请日期")
    private String applyTime;

    @Schema(description = "数据-描述（补充说明）")
    private String remark;

    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-申请状态（10-初始、11-提交、12-通过、13驳回）")
    private String applyStatus;

    @Schema(description = "数据-预算分解mainid")
    private Integer mainId;

    @Schema(description = "数据-车系集合")
    List<BgtApplySeriesInDTO> seriesList;

    @Schema(description = "数据-达成目标集合")
    List<BgtApplyAimInDTO> aimList;

    @Schema(description = "数据-文件列表")
    List<BgtApplyFileInDTO> fileList;

    @Schema(description = "数据-任务实例代码")
    private String taskInstanceCode;
}
