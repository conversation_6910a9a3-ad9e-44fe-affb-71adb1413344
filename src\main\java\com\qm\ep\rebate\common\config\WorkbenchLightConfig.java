package com.qm.ep.rebate.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * 工作台灯塔指标配置信息
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@Configuration
@RefreshScope
@Data
public class WorkbenchLightConfig {

    @Value("${light.publish.KeyIndexCode:BI-000032}")
    private String lightPublishKeyIndexCode;

    @Value("${light.publish.lightPublishSequence:2}")
    private String lightPublishSequence;

    @Value("${light.pay.KeyIndexCode:BI-000033}")
    private String lightPayKeyIndexCode;

    @Value("${light.pay.lightPaySequence:3}")
    private String lightPaySequence;
    @Value("${light.settle.KeyIndexCode:BI-000031}")
    private String lightSettleKeyIndexCode;

    @Value("${light.settle.lightSettleSequence:1}")
    private String lightSettleSequence;

    @Value("${light.acquire.KeyIndexCode:BI-000088}")
    private String lightAcquireKeyIndexCode;

    @Value("${light.acquire.lightAcquireSequence:4}")
    private String lightAcquireSequence;

    @Value("${light.increasing:2}")
    private Integer lightIncreasing;

    @Value("${light.decreasing:1}")
    private Integer lightDecreasing;


}
