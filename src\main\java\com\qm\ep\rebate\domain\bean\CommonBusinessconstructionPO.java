package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * 业务结构表
 *
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Getter
@Setter
@TableName("common_businessconstruction")
public class CommonBusinessconstructionPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 业务底表ID
     */
    @TableField("table_id")
    private String tableId;

    /**
     * 业务底表名
     */
    @TableField("tablename")
    private String tablename;

    /**
     * 业务底表字段名
     */
    @TableField("fieldname")
    private String fieldname;

    /**
     * 业务底表字段类型
     */
    @TableField("field_type")
    private String fieldType;

    /**
     * 数值型保留的小数位数
     */
    @TableField("decimalPoint")
    private Integer decimalPoint;

    /**
     * 必填项(0-不是,1-是)
     */
    @TableField("required")
    private Boolean required;

    /**
     * 联合唯一标识
     */
    @TableField("unique_key")
    private Boolean uniqueKey;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 业务底表关联字段
     */
    @TableField("relevancefieldname")
    private String relevancefieldname;

    /**
     * 创建时间
     */
    @TableField("createon")
    private LocalDateTime createon;

    /**
     * 创建人
     */
    @TableField("createby")
    private String createby;

    /**
     * 更新时间
     */
    @TableField("updateon")
    private LocalDateTime updateon;

    /**
     * 更新人
     */
    @TableField("updateby")
    private String updateby;


}
