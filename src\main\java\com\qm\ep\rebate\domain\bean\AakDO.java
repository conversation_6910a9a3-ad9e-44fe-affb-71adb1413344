package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("AAK")
@Schema(description = "车系销量表对象")
public class AakDO implements Serializable {
    

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "经销商代码")
    @TableField("DEALER_CODE")
    private String dealerCode;

    @Schema(description = "车系代码（H5，HS5等等）")
    @TableField("SERIES")
    private String series;

    @Schema(description = "零售日期")
    @TableField("SALE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date saleDate;

    @Schema(description = "零售数量")
    @TableField("POSITIVE_AAK")
    private Integer positiveAak;

    @Schema(description = "退零售数量")
    @TableField("NEGATIVE_AAK")
    private Integer negativeAak;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;
}
