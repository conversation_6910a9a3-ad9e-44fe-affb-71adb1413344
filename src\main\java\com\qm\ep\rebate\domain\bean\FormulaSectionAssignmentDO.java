package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 计算公式区间赋值表主表
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-计算公式区间赋值表主表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value ="formula_section_assignment")
public class FormulaSectionAssignmentDO implements Serializable {

    /**
     *主键id
     */
    @Schema(description = "数据-主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 计算公式主表id
     */
    @Schema(description = "数据-计算公式主表id")
    @TableField(value = "forid")
    private String forid;

    /**
     * 输出类型
     */
    @Schema(description = "数据-输出类型")
    @TableField(value = "resulttype")
    private String resulttype;

    /**
     * 聚合函数
     */
    @Schema(description = "数据-聚合函数：空串-无，greatest-最大值，least-最小值")
    @TableField(value = "aggregatefunction")
    private String aggregatefunction;

    /**
     * 输出结果
     */
    @Schema(description = "数据-输出结果")
    @TableField(value = "resultvalue")
    private String resultvalue;

    @Schema(description = "数据-创建时间")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-创建人")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-修改时间")
    @TableField(value = "updateon", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-修改人")
    @TableField(value = "updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-筛选条件")
    @TableField(exist = false)
    private List<FormulaSectionConditionsDO> conditions;

    /**
     * 备注
     */
    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;
}
