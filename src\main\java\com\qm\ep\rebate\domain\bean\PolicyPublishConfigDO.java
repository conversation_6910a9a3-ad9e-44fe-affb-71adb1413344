package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 *  
 * 发布配置信息
 *
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy_publish_config")
@Schema(description = "数据:发布配置信息，主要记录经销商")
public class PolicyPublishConfigDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-主表ID")
    @TableField("POLICY_ID")
    private String policyId;

    @Schema(description = "数据-经销商代码")
    @TableField("DEALER_CODE")
    private String dealerCode;

    @Schema(description = "数据-是否可见 1是0否")
    @TableField("VISIBLE")
    private Integer visible;

    @Schema(description = "数据-允许试算 1是0否")
    @TableField("CALC")
    private Integer calc;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;
}
