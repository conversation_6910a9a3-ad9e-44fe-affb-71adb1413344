package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.BusinessDataDO;
import com.qm.ep.rebate.domain.bean.SaleArchivesDO;
import com.qm.ep.rebate.domain.dto.BusinessDataDTO;
import com.qm.ep.rebate.domain.dto.VdealerDTO;
import com.qm.ep.rebate.domain.dto.VproductDTO;
import com.qm.ep.rebate.service.BusinessDataService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/businessData")
@Tag(name = "业务数据", description = "[author: 10200571]")
public class BusinessDataController extends BaseController {

    @Autowired
    private BusinessDataService businessDataService;

    @Operation(summary = "获取业务数据", description = "[author: 10200571]")
    @PostMapping("/getDataList")
    public JsonResultVo<QmPage<BusinessDataDO>> getDataSource(@RequestBody BusinessDataDTO businessDataDTO) {
        JsonResultVo<QmPage<BusinessDataDO>> result = new JsonResultVo<>();
        result.setData(businessDataService.queryTable(businessDataDTO));
        return result;
    }
    /**
     * 经销商档案查询
     */
    @Operation(summary = "对应区域经销商表格查询", description = "[author: 10200571]")
    @PostMapping("/dealerTable")
    public JsonResultVo<QmPage<BusinessDataDO>> dealerTable(@RequestBody VdealerDTO tempDTO) {
        JsonResultVo<QmPage<BusinessDataDO>> ret = new JsonResultVo<>();
        ret.setData(businessDataService.dealTable(tempDTO));
        return ret;
    }

    /**
     * 对应产品表格查询
     *
     * @param tempDTO
     * @return
     */
    @Operation(summary = "对应产品表格查询", description = "[author: 10200571]")
    @PostMapping("/productTable")
    public JsonResultVo<QmPage<BusinessDataDO>> productTable(@RequestBody VproductDTO tempDTO) {
        JsonResultVo<QmPage<BusinessDataDO>> ret = new JsonResultVo<>();
        ret.setData(businessDataService.productTable(tempDTO));
        return ret;
    }

    @Operation(summary = "产品搜索帮助", description = "[author: 10200571]")
    @PostMapping("/getSearchHelpData")
    public JsonResultVo<Map<String,List<Map<String,Object>>>> getSearchHelpData(){
        JsonResultVo<Map<String,List<Map<String,Object>>>> ret=new JsonResultVo();
        ret.setData(businessDataService.getSearchHelpData());
        return ret;
    }
    @Operation(summary = "经销商维护车辆档案下拉选帮助", description = "[author: 10200571]")
    @PostMapping("/getSelectSearch")
    public JsonResultVo<Map<String,List<Map<String,Object>>>> getSelectSearch(@RequestBody SaleArchivesDO saleArchivesDO){
        JsonResultVo<Map<String,List<Map<String,Object>>>> ret=new JsonResultVo();
        ret.setData(businessDataService.getSelectSearch(saleArchivesDO));
        return ret;
    }

    @Operation(summary = "根据年月删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteByMap")
    public JsonResultVo<Integer> deleteByMap(@RequestParam("tableName") String tablename ,@RequestBody String yesrs){
        JsonResultVo<Integer> ret=new JsonResultVo();
        ret.setData(businessDataService.deleteBySql(tablename,yesrs));
        return ret;
    }

    @Operation(summary = "通过大区获取经销商", description = "[author: 10200571]")
    @GetMapping("/getDealerArea")
    public JsonResultVo<List<String>> getDealerArea() {
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        ret.setData(businessDataService.getDealerArea());
        return ret;
    }

    @Operation(summary = "通过省份获取经销商", description = "[author: 10200571]")
    @PostMapping("/getDealerProvince")
    public JsonResultVo<List<String>> getDealerProvince(@RequestBody VdealerDTO tempDTO) {
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        ret.setData(businessDataService.getDealerProvince(tempDTO));
        return ret;
    }

    @Operation(summary = "通过城市获取经销商", description = "[author: 10200571]")
    @PostMapping("/getDealerCity")
    public JsonResultVo<List<String>> getDealerCity(@RequestBody VdealerDTO tempDTO) {
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        ret.setData(businessDataService.getDealerCity(tempDTO));
        return ret;
    }


}
