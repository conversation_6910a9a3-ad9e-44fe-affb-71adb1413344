package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.RebateExtractApplyPO;
import com.qm.ep.rebate.domain.bean.RebateExtractFilePO;
import com.qm.ep.rebate.domain.request.RebateExtractApplyQueryRequest;
import com.qm.ep.rebate.domain.request.RebateExtractApplySaveRequest;
import com.qm.ep.rebate.domain.request.RebateExtractFileSaveRequest;
import com.qm.ep.rebate.domain.response.RebateExtractApplyHandleListResponse;
import com.qm.ep.rebate.domain.response.RebateExtractApplyResponse;
import com.qm.ep.rebate.service.RebateExtractApplyConfirmService;
import com.qm.ep.rebate.service.RebateExtractApplyService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/rebateExtractConfirm")

@Tag(name = "返利折让Controller")
@Slf4j
public class RebateExtractApplyConfirmController {

    @Resource
    private RebateExtractApplyConfirmService confirmService;

    @Resource
    private RebateExtractApplyService rebateExtractApplyService;

    @Operation(summary = "确认返利折让申请-列表", description = "[author: 10200571]")
    @PostMapping("/listConfirmRebateExtractApply")
    public JsonResultVo<QmPage<RebateExtractApplyResponse>> listConfirmRebateExtractApply(@Valid @RequestBody RebateExtractApplyQueryRequest request) {
        JsonResultVo<QmPage<RebateExtractApplyResponse>> result = new JsonResultVo<>();
        result.setData(confirmService.listRebateExtractApply(request));
        return result;
    }

    @Operation(summary = "确认返利折让申请-详情", description = "[author: 10200571]")
    @GetMapping("/detailConfirmApply")
    public JsonResultVo<RebateExtractApplyHandleListResponse> detailConfirmApply(@RequestParam("id") Long id) {
        JsonResultVo<RebateExtractApplyHandleListResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.detailHandle(id,"F02"));
        return result;
    }

    @Operation(summary = "通过", description = "[author: 10200571]")
    @PostMapping("/apopt")
    public JsonResultVo<RebateExtractFilePO> apopt(@RequestBody RebateExtractFileSaveRequest request) {
        JsonResultVo<RebateExtractFilePO> result = new JsonResultVo<>();
        result.setData(confirmService.apopt(request));
        return result;
    }

    @Operation(summary = "不通过", description = "[author: 10200571]")
    @PostMapping("/notApprove")
    public JsonResultVo<RebateExtractFilePO> notApprove(@RequestBody RebateExtractFileSaveRequest request) {
        JsonResultVo<RebateExtractFilePO> result = new JsonResultVo<>();
        result.setData(confirmService.notApprove(request));
        return result;
    }

    @Operation(summary = "确认", description = "[author: 10200571]")
    @PostMapping("/confirm")
    public JsonResultVo<RebateExtractApplyPO> confirm(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<RebateExtractApplyPO> result = new JsonResultVo<>();
        result.setData(confirmService.confirm(request));
        return result;
    }

    @Operation(summary = "撤销", description = "[author: 10200571]")
    @PostMapping("/withdraw")
    public JsonResultVo<RebateExtractApplyPO> withdraw(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<RebateExtractApplyPO> result = new JsonResultVo<>();
        result.setData(confirmService.withdraw(request));
        return result;
    }

    @Operation(summary = "终止", description = "[author: 10200571]")
    @PostMapping("/terminate")
    public JsonResultVo<RebateExtractApplyPO> terminate(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<RebateExtractApplyPO> result = new JsonResultVo<>();
        result.setData(confirmService.terminate(request));
        return result;
    }

    @Operation(summary = "取消确认", description = "[author: 10200571]")
    @PostMapping("/cancelConfirm")
    public JsonResultVo<RebateExtractApplyPO> cancelConfirm(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<RebateExtractApplyPO> result = new JsonResultVo<>();
        result.setData(confirmService.cancelConfirm(request));
        return result;
    }

    @Operation(summary = "提交财务", description = "[author: 10200571]")
    @PostMapping("/submitFinace")
    public JsonResultVo<RebateExtractApplyPO> submitFinace(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<RebateExtractApplyPO> result = new JsonResultVo<>();
        result.setData(confirmService.submitFinace(request));
        return result;
    }

    @Operation(summary = "取消提交财务", description = "[author: 10200571]")
    @PostMapping("/cancelFinace")
    public JsonResultVo<RebateExtractApplyPO> cancelFinace(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<RebateExtractApplyPO> result = new JsonResultVo<>();
        result.setData(confirmService.cancelFinace(request));
        return result;
    }
}
