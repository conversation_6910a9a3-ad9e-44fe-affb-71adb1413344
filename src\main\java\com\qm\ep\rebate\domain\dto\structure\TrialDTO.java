package com.qm.ep.rebate.domain.dto.structure;

import com.qm.ep.rebate.domain.bean.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-试用 DTO")
@Data
public class TrialDTO {

    @Schema(description = "数据-主要")
    private RedFlagTrialMainDO main;
    @Schema(description = "数据-加入")
    private List<RedFlagTrialJoinDO> joins;
    @Schema(description = "数据-条件")
    private List<RedFlagTrialConditionsDO> conditions;
    @Schema(description = "数据-详")
    private List<RedFlagTrialDetailDO> details;

}