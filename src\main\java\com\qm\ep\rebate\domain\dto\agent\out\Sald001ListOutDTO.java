package com.qm.ep.rebate.domain.dto.agent.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 */
@Data
@Schema(description = "数据:结算单对象")
public class Sald001ListOutDTO implements Serializable {
    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "数据-主键")
    private String id;
    /**
     * 结算单号(经销商代码+YYYYMMDD+3位流水)
     */
    @Schema(description = "数据-结算单号")
    private String settleNo;
    /**
     * 代理商代码
     */
    @Schema(description = "数据-代理商代码")
    private String vdealerCode;
    /**
     * 代理商名称
     */
    @Schema(description = "数据-代理商名称")
    private String vdealerName;
    /**
     * 报账标识，0初始，1报账占用，2已报销
     */
    @Schema(description = "数据-报账标识，0初始，1报账占用，2已报销")
    private String sald001Flag;
    /**
     * 车系名称
     */
    @Schema(description = "数据-车系名称")
    private String series;
    /**
     * 结算单状态(0-录入，1-提交，2-已报账)
     */
    @Schema(description = "数据-结算单状态")
    private Integer settleStatus;

    @Schema(description = "数据-结算单提交日期")
    private Date settleSubmitDate;
    /**
     * 税率
     */
    @Schema(description = "数据-税率")
    private String taxRate;

    @Schema(description = "数据-创建者")
    private String createBy;

    @Schema(description = "数据-创建日期")
    private Date createOn;

    @Schema(description = "数据-更新者")
    private String updateBy;

    @Schema(description = "数据-更新日期")
    private Date updateOn;


    @Schema(description = "数据-时间戳")
    private Date dtstamp;
    /**
     * 扩展字段
     */
    @Schema(description = "数据-扩展字段")
    private String ext;

    @Schema(description = "数据-结算份数")
    private Long settleCount;

    @Schema(description = "数据-结算金额")
    private BigDecimal settleAmount;

}