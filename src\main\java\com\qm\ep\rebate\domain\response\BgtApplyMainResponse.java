package com.qm.ep.rebate.domain.response;

import com.qm.ep.rebate.domain.bean.BgtApplyDetailDemandDO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  
 * 预算申请单
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Data
@Schema(description = "数据:预算申请单")
public class BgtApplyMainResponse implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-返利项目对应车系列表")
    private List<BgtApplyDetailDemandDO> items;

    @Schema(description = "数据-返利项目对应目标数值")
    private String aimValue;

    @Schema(description = "数据-返利项目对应目标单位")
    private String unit;

}
