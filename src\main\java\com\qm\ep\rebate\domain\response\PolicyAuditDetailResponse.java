package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "审计结果详情")
@Data
public class PolicyAuditDetailResponse {

    @Schema(description = "参与测算政策")
    private List<PolicyDetail> policyDetails;

    @Schema(description = "入账明细")
    private List<Detail> details;

    @Schema(description = "入账明细")
    @Data
    public static class Detail {
        @Schema(description = "主键")
        private String id;
        @Schema(description = "因子名称")
        private String factorName;
        @Schema(description = "经销商代码")
        private String dealerCode;
        @Schema(description = "经销商名称")
        private String dealerName;
        @Schema(description = "系列")
        private String series;
        @Schema(description = "数据VIN")
        private String vin;
        @Schema(description = "返利金额")
        private String rebateAmount;
        @Schema(description = "政策名称")
        private String policyName;
    }

    @Schema(description = "参与测算政策")
    @Data
    public static class PolicyDetail {
        @Schema(description = "主键")
        private String id;
        @Schema(description = "政策编码")
        private String policyCode;
        @Schema(description = "政策名称")
        private String policyName;
        @Schema(description = "政策部门")
        private String department;
        @Schema(description = "政策创建人")
        private String creator;
    }

}
