package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 政策评价表
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Getter
@Setter
@TableName("policy_evaluate")
@Schema(description = "政策评价表")
public class PolicyEvaluatePO implements Serializable {

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "政策编码")
    @TableField("policyCode")
    private String policyCode;

    @Schema(description = "政策名称")
    @TableField("policyName")
    private String policyName;

    @Schema(description = "政策文件发布时间")
    @TableField("filePublishTime")
    private Date filePublishTime;

    @Schema(description = "政策执行开始日期")
    @TableField("dbegin")
    private LocalDateTime dbegin;

    @Schema(description = "政策执行结束日期")
    @TableField("dend")
    private LocalDateTime dend;

    @Schema(description = "创建人所在部门")
    @TableField("deptName")
    private String deptName;

    @Schema(description = "政策创建人")
    @TableField("creator")
    private String creator;

    @Schema(description = "政策产品（系列）")
    @TableField("product")
    private String product;

    @Schema(description = "发布逾期（是或否）")
    @TableField("publishDelay")
    private String publishDelay;

    @Schema(description = "发布周期（天）")
    @TableField("publishCycle")
    private Integer publishCycle;

    @Schema(description = "结算逾期（是或否）")
    @TableField("settleDelay")
    private String settleDelay;

    @Schema(description = "结算周期（天）")
    @TableField("settleCycle")
    private Integer settleCycle;

    @Schema(description = "政策目标")
    @TableField("aim")
    private String aim;

    @Schema(description = "政策目标值")
    @TableField("aimValue")
    private String aimValue;

    @Schema(description = "政策达成值")
    @TableField("achiveValue")
    private String achiveValue;

    @Schema(description = "目标达成率")
    @TableField("aimAchiveRate")
    private String aimAchiveRate;

    @Schema(description = "预算金额（元）")
    @TableField("bgtAmount")
    private BigDecimal bgtAmount;

    @Schema(description = "实际结算金额（元）")
    @TableField("settleAmount")
    private BigDecimal settleAmount;

    @Schema(description = "结算差额（元）")
    @TableField("settleDiff")
    private BigDecimal settleDiff;

    @Schema(description = "政策覆盖经销商数（基础页设置）")
    @TableField("planDealerCount")
    private Integer planDealerCount;

    @Schema(description = "政策获取经销商数(实际结算)")
    @TableField("actualDealerCount")
    private Integer actualDealerCount;

    @Schema(description = "政策获取率")
    @TableField("acquireRate")
    private String acquireRate;

    @Schema(description = "目标达成评价 0-未达成 红灯 1-达成 绿灯")
    @TableField("aimAchiveEvaluate")
    private String aimAchiveEvaluate;

    @Schema(description = "预算使用评价  0-未达成 红灯 1-达成 绿灯")
    @TableField("bgtUseEvaluate")
    private String bgtUseEvaluate;

    @Schema(description = "关键指标综合评价  1-深绿色 2-浅绿色 3-黄色 4-红灯")
    @TableField("criticalAimEvaluate")
    private String criticalAimEvaluate;

    @Schema(description = "优化建议")
    @TableField("optAdvice")
    private String optAdvice;

    @Schema(description = "业务确认结果")
    @TableField("confirmResult")
    private String confirmResult;

    @Schema(description = "调整方向措施")
    @TableField("adjustWay")
    private String adjustWay;

    @Schema(description = "创建者")
    @TableField("CREATEBY")
    private String createby;

    @Schema(description = "创建日期")
    @TableField("CREATEON")
    private LocalDateTime createon;

    @Schema(description = "数据-更新者")
    private String updateby;

    @Schema(description = "数据-更新时间")
    @TableField(value = "UPDATEON")
    private Date updateon;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "政策评价星级")
    @TableField("starValue")
    private Integer starValue;

    @Schema(description = "发布得星")
    @TableField("publishStarValue")
    private Integer publishStarValue;

    @Schema(description = "结算得星")
    @TableField("settleStarValue")
    private Integer settleStarValue;

    @Schema(description = "预算得星")
    @TableField("bgtStarValue")
    private Integer bgtStarValue;

    @Schema(description = "目标达成得星")
    @TableField("aimStarValue")
    private Integer aimStarValue;

    @Schema(description = "政策获取率得星")
    @TableField("acquireStarValue")
    private Integer acquireStarValue;

    @Schema(description = "政策实际发布时间")
    @TableField("publishActualTime")
    private Date publishActualTime;

    @Schema(description = "政策计划发布时间")
    @TableField("publishPlanTime")
    private Date publishPlanTime;

    @Schema(description = "政策实际结算时间")
    @TableField("settleActualTime")
    private Date settleActualTime;

    @Schema(description = "政策计划结算时间")
    @TableField("settlePlanTime")
    private Date settlePlanTime;

    @Schema(description = "任务实例Code")
    @TableField("taskInstanceCode")
    private String taskInstanceCode;


    @Schema(description = "任务是否完成 0-未完成 1-完成")
    @TableField("taskIsFinish")
    private Integer taskIsFinish;


    @Schema(description = "经办人域账号")
    @TableField("agentCode")
    private String agentCode;

    @Schema(description = "经办人域姓名")
    @TableField("agentName")
    private String agentName;
}
