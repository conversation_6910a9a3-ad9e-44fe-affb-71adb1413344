package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.qm.ep.rebate.enumerate.JoinTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REDFLAGTRIALJOIN")
@Schema(description = "数据:红旗伙伴试算关联表对象")
public class RedFlagTrialJoinDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策主键")
    @TableField("POLICY_ID")
    private String policyId;

    @Schema(description = "数据-红旗伙伴试算主键")
    @TableField("FACTOR_ID")
    private String factorId;

    @Schema(description = "数据-关联底表名称")
    @TableField("TABLE_NAME")
    private String tableName;

    @Schema(description = "数据-关联关系")
    @TableField("JOIN_TYPE")
    private JoinTypeEnum joinType;

    @Schema(description = "数据-关联条件")
    @TableField(exist=false)
    private List<RedFlagTrialJoinOnDO> joinOns;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;

}
