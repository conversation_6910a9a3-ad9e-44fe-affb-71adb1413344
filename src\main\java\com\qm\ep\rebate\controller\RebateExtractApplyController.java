package com.qm.ep.rebate.controller;


import com.qm.ep.rebate.common.constant.LockConstant;
import com.qm.ep.rebate.domain.request.*;
import com.qm.ep.rebate.domain.response.RebateExtractApplyHandleListResponse;
import com.qm.ep.rebate.domain.response.RebateExtractApplyHandleResultResponse;
import com.qm.ep.rebate.domain.response.RebateExtractApplyResponse;
import com.qm.ep.rebate.domain.response.RebateExtractApplySaveInitInfoResponse;
import com.qm.ep.rebate.remote.request.RebateExtractFeignRequest;
import com.qm.ep.rebate.service.RebateExtractApplyService;
import com.qm.ep.rebate.service.RebateExtractParamService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 *  
 * 返利折让申请单与处理单合并表 前端控制器
 *  
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@RestController
@RequestMapping("/rebateExtract")

@Tag(name = "返利折让Controller")
@Slf4j
public class RebateExtractApplyController {
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RebateExtractParamService rebateExtractParamService;
    @Resource
    private RebateExtractApplyService rebateExtractApplyService;

    @Operation(summary = "维护返利折让申请-新建-查询信息-DMS", description = "[author: 10200571]")
    @PostMapping("/getApplyInitInfo")
    public JsonResultVo<RebateExtractApplySaveInitInfoResponse> getApplyInitInfo(@RequestBody RebateExtractFeignRequest request) {
        JsonResultVo<RebateExtractApplySaveInitInfoResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.getApplyInitInfo(request.getDealerCode()));
        return result;
    }

    @Operation(summary = "维护返利折让申请-新建-DMS", description = "[author: 10200571]")
    @PostMapping("/saveApply")
    public JsonResultVo<RebateExtractApplyResponse> saveApply(@Valid @RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<RebateExtractApplyResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.saveApply(request));
        return result;
    }

    @Operation(summary = "维护返利折让申请-列表-DMS", description = "[author: 10200571]")
    @PostMapping("/listRebateExtractApply")
    public JsonResultVo<QmPage<RebateExtractApplyResponse>> listRebateExtractApply(@Valid @RequestBody RebateExtractApplyQueryRequest request) {
        JsonResultVo<QmPage<RebateExtractApplyResponse>> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.listRebateExtractApply(request));
        return result;
    }

    @Operation(summary = "维护返利折让申请-详情-DMS", description = "[author: 10200571]")
    @GetMapping("/detailApply")
    public JsonResultVo<RebateExtractApplyResponse> detailApply(@RequestParam("id") Long id) {
        JsonResultVo<RebateExtractApplyResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.detailApply(id));
        return result;
    }

    @Operation(summary = "维护返利折让申请-删除-DMS", description = "[author: 10200571]")
    @GetMapping("/deleteApply")
    public JsonResultVo<RebateExtractApplyResponse> deleteApply(@RequestParam("id") Long id) {
        JsonResultVo<RebateExtractApplyResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.deleteApply(id));
        return result;
    }

    @Operation(summary = "审核返利折让申请-列表-DMS", description = "[author: 10200571]")
    @PostMapping("/listRebateExtractApprove")
    public JsonResultVo<QmPage<RebateExtractApplyResponse>> listRebateExtractApprove(@Valid @RequestBody RebateExtractApplyQueryRequest request) {
        JsonResultVo<QmPage<RebateExtractApplyResponse>> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.listRebateExtractApprove(request));
        return result;
    }


    @Operation(summary = "审核返利折让申请-详情-DMS", description = "[author: 10200571]")
    @GetMapping("/getRebateExtractApproveDetail")
    public JsonResultVo<RebateExtractApplyResponse> getRebateExtractApproveDetail(@RequestParam("id") Long id) {
        JsonResultVo<RebateExtractApplyResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.getRebateExtractApproveDetail(id));
        return result;
    }

    @Operation(summary = "审核返利折让申请-审核与驳回-DMS", description = "[author: 10200571]")
    @PostMapping("/approve")
    JsonResultVo<RebateExtractApplyResponse> approve(@Valid @RequestBody RebateExtractApplyApproveRequest request) {
        JsonResultVo<RebateExtractApplyResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.approve(request));
        return result;
    }



    @Operation(summary = "处理返利折让申请-列表-工作台", description = "[author: 10200571]")
    @PostMapping("/listHandleApply")
    public JsonResultVo<QmPage<RebateExtractApplyResponse>> listHandleApply(@RequestBody RebateExtractApplyQueryRequest request) {
        JsonResultVo<QmPage<RebateExtractApplyResponse>> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.listHandleApply(request));
        return result;
    }


    @Operation(summary = "处理返利折让申请-开始测算-工作台", description = "[author: 10200571]")
    @PostMapping("/startCalculate")
    public JsonResultVo<List<RebateExtractApplyHandleResultResponse>> startCalculate(@RequestBody RebateExtractApplyHandleRequest request) {
        JsonResultVo<List<RebateExtractApplyHandleResultResponse>> result = new JsonResultVo<>();
        RLock lock = redissonClient.getLock(LockConstant.REBATE_EXTRACT_START_CAL_LOCK + request.getId());
        boolean isLock = false;
        try {
            isLock = lock.tryLock(500, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            throw new QmException("正在测算中中，请稍候重试");
        }
        if (isLock) {
            try {
                // 业务代码
                result.setData(rebateExtractApplyService.startCalculate(request));
            }finally {
                // 释放锁
                lock.unlock();
            }
        } else {
            throw new QmException("正在测算中中，请稍候重试");
        }


        return result;
    }

    @Operation(summary = "处理返利折让申请-上传文件-工作台", description = "[author: 10200571]")
    @PostMapping("/startUpload")
    public JsonResultVo<List<RebateExtractApplyHandleResultResponse>> startUpload(
            RebateExtractApplyHandleRequest request,
            @RequestPart("file") MultipartFile file) {
        JsonResultVo<List<RebateExtractApplyHandleResultResponse>> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.startUpload(request, file));
        return result;
    }

    @Operation(summary = "处理返利折让申请-下载文件-DMS", description = "[author: 10200571]")
    @GetMapping("/downloadFile")
    public JsonResultVo<String> downloadFile(@RequestParam("fileId") Integer fileId) {
        JsonResultVo<String> result = new JsonResultVo<>();
        String url = rebateExtractApplyService.downloadFile(fileId);
        result.setData(url);
        return result;
    }

    @Operation(summary = "处理返利折让申请-下载文件-工作台", description = "[author: 10200571]")
    @GetMapping("/downloadFileWorkbench")
    public void downloadFileWorkbench(@RequestParam("fileId") Integer fileId, HttpServletResponse response) throws UnsupportedEncodingException {
       rebateExtractApplyService.downloadFileWorkbench(fileId,response);
    }

    @Operation(summary = "处理返利折让申请-删除文件-工作台&DMS", description = "[author: 10200571]")
    @PostMapping("/deleteFile")
    public JsonResultVo<String> deleteFile(@RequestBody RebateExtractApplyHandleRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.deleteFile(request.getFileId()));
        return result;
    }


    @Operation(summary = "处理返利折让申请-处理完成-工作台", description = "[author: 10200571]")
    @PostMapping("/startHandle")
    public JsonResultVo<List<RebateExtractApplyHandleResultResponse>> startHandle(@RequestBody RebateExtractApplyHandleRequest request) {
        JsonResultVo<List<RebateExtractApplyHandleResultResponse>> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.startHandle(request));
        return result;
    }



    @Operation(summary = "返利折让申请-详情-工作台", description = "[author: 10200571]")
    @GetMapping("/detailHandle")
    public JsonResultVo<RebateExtractApplyHandleListResponse> detailHandle(@RequestParam("id") Long id) {
        JsonResultVo<RebateExtractApplyHandleListResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.detailHandle(id,"F01"));
        return result;
    }

    @Operation(summary = "处理返利折让申请-详情-终止-工作台", description = "[author: 10200571]")
    @PostMapping("/endHandle")
    public JsonResultVo<String> endHandle(@RequestBody RebateExtractApplyHandleRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.endHandle(request.getId(), request.getEndReason()));
        return result;
    }

    @Operation(summary = "提报返利折让申请材料-上传文件-DMS", description = "[author: 10200571]")
    @PostMapping("/uploadApplyFile")
    JsonResultVo<String> uploadApplyFile(RebateExtractApplyHandleRequest request,
                                         @RequestPart("file") MultipartFile file) {
        JsonResultVo<String> result = new JsonResultVo<>();
        Long id = request.getIds().get(0);
        rebateExtractApplyService.uploadApplyFile(id, file,request.getFileType());
        result.setData("上传成功");
        return result;
    }

    @Operation(summary = "提报返利折让申请材料-列表-DMS", description = "[author: 10200571]")
    @PostMapping("/listSubmitApply")
    public JsonResultVo<QmPage<RebateExtractApplyResponse>> listSubmitApply(@RequestBody RebateExtractApplyQueryRequest request) {
        JsonResultVo<QmPage<RebateExtractApplyResponse>> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.listSubmitApply(request));
        return result;
    }

    @Operation(summary = "提报返利折让申请材料-详情-DMS", description = "[author: 10200571]")
    @GetMapping("/detailSubmitApply")
    public JsonResultVo<RebateExtractApplyResponse> detailSubmitApply(@RequestParam("id") Long id) {
        JsonResultVo<RebateExtractApplyResponse> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.detailSubmitApply(id));
        return result;
    }

    @Operation(summary = "提报返利折让申请材料-保存-DMS", description = "[author: 10200571]")
    @PostMapping("/saveSubmitApply")
    public JsonResultVo<String> saveSubmitApply(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.saveSubmitApply(request.getId(),request.getInvoiceCodes()));
        return result;
    }

    @Operation(summary = "提报返利折让申请材料-提报-DMS", description = "[author: 10200571]")
    @PostMapping("/submitApply")
    public JsonResultVo<String> submitApply(@RequestBody RebateExtractApplySaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        RLock lock = redissonClient.getLock(LockConstant.REBATE_EXTRACT_SUBMIT_APPLY_LOCK + request.getId());
        boolean isLock = false;
        try {
            isLock = lock.tryLock(500, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            throw new QmException("正在提报中，请稍候重试");
        }
        if (isLock) {
            try {
                // 业务代码
                rebateExtractApplyService.submitApply(request);
                result.setData("提报成功");
            }finally {
                // 释放锁
                lock.unlock();
            }
        } else {
            throw new QmException("正在提报中，请稍候重试");
        }
        return result;
    }


    @Operation(summary = "确认返利折让申请-保存-工作台", description = "[author: 10200571]")
    @PostMapping("/saveConfirm")
    public JsonResultVo<String> saveConfirm(@RequestBody RebateExtractApplyConfirmSaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(rebateExtractApplyService.saveConfirm(request));
        return result;
    }

    // @Operation(summary = "设置返利折让参数-修改-工作台", description = "[author: 10200571]")
    // @PostMapping("/updateExtractParam")
    // public JsonResultVo<String> updateExtractParam(@RequestBody List<RebateExtractParamPO> request) {
    //     JsonResultVo<String> result = new JsonResultVo<>();
    //     result.setData(rebateExtractParamService.updateExtractParam(request));
    //     return result;
    // }

}
