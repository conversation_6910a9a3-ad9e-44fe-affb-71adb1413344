package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 *
 * 计算方案主表
 *
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("planmain")
@Schema(description = "计算方案主表")
public class PolicyCalQueryDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策ID")
    @TableField("policyid")
    private String policyid;

    @Schema(description = "数据-方案名称")
    @TableField("planname")
    private String planname;

    @Schema(description = "数据-方案描述")
    @TableField("plandesc")
    private String plandesc;

    @Schema(description = "数据-选择的计算对象底部")
    @TableField("calcobjects")
    private String calcobjects;

    @Schema(description = "数据-兑付对象底表")
    @TableField("payobjects")
    private String payobjects;

    @Schema(description = "数据-额度对象底表")
    @TableField("limitobjects")
    private String limitobjects;

    @Schema(description = "数据-兑付公式")
    @TableField("formulacontent")
    private String formulacontent;

    @Schema(description = "数据-创建")
    @TableField("createon")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createon;

    @Schema(description = "数据-创建者")
    @TableField("createby")
    private String createby;

    @Schema(description = "数据-更新")
    @TableField("updateon")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateon;

    @Schema(description = "数据-更新作者")
    @TableField("updateby")
    private String updateby;

    @Schema(description = "数据-数据DTSTAMP")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    private Timestamp dtstamp;


}
