package com.qm.ep.rebate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.common.constant.SqlConstants;
import com.qm.ep.rebate.common.constant.StringConstants;
import com.qm.ep.rebate.domain.bean.ParamBusinessDataDO;
import com.qm.ep.rebate.domain.bean.ParamSetDO;
import com.qm.ep.rebate.domain.dto.ParamSetDTO;
import com.qm.ep.rebate.domain.dto.ValidationResultDTO;
import com.qm.ep.rebate.domain.vo.CalcObjectVO;
import com.qm.ep.rebate.domain.vo.ParamSetVO;
import com.qm.ep.rebate.enumerate.ParamTypeEnum;
import com.qm.ep.rebate.service.ParamBusinessDataService;
import com.qm.ep.rebate.service.ParamSetService;
import com.qm.ep.rebate.service.SysPersonOrgService;
import com.qm.ep.rebate.service.SystemConfigService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/paramSet")
@Tag(name = "自定义参数")
public class ParamSetController extends BaseController {

    @Resource
    private ParamSetService paramSetService;
    @Resource
    private ParamBusinessDataService paramBusinessDataService;
    @Resource
    private SystemConfigService systemConfigService;
    @Resource
    private SysPersonOrgService sysPersonOrgService;

    @Operation(summary = "查询自定义参数列表", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ParamSetDO>> table(@RequestBody ParamSetDTO paramSetDTO){
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);

        JsonResultVo<QmPage<ParamSetDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<ParamSetDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ParamSetDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.like(BootAppUtil.isnotNullOrEmpty(paramSetDTO.getParamName()), ParamSetDO::getParamName, paramSetDTO.getParamName());
        lambdaWrapper.in(CollUtil.isNotEmpty(paramSetDTO.getParamTypes()), ParamSetDO::getParamType, paramSetDTO.getParamTypes());

        if(BootAppUtil.isNullOrEmpty(paramSetDTO.getPolicyId())) {
            // 全局自定义参数
            lambdaWrapper.and(wrapper->wrapper.isNull(ParamSetDO::getPolicyId).or().eq(ParamSetDO::getPolicyId, ""));
            if("bx".equals(company)){
                LoginKeyDO userInfo = getUserInfo();
                List<String> personIds = sysPersonOrgService.queryPersonIdAtRelatedLevel(userInfo.getOperatorId());
                if(CollUtil.isNotEmpty(personIds)){
                    lambdaWrapper.in(ParamSetDO::getCreateBy, personIds);
                }else{
                    jsonResultVo.setData(new QmPage<>());
                    return jsonResultVo;
                }
            }
        } else {
            // 政策自定义参数
            lambdaWrapper.eq(ParamSetDO::getPolicyId, paramSetDTO.getPolicyId());
        }

        QmPage<ParamSetDO> data = paramSetService.table(queryWrapper, paramSetDTO);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<String> save(@RequestBody ParamSetDTO paramSetDTO){
        JsonResultVo<String> ret = new JsonResultVo<>();

        // 检查参数名称是否重复
        ValidationResultDTO validationResult = paramSetService.validateName(paramSetDTO);
        if(!validationResult.getOk()) {
            ret.setMsgErr(validationResult.getMsg());
            return ret;
        }

        ParamSetDO paramSetDO = new ParamSetDO();
        if(BootAppUtil.isnotNullOrEmpty(paramSetDTO.getId())) {
            paramSetDO = paramSetService.getById(paramSetDTO.getId());
        }

        BeanUtil.copyProperties(paramSetDTO, paramSetDO, "paramTypes", "selectedObj", "selectedField");

        if(ParamTypeEnum.LIST.equals(paramSetDO.getParamType())){
            paramSetDO.setParamValue(paramSetDTO.getSelectedObj() + StringConstants.DOT + paramSetDTO.getSelectedField());
        }

        paramSetService.saveOrUpdate(paramSetDO);
        ret.setData(paramSetDO.getId());
        return ret;
    }


    @Operation(summary = "获取详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<ParamSetVO> getDetail(@RequestBody ParamSetDTO paramSetDTO){
        JsonResultVo<ParamSetVO> ret = new JsonResultVo<>();
        ParamSetDO paramSetDO = paramSetService.getById(paramSetDTO.getId());
        ParamSetVO paramSetVO = new ParamSetVO();
        BeanUtil.copyProperties(paramSetDO, paramSetVO);
        if(ParamTypeEnum.LIST.equals(paramSetDO.getParamType())){
            String paramValue = paramSetDO.getParamValue();
            String[] split = paramValue.split("\\.");
            paramSetVO.setSelectedObj(split[0]);
            paramSetVO.setSelectedField(split[1]);
        }
        ret.setData(paramSetVO);
        return ret;
    }

    @Operation(summary = "删除", description = "[author: 10200571]")
    @PostMapping("/deleteByIds")
    public JsonResultVo deleteByIds(@RequestBody List<String> ids){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        if(CollUtil.isEmpty(ids)) {
            ret.setMsgErr("删除失败！ID列表不能为空！");
        } else {
            List<String> canDeleteIds = new ArrayList<>();
            List<String> canDeleteNames = new ArrayList<>();
            List<String> canNotDeleteNames = new ArrayList<>();
            ids.forEach(id->{
                ParamSetDO paramSetDO = paramSetService.getById(id);
                List<CalcObjectVO> paramUsed = paramSetService.paramUsed(paramSetDO.getPolicyId(), paramSetDO.getParamName());
                if(CollUtil.isNotEmpty(paramUsed)) {
                    canNotDeleteNames.add(paramSetDO.getParamName());
                } else {
                    canDeleteIds.add(id);
                    canDeleteNames.add(paramSetDO.getParamName());
                }
            });
            if(CollUtil.isNotEmpty(canDeleteIds)) {
                paramSetService.removeByIds(canDeleteIds);

                LambdaQueryWrapper<ParamBusinessDataDO> wrapper = new QmQueryWrapper<ParamBusinessDataDO>().lambda();
                wrapper.in(ParamBusinessDataDO::getParamId, canDeleteIds);
                paramBusinessDataService.remove(wrapper);
            }
            if(CollUtil.isNotEmpty(canNotDeleteNames)) {
                ret.setMsgErr("参数："+CollUtil.join(canNotDeleteNames, "，")+" 已被使用，无法删除！");
            } else {
                ret.setMsg("删除成功！");
            }
        }

        return ret;
    }

    @Operation(summary = "获取可用的自定义参数", description = "[author: 10200571]")
    @PostMapping("/availableParams")
    public JsonResultVo<List<ParamSetDO>> getAvailableParams(@RequestBody ParamSetDTO paramSetDTO){
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);

        QmQueryWrapper<ParamSetDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ParamSetDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.like(BootAppUtil.isnotNullOrEmpty(paramSetDTO.getParamName()), ParamSetDO::getParamName, paramSetDTO.getParamName());
        lambdaWrapper.in(CollUtil.isNotEmpty(paramSetDTO.getParamTypes()), ParamSetDO::getParamType, paramSetDTO.getParamTypes());

        // and (policyid = '111' or ((policyid = '' or policyid is null) and createby in (1,2,3))

        lambdaWrapper.and(wrapper -> {
            wrapper.eq(ParamSetDO::getPolicyId, paramSetDTO.getPolicyId());
            if("bx".equals(company)){
                wrapper.or(wrapper1 -> {
                    LoginKeyDO userInfo = getUserInfo();
                    List<String> personIds = sysPersonOrgService.queryPersonIdAtRelatedLevel(userInfo.getOperatorId());
                    // 如果personIds为空，表示当前用户不可使用全局参数，故不再继续拼接条件
                    if(CollUtil.isNotEmpty(personIds)){
                        // 如果personIds不为空，表示当前用户可以查看personIds中人员创建的全局参数，故拼接条件
                        wrapper1.and(wrapper2->wrapper2.isNull(ParamSetDO::getPolicyId).or().eq(ParamSetDO::getPolicyId, "")).in(ParamSetDO::getCreateBy, personIds);
                    }
                });
            }else{
                wrapper.or().eq(ParamSetDO::getPolicyId, "").or().isNull(ParamSetDO::getPolicyId);
            }
        });
        lambdaWrapper.orderByAsc(ParamSetDO::getPolicyId, ParamSetDO::getParamType, ParamSetDO::getParamName);
        List<ParamSetDO> paramSets = paramSetService.list(lambdaWrapper);
        JsonResultVo<List<ParamSetDO>> ret = new JsonResultVo<>();
        ret.setData(paramSets);
        return ret;
    }

    @Operation(summary = "运行自定义函数", description = "[author: 10200571]")
    @PostMapping("/runParam")
    public JsonResultVo<String> runParam(@RequestBody ParamSetDTO paramSetDTO){
        JsonResultVo<String> ret = new JsonResultVo<>();
        String paramValue = paramSetDTO.getParamValue();

        Map<String, Object> sqlMap = new HashMap<>(1);
        sqlMap.put("sql", SqlConstants.SELECT + paramValue + SqlConstants.AS + "result");
        try {
            List<Map> result = paramSetService.getBySql(sqlMap);
            if(CollUtil.isNotEmpty(result)) {
                ret.setData(String.valueOf(result.get(0).get("result")));
            } else {
                ret.setMsgErr("函数值计算后没有得到输出结果！");
            }
        } catch (Exception e) {
            ret.setMsgErr("函数值无法计算！不符合SQL规范语法！");
        }
        return ret;
    }

    @Operation(summary = "参数使用情况", description = "[author: 10200571]")
    @PostMapping("/paramUsed")
    public JsonResultVo<List<CalcObjectVO>> detail(@RequestBody ParamSetDTO paramSetDTO){
        List<CalcObjectVO> calcObjectVOS = paramSetService.paramUsed(paramSetDTO.getPolicyId(), paramSetDTO.getParamName());
        JsonResultVo<List<CalcObjectVO>> ret = new JsonResultVo<>();
        ret.setData(calcObjectVOS);
        return ret;
    }
}
