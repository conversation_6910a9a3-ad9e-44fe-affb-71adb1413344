package com.qm.ep.rebate.domain.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 *  
 * 
 *
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "数据实体")
public class AuditAuthorizeSaveRequest implements Serializable {

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-部门")
    @NotBlank(message = "部门不能为空")
    private String department;


    @Schema(description = "数据-商务政策专员手机号")
    private String commissionerPhone;

    @Schema(description = "数据-总监手机号")
    private String directorPhone;

    @Schema(description = "数据-总监id")
    private String director;



    @Schema(description = "提交人员工id")
    private String commissioner;

    @Schema(description = "提交人域账号")
    @NotBlank(message = "提交人域账号不能为空")
    private String firstLoginAccount;

    @Schema(description = "提交人姓名")
    @NotBlank(message = "提交人姓名不能为空")
    private String firstAuditName;


    @Schema(description = "总监域账号")
    @NotBlank(message = "总监域账号不能为空")
    private String secondLoginAccount;

    @Schema(description = "总监姓名")
    @NotBlank(message = "总监姓名不能为空")
    private String secondAuditName;


    @Schema(description = "数据-部门Id（hq）")
    @NotBlank(message = "部门Id不能为空")
    private String departmentId;

    @Schema(description = "数据-数据DTSTAMP")
    private Timestamp dtstamp;
}
