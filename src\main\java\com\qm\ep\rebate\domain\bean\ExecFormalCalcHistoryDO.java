package com.qm.ep.rebate.domain.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.ep.rebate.enumerate.AccountEntryStateEnum;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.ExecTypeEnum;
import com.qm.ep.rebate.enumerate.StateEnum;
import com.qm.ep.rebate.common.config.mp.CommonEnumCodeDeserializer;
import com.qm.ep.rebate.common.config.mp.CommonEnumCodeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exec_formal_calc_history")
@Schema(description = "数据:计算历史主表对象")
public class ExecFormalCalcHistoryDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "数据-计算对象ID")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "数据-计算对象类型")
    @TableField("objectType")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "数据-计算类型")
    @TableField("execType")
    private ExecTypeEnum execType;

    @Schema(description = "数据-申请入帐状态")
    @TableField("applyEntryState")
    @JSONField(serializeUsing = CommonEnumCodeSerializer.class,
            deserializeUsing = CommonEnumCodeDeserializer.class)
    private AccountEntryStateEnum applyEntryState;

    @Schema(description = "数据-财务入帐状态")
    @TableField(value = "finEntryState", updateStrategy = FieldStrategy.IGNORED)
    @JSONField(serializeUsing = CommonEnumCodeSerializer.class,
            deserializeUsing = CommonEnumCodeDeserializer.class)
    private AccountEntryStateEnum finEntryState;

    @Schema(description = "数据-历史版本号")
    @TableField("calcVersion")
    private Integer calcVersion;

    @Schema(description = "数据-状态")
    @TableField("state")
    private StateEnum state;

    @Schema(description = "数据-MQ状态")
    @TableField("mqState")
    private StateEnum mqState;

    @Schema(description = "数据-试算开始时间")
    @TableField(value = "begin")
    private Date begin;

    @Schema(description = "数据-试算结束时间")
    @TableField(value = "end")
    private Date end;

    @Schema(description = "数据-sql")
    @TableField("sqlStructure")
    private String sqlStructure;

    @Schema(description = "数据-log")
    @TableField("log")
    private String log;

    @Schema(description = "数据-mq log")
    @TableField("mqLog")
    private String mqLog;

    @Schema(description = "数据-创建者")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "updateon", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-isDeleted")
    @TableField("isDeleted")
    private String isDeleted;

    @Schema(description = "数据-recordVersion")
    @TableField("recordVersion")
    private String recordVersion;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Date dtstamp;

    @Schema(description = "数据-审批流主键")
    @TableField(value = "processInstanceId")
    private String processInstanceId;

    @Schema(description = "数据-申请入账时间")
    @TableField(value = "apply_time")
    private String applyTime;



    @Schema(description = "数据-财务通过时间（兑付/结算时间）")
    @TableField(value = "pass_time")
    private String passTime;

    @Schema(description = "发送失败原因")
    @TableField(value = "applyEntryContent")
    private String applyEntryContent;

    @Schema(description = "计算是否补偿")
    @TableField(value = "accountCompensate")
    private String accountCompensate;

    @Schema(description = "计算补偿次数")
    @TableField(value = "accountCompensateResult")
    private String accountCompensateResult;

    @Schema(description = "计算补偿次数")
    @TableField(value = "accountCompensateCount")
    private String accountCompensateCount;

    @Schema(description = "EP是否补偿")
    @TableField(value = "epCompensate")
    private String epCompensate;

    @Schema(description = "EP补偿次数")
    @TableField(value = "epCompensateResult")
    private String epCompensateResult;

    @Schema(description = "EP补偿次数")
    @TableField(value = "epCompensateCount")
    private String epCompensateCount;
}
