package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.PolicyAllocationInfoDO;
import com.qm.ep.rebate.service.PolicyAllocationService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "政策分配政策担当信息")
@RestController
@RequestMapping("/policyAllocation")
public class PolicyAllocationController {

    @Autowired
    private PolicyAllocationService policyAllocationService;

    @Operation(summary = "保存政策担当信息", description = "[author: 10200571]")
    @PostMapping("/batchSave")
    public JsonResultVo<Boolean> batchSavePolicyStaffs(@RequestBody List<PolicyAllocationInfoDO> list) {
        JsonResultVo<Boolean> jsonResultVo = new JsonResultVo<>();
        policyAllocationService.batchSavePolicyStaffs(list);
        jsonResultVo.setMsg("分配成功！");
        return jsonResultVo;
    }

}
