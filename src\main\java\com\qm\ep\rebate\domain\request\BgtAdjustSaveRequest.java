package com.qm.ep.rebate.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "数据:数据BGT 调整保存请求")
@Data
@Builder
@Valid
public class BgtAdjustSaveRequest {

    /**
     * 返利项目代码
     */
    @Schema(description = "返利项目代码")
    private String classItem;


    /**
     * 政策开始执行时间
     */
    @Schema(description = "政策开始执行时间")
    private String dBegin;

    /**
     * 申请单主表id（可以在URL中拿到）
     */
    @Schema(description = "申请单主表id（可以在URL中拿到）")
    @NotNull(message = "applyMainId 不能为空")
    private Integer applyMainId;

    @Schema(description = "任务实例代码")
    @NotBlank(message = "taskInstanceCode 不能为空")
    private String taskInstanceCode;

}
