package com.qm.ep.rebate.common.config.mp;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import lombok.SneakyThrows;

import java.lang.reflect.Method;
import java.lang.reflect.Type;

public class CommonEnumCodeDeserializer implements ObjectDeserializer {

    @SneakyThrows
    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        String value = parser.parseObject(String.class);
        Class<?> enumClass = Class.forName(type.getTypeName());
        Method getByCode = enumClass.getDeclaredMethod("getByCode", String.class);
        Object enumObj = getByCode.invoke(null, value);

        return enumObj == null ? null : (T) enumObj;
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.LITERAL_STRING;
    }
}
