package com.qm.ep.rebate.domain.bean.board;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:延迟策略")
@Data
public class DelayPolicy {

    @Schema(description = "政策主键")
    private String policyId;

    /**
     * 政策所处节点
     */
    @Schema(description = "政策所处节点")
    private String policyNode;

    @Schema(description = "策略状态")
    private String policyState;
    /**
     * 政策逾期时长
     */
    @Schema(description = "政策逾期时长")
    private int policyDelay;

    /**
     * 政策名称
     */
    @Schema(description = "政策名称")
    private String policyName;

    /**
     * 创建人部门
     */
    @Schema(description = "创建人部门")
    private String creatorPart;

    /**
     * 政策创建人
     */
    @Schema(description = "政策创建人")
    private String creator;

    /**
     * 员工工号
     */
    @Schema(description = "员工工号")
    private String employeeId;

    @Schema(description = "计算状态")
    private String calculateStatus;

    @Schema(description = "策略创建日期")
    private String policyCreateDate;
    @Schema(description = "文件发布日期")
    private String filePublishDate;
    /**
     * 配置计划完成日期
     */
    @Schema(description = "配置计划完成日期")
    private String policyFinishDate;
    @Schema(description = "配置完成于")
    private String configCompletedOn;
    @Schema(description = "策略发布日期")
    private String policyPublishDate;
    @Schema(description = "策略计算日期")
    private String policyCalcDate;
    @Schema(description = "申请入学日期")
    private String applyEntryDate;
    @Schema(description = "报名完成日期")
    private String entryFinishDate;
}
