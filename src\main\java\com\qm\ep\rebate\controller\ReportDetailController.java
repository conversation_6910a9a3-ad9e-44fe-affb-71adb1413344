package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.common.constant.ValidationMessageConstants;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.*;
import com.qm.ep.rebate.domain.vo.ReportMainVO;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.ReportTypeEnum;
import com.qm.ep.rebate.enumerate.SourceTypeEnum;
import com.qm.ep.rebate.service.*;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.ep.rebate.infrastructure.util.RegexUtils;
import com.qm.ep.rebate.infrastructure.util.SqlUtils;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/reportDetail")
@Tag(name = "报表明细")
public class ReportDetailController extends BaseController {

    /** key: 前端字段名, value: 数据库字段名 */
    private static final Map<String, String> WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP = new HashMap<>();

    static {
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("reportName", "REPORT_NAME");
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("calculationStatus", "CALCULATION_STATUS");
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("beginTime", "BEGIN_TIME");
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("endTime", "END_TIME");
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("createOn", "CREATE_ON");
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("createByName", "CREATE_BY_NAME");
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("updateOn", "UPDATE_ON");
        WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP.put("updateByName", "UPDATE_BY_NAME");
    }

    @Autowired
    private ReportMainService reportMainService;
    @Autowired
    private ReportJoinService reportJoinService;
    @Autowired
    private CalcObjectSourceService calcObjectSourceService;
    @Resource
    private FormulaService formulaService;
    @Resource
    private FormulaItemService formulaItemService;
    @Resource
    private ReportFormulaContentService reportFormulaContentService;
    @Resource
    private SystemConfigService systemConfigService;
    @Resource
    private SysPersonOrgService sysPersonOrgService;

    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<ReportMainDO> deleteById(@RequestBody ReportMainDTO reportMainDTO){
        JsonResultVo<ReportMainDO> resultObj = new JsonResultVo<>();
        if(BootAppUtil.isNullOrEmpty(reportMainDTO.getDealerCode()) || !"ALL".equals(reportMainDTO.getDealerCode())){
            throw new QmException("当前账户没有删除报表明细权限");
        }
        reportMainService.deleteReport(reportMainDTO);
        return resultObj;
    }

    @Operation(summary = "判断主、从数据源是否发生变更 ", description = "[author: 10200571]")
    @PostMapping("/getChange")
    public JsonResultVo getChange(@RequestBody ReportMainDO reportMainDO){
        JsonResultVo<Object> res = new JsonResultVo<>();
        Map<String, Set<String>> tableWithFieldMap = reportMainService.getChangedDataSourceName(reportMainDO.getId());
        if(CollUtil.isNotEmpty(tableWithFieldMap)){
            res.setData(tableWithFieldMap);
        }
        return res;
    }

    @Operation(summary = "查询报表明细", description = "[author: 10200571]")
    @PostMapping("/info")
    public JsonResultVo<ReportMainVO> reportInfo(@RequestBody ReportMainDTO reportMainDTO){
        JsonResultVo<ReportMainVO> jsonResultVo = new JsonResultVo<>();
        // 校验主键
        if(BootAppUtil.isNullOrEmpty(reportMainDTO.getId())){
            jsonResultVo.setMsgErr("查询失败");
            return jsonResultVo;
        }
        jsonResultVo.setData(reportMainService.getReport(reportMainDTO.getId()));
        return jsonResultVo;
    }

    @Operation(summary = "查询报表明细列表", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ReportMainDO>> table(@RequestBody ReportMainDTO reportMainDTO){
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);
        LoginKeyDO userInfo = getUserInfo();

        QmQueryWrapper<ReportMainDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ReportMainDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ReportMainDO::getReportType, ReportTypeEnum.FREE);
        //起始截止日期
        if (!BootAppUtil.isNullOrEmpty(reportMainDTO.getStartTime())) {
            //操作日期
            if (RebateConstants.CREATE_TIME.equals(reportMainDTO.getDateType())) {
                lambdaWrapper.ge(ReportMainDO::getCreateOn, reportMainDTO.getStartTime());
            } else if (RebateConstants.UPDATE_TIME.equals(reportMainDTO.getDateType())) {
                lambdaWrapper.ge(ReportMainDO::getUpdateOn, reportMainDTO.getStartTime());
            }
        }
        if (!BootAppUtil.isNullOrEmpty(reportMainDTO.getEndTime())) {
            //操作日期
            if (RebateConstants.CREATE_TIME.equals(reportMainDTO.getDateType())) {
                lambdaWrapper.le(ReportMainDO::getCreateOn, reportMainDTO.getEndTime());
            } else if (RebateConstants.UPDATE_TIME.equals(reportMainDTO.getDateType())) {
                lambdaWrapper.le(ReportMainDO::getUpdateOn, reportMainDTO.getEndTime());
            }
        }

        // 报表名称
        lambdaWrapper.like(!BootAppUtil.isNullOrEmpty(reportMainDTO.getReportName()),
                ReportMainDO::getReportName, SqlUtils.escape(reportMainDTO.getReportName()));

        if("bx".equals(company)){
            List<String> createByList = sysPersonOrgService.queryPersonIdAtRelatedLevel(userInfo.getOperatorId());
            lambdaWrapper.in(ReportMainDO::getCreateBy, createByList);
        }

        lambdaWrapper.orderByDesc(ReportMainDO::getCreateOn);

        // 将前端传回的字段名称转化为数据库字段名称
        SqlUtils.convertFilterSortBy(WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP, reportMainDTO);
        SqlUtils.convertFilterWhere(WEB_COLUMN_WITH_DATASOURCE_FIELD_MAP, reportMainDTO);

        QmPage<ReportMainDO> data = reportMainService.table(queryWrapper, reportMainDTO);

        if("bx".equals(company)){
            List<String> createUserList = sysPersonOrgService.queryPersonIdAtSameLevel(userInfo.getOperatorId());
            data.getItems().forEach(item -> {
                // 只有同组织的用户可以互相编辑
                boolean editable = createUserList.contains(item.getCreateBy());
                item.setEditable(editable);
            });
        }else{
            data.getItems().forEach(item -> item.setEditable(true));
        }

        JsonResultVo<QmPage<ReportMainDO>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<String> save(@RequestBody ReportMainDTO reportMainDTO){
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        String msg = validateReportMainDTO(reportMainDTO);
        if(BootAppUtil.isnotNullOrEmpty(msg)){
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String reportId = reportMainService.saveReport(reportMainDTO);
        jsonResultVo.setData(reportId);
        return jsonResultVo;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateReportMainDTO(ReportMainDTO reportMainDTO){
        // 校验是否为空
        if(BootAppUtil.isNullOrEmpty(reportMainDTO)){
            return "保存失败";
        }
        return validateId(reportMainDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateId(ReportMainDTO reportMainDTO){
        // 校验主键是否合法
        if(BootAppUtil.isnotNullOrEmpty(reportMainDTO.getId()) && reportMainDTO.getId().length() > RebateConstants.ID_LENGTH_36){
            return "主键长度过长";
        }
        return validateReport(reportMainDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateReport(ReportMainDTO reportMainDTO){
        // 校验报表名称是否合法
        if(BootAppUtil.isNullOrEmpty(reportMainDTO.getReportName())){
            return "报表名称不能为空";
        }
        if(reportMainDTO.getReportName().length() > RebateConstants.FIELD_LENGTH_255){
            return "报表名称长度过长";
        }
        if(!RegexUtils.validateName(reportMainDTO.getReportName())){
            return "名称只能包含数字、字母或汉字";
        }
        // 校验报表描述是否合法
        if(BootAppUtil.isnotNullOrEmpty(reportMainDTO.getDescription()) && reportMainDTO.getDescription().length() > RebateConstants.FIELD_LENGTH_255){
            return "报表描述长度过长";
        }
        return validateOutputColumn(reportMainDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateOutputColumn(ReportMainDTO reportMainDTO){
        // 校验兑付对象是否合法
        if(BootAppUtil.isNullOrEmpty(reportMainDTO.getCashObject())){
            return "兑付对象不能为空";
        }
        if(reportMainDTO.getCashObject().length() > RebateConstants.FIELD_LENGTH_65535){
            return "兑付对象长度过长";
        }
        // 校验输出字段是否合法
        if(reportMainDTO.getOutputFieldList() == null || reportMainDTO.getOutputFieldList().isEmpty()){
            return "输出字段不能为空";
        }
        // 校验数据字段中是否包含经销商代码
        /*List<String> outputFieldList = reportMainDTO.getOutputFieldList();
        boolean containsDealerCodeFlag = false;
        for (String json : outputFieldList) {
            JSONObject field = JSON.parseObject(json);
            if("经销商代码".equals(field.get("fieldId"))){
                containsDealerCodeFlag = true;
                break;
            }
        }
        if(!containsDealerCodeFlag){
            return "输出字段必须包含经销商代码";
        }*/
        // 校验关联方案
        List<ReportJoinDTO> reportJoinDTOList = reportMainDTO.getReportJoinDTOList();
        if(reportJoinDTOList != null && !reportJoinDTOList.isEmpty()){
            return validateReportJoin(reportMainDTO);
        }else{
            return "关联方案不能为空";
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateReportJoin(ReportMainDTO reportMainDTO){
        List<ReportJoinDTO> reportJoinDTOList = reportMainDTO.getReportJoinDTOList();
        List<String> reportJoinTableList = reportJoinDTOList.stream().map(ReportJoinDTO::getJoinTable).collect(Collectors.toList());
        Set<String> reportJoinTableSet = new HashSet<>(reportJoinTableList);
        if(reportJoinTableSet.size() < reportJoinTableList.size()){
            return "不可多次关联同一个方案";
        }
        for (ReportJoinDTO joinDTO : reportJoinDTOList) {
            try{
                validateJoinTable(joinDTO);
                validateJoinOn(joinDTO);
            }catch(QmException e){
                return e.getMessage();
            }
        }
        // 校验公式
        return validateFormulaContent(reportMainDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinTable(ReportJoinDTO joinDTO){
        // 校验关联方式
        if(BootAppUtil.isNullOrEmpty(joinDTO.getJoinType())){
            throw new QmException("关联类型不能为空");
        }
        if(joinDTO.getJoinType().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("关联类型长度过长");
        }
        // 校验关联方案表
        if(BootAppUtil.isNullOrEmpty(joinDTO.getJoinTable())){
            throw new QmException("计算方案不能为空");
        }
        if(joinDTO.getJoinTable().length() > RebateConstants.FIELD_LENGTH_65535){
            throw new QmException("计算方案长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinOn(ReportJoinDTO joinDTO){
        List<ReportJoinOnDTO> reportJoinOnDTOList = joinDTO.getReportJoinOnDTOList();
        for (ReportJoinOnDTO joinOnDTO : reportJoinOnDTOList) {
            validateJoinOnLeftCondition(joinOnDTO);
            validateJoinOnOperation(joinOnDTO);
            validateJoinOnRightCondition(joinOnDTO);
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinOnLeftCondition(ReportJoinOnDTO joinDTO){
        // 校验关联方案中兑付字段
        if(BootAppUtil.isNullOrEmpty(joinDTO.getLeftFieldName()) || BootAppUtil.isNullOrEmpty(joinDTO.getLeftFieldFrom())){
            throw new QmException("关联方案中兑付字段不能为空");
        }
        if(joinDTO.getLeftFieldName().length() > RebateConstants.FIELD_LENGTH_255
                || joinDTO.getLeftFieldFrom().length() > RebateConstants.FIELD_LENGTH_255
                || (BootAppUtil.isnotNullOrEmpty(joinDTO.getLeftTableSource())
                && joinDTO.getLeftTableSource().length() > RebateConstants.FIELD_LENGTH_255)){
            throw new QmException("关联方案中兑付字段长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinOnOperation(ReportJoinOnDTO joinDTO){
        // 校验前括号
        if(joinDTO.getBracketBefore() != null && joinDTO.getBracketBefore().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("关联方案中括号长度过长");
        }
        // 校验运算符
        if(BootAppUtil.isNullOrEmpty(joinDTO.getCalculationLogic())){
            throw new QmException("关联方案中运算符不能为空");
        }
        if(joinDTO.getCalculationLogic().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("关联方案中运算符长度过长");
        }
        // 校验后括号
        if(joinDTO.getBracketAfter() != null && joinDTO.getBracketAfter().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("关联方案中括号长度过长");
        }
        // 校验关联逻辑
        if(joinDTO.getAssociationLogic() != null && joinDTO.getAssociationLogic().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("关联方案中关联逻辑长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinOnRightCondition(ReportJoinOnDTO joinDTO){
        // 校验关联方案中条件类型
        if(BootAppUtil.isNullOrEmpty(joinDTO.getConditionType())){
            throw new QmException("关联方案中条件类型不能为空");
        }
        if(joinDTO.getConditionType().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("关联方案中条件类型长度过长");
        }
        // 校验条件值
        if(!RebateConstants.VALUE.equals(joinDTO.getConditionType()) && BootAppUtil.isNullOrEmpty(joinDTO.getRightValue())){
            throw new QmException("关联方案中条件值不能为空");
        }
        if(BootAppUtil.isnotNullOrEmpty(joinDTO.getRightValue()) && joinDTO.getRightValue().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException(ValidationMessageConstants.REPORT_PLAN_CONDITION_LENGTH_ERROR);
        }
        if(RebateConstants.FIELD.equals(joinDTO.getConditionType())) {
            if(BootAppUtil.isNullOrEmpty(joinDTO.getRightFieldFrom())) {
                throw new QmException("关联方案中条件值不能为空");
            }
            if(joinDTO.getRightFieldFrom().length() > RebateConstants.FIELD_LENGTH_255){
                throw new QmException(ValidationMessageConstants.REPORT_PLAN_CONDITION_LENGTH_ERROR);
            }
            if(BootAppUtil.isnotNullOrEmpty(joinDTO.getRightTableSource()) && joinDTO.getRightTableSource().length() > RebateConstants.FIELD_LENGTH_255){
                throw new QmException(ValidationMessageConstants.REPORT_PLAN_CONDITION_LENGTH_ERROR);
            }
        }

        if(RebateConstants.NUMBER.equals(joinDTO.getConditionType()) && !NumberUtil.isNumber(joinDTO.getRightValue())){
            throw new QmException("关联方案中条件值格式有误");
        }
        if(RebateConstants.DATE.equals(joinDTO.getConditionType()) && !DateUtil.validateDateTime(joinDTO.getRightValue(), RebateConstants.DATE_FORMAT_PATTERN)){
            throw new QmException("关联方案中条件值格式有误");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFormulaContent(ReportMainDTO reportMainDTO){
        List<ReportFormulaContentDTO> reportFormulaContentDTOList = reportMainDTO.getReportFormulaContentDTOList();
        if(reportFormulaContentDTOList != null && !reportFormulaContentDTOList.isEmpty()){
            List<String> formulaFieldNameList = reportFormulaContentDTOList.stream().map(ReportFormulaContentDTO::getName).collect(Collectors.toList());
            Set<String> formulaFieldNameSet = new HashSet<>(formulaFieldNameList);
            if(formulaFieldNameSet.size() < formulaFieldNameList.size()){
                return "公式列名不可重复";
            }
            for (ReportFormulaContentDTO content : reportFormulaContentDTOList) {
                try{
                    validateFormulaFieldName(content);
                    validateFormulaContent(content);
                    validateFormulaProp(content);
                }catch(QmException e){
                    return e.getMessage();
                }
            }
        }
        // 校验筛选条件
        return validateFilterCondition(reportMainDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFormulaFieldName(ReportFormulaContentDTO content){
        // 校验公式列名
        if(BootAppUtil.isNullOrEmpty(content.getName())){
            throw new QmException("公式列名不能为空");
        }
        if(content.getName().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("公式列名长度过长");
        }
        if(!RegexUtils.validateName(content.getName())){
            throw new QmException("公式列名只能包含数字、字母或汉字");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFormulaContent(ReportFormulaContentDTO content){
        // 校验公式是否合法
        if(CollUtil.isEmpty(content.getContent())){
            throw new QmException("公式不能为空");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFormulaProp(ReportFormulaContentDTO content){
        // 校验取值类型是否合法
        if(content.getFetchType() == null){
            throw new QmException("取值类型不能为空");
        }
        // 校验分母为零时计算规则是否合法
        if(content.getRule() == null){
            throw new QmException("分母为零时计算规则不能为空");
        }
        // 校验保留小数位是否合法
        if(content.getDecimal() == null){
            throw new QmException("保留小数位不能为空");
        }
        if(content.getDecimal().compareTo(0) < 0){
            throw new QmException("保留小数位必须大于或等于零");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFilterCondition(ReportMainDTO reportMainDTO){
        List<ReportConditionDTO> reportConditionDTOList = reportMainDTO.getReportConditionDTOList();
        if(reportConditionDTOList != null && !reportConditionDTOList.isEmpty()){
            for (ReportConditionDTO condition : reportConditionDTOList) {
                try{
                    validateWhereLeftCondition(condition);
                    validateWhereOperation(condition);
                    validateWhereRightCondition(condition);
                }catch(QmException e){
                    return e.getMessage();
                }
            }
        }
        return "";
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateWhereLeftCondition(ReportConditionDTO condition){
        // 校验关联方案中兑付字段
        if(BootAppUtil.isNullOrEmpty(condition.getLeftFieldName()) || BootAppUtil.isNullOrEmpty(condition.getLeftFieldFrom())){
            throw new QmException("筛选条件中兑付字段不能为空");
        }
        if(condition.getLeftFieldName().length() > RebateConstants.FIELD_LENGTH_255
                || condition.getLeftFieldFrom().length() > RebateConstants.FIELD_LENGTH_255
                || (BootAppUtil.isnotNullOrEmpty(condition.getLeftTableSource())
                && condition.getLeftTableSource().length() > RebateConstants.FIELD_LENGTH_255)){
            throw new QmException("筛选条件中兑付字段长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateWhereOperation(ReportConditionDTO condition){
        // 校验前括号
        if(condition.getBracketBefore() != null && condition.getBracketBefore().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("筛选条件中括号长度过长");
        }
        // 校验运算符
        if(BootAppUtil.isNullOrEmpty(condition.getCalculationLogic())){
            throw new QmException("筛选条件中运算符不能为空");
        }
        if(condition.getCalculationLogic().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("筛选条件中运算符长度过长");
        }
        // 校验后括号
        if(condition.getBracketAfter() != null && condition.getBracketAfter().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("筛选条件中括号长度过长");
        }
        // 校验关联逻辑
        if(condition.getAssociationLogic() != null && condition.getAssociationLogic().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("筛选条件中关联逻辑长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateWhereRightCondition(ReportConditionDTO condition){
        // 校验关联方案中条件类型
        if(BootAppUtil.isNullOrEmpty(condition.getConditionType())){
            throw new QmException("筛选条件中条件类型不能为空");
        }
        if(condition.getConditionType().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("筛选条件中条件类型长度过长");
        }
        // 校验条件值
        if(!RebateConstants.VALUE.equals(condition.getConditionType()) && BootAppUtil.isNullOrEmpty(condition.getRightValue())){
            throw new QmException("筛选条件中条件值不能为空");
        }
        if(BootAppUtil.isnotNullOrEmpty(condition.getRightValue()) && condition.getRightValue().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException(ValidationMessageConstants.REPORT_FILTER_CONDITION_LENGTH_ERROR);
        }
        if(RebateConstants.FIELD.equals(condition.getConditionType())) {
            if(BootAppUtil.isNullOrEmpty(condition.getRightFieldFrom())) {
                throw new QmException("筛选条件中条件值不能为空");
            }
            if(condition.getRightFieldFrom().length() > RebateConstants.FIELD_LENGTH_255){
                throw new QmException(ValidationMessageConstants.REPORT_FILTER_CONDITION_LENGTH_ERROR);
            }
            if(BootAppUtil.isnotNullOrEmpty(condition.getRightTableSource()) && condition.getRightTableSource().length() > RebateConstants.FIELD_LENGTH_255){
                throw new QmException(ValidationMessageConstants.REPORT_FILTER_CONDITION_LENGTH_ERROR);
            }
        }

        if(RebateConstants.NUMBER.equals(condition.getConditionType()) && !NumberUtil.isNumber(condition.getRightValue())){
            throw new QmException("筛选条件中条件值格式有误");
        }
        if(RebateConstants.DATE.equals(condition.getConditionType()) && !DateUtil.validateDateTime(condition.getRightValue(), RebateConstants.DATE_FORMAT_PATTERN)){
            throw new QmException("筛选条件中条件值格式有误");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @PostMapping("/turnDataSource")
    public JsonResultVo turnDataSource() {
        JsonResultVo<Object> res = new JsonResultVo<>();
        List<ReportMainDO> mainDOList = reportMainService.list();
        mainDOList.forEach(main -> {
            String reportId = main.getId();
            ReportTypeEnum reportType = main.getReportType();
            String cashObject = main.getCashObject();
            if(ReportTypeEnum.FREE.equals(reportType) && cashObject.contains("{")){
                CalcObjectSourceDO sourceDO = JSON.parseObject(cashObject, CalcObjectSourceDO.class);

                sourceDO.setForObjectId(reportId);
                sourceDO.setForType(CalcObjectTypeEnum.REPORT_FREE);
                sourceDO.setForSourceType(SourceTypeEnum.MAIN);

                calcObjectSourceService.save(sourceDO);
                main.setCashObject(sourceDO.getId());
                reportMainService.updateById(main);
            }
        });

        List<ReportJoinDO> joinList = reportJoinService.list();
        joinList.forEach(joinDO -> {
            String reportId = joinDO.getReportId();
            String joinTable = joinDO.getJoinTable();
            if(joinTable.contains("{")){
                CalcObjectSourceDO sourceDO = JSON.parseObject(joinTable, CalcObjectSourceDO.class);

                sourceDO.setForObjectId(reportId);
                sourceDO.setForType(CalcObjectTypeEnum.REPORT_FREE);
                sourceDO.setForSourceType(SourceTypeEnum.SUB);

                calcObjectSourceService.save(sourceDO);
                joinDO.setJoinTable(sourceDO.getId());
                reportJoinService.updateById(joinDO);
            }
        });

        return res;
    }

    @Operation(summary = "迁移公式内容数据", description = "[author: 10200571]")
    @PostMapping("/migrateFormulaData")
    public void migrateFormulaData(){
        List<ReportFormulaContentDO> formulaContentDOS = reportFormulaContentService.list();
        formulaContentDOS.forEach(f->{
            FormulaDO formulaDO = new FormulaDO();
            formulaDO.setPolicyId("");
            formulaDO.setObjectId(f.getReportId());
            formulaDO.setObjectType(CalcObjectTypeEnum.REPORT_FREE);
            formulaDO.setName(f.getName());
            formulaDO.setDecimal(f.getDecimal());
            formulaDO.setRule(f.getRule());
            formulaDO.setFetchType(f.getFetchType());
            formulaDO.setCreateOn(f.getCreateOn());
            formulaDO.setCreateBy(f.getCreateBy());
            formulaDO.setUpdateOn(f.getUpdateOn());
            formulaDO.setUpdateBy(f.getUpdateBy());
            formulaDO.setDtstamp(f.getDtstamp());
            formulaService.save(formulaDO);

            List<CommonFormulaItemDTO> formulaContents = JSONUtils.packingDOListFromJsonStr(f.getContent(), CommonFormulaItemDTO.class);
            formulaContents.forEach(i->{
                FormulaItemDO formulaItemDO = new FormulaItemDO();
                formulaItemDO.setFormulaId(formulaDO.getId());
                formulaItemDO.setValue(i.getValue());
                formulaItemDO.setType(i.getType());
                formulaItemDO.setTableName(i.getTableName());
                formulaItemDO.setTableField(i.getTableField());
                formulaItemDO.setRule(i.getRule());
                formulaItemService.save(formulaItemDO);
            });
        });
    }
}
