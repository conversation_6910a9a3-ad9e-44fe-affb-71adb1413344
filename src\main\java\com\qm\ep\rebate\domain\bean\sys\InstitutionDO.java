package com.qm.ep.rebate.domain.bean.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 *
 * 业务机构   Institution
 *  
 *
 * <AUTHOR>
 * @since 2020-08-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysc060")
@Schema(description = "数据:业务机构   Institution")
public class InstitutionDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-机构代码")
    @TableField("VINSTCODE")
    private String vinstcode;

    @Schema(description = "数据-机构名称")
    @TableField("VINSTTEXT")
    private String vinsttext;

    @Schema(description = "数据-机构类型")
    @TableField("VINSTTYPE")
    private String vinsttype;

    @Schema(description = "数据-父级机构ID")
    @TableField("NPARENTINSTID")
    private String nparentinstid;

    @Schema(description = "数据-公司ID")
    @TableField(value = "NCOMPANYID")
    private String ncompanyid;

    @Schema(description = "数据-停用标识")
    @TableField("VSTOP")
    private String vstop;

    @Schema(description = "数据-停用日期")
    @TableField("DSTOP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dstop;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-负责人")
    @TableField("VPRINCIPAL")
    private String vprincipal;

    @Schema(description = "数据-联系人")
    @TableField("VLINKMAN")
    private String vlinkman;

    @Schema(description = "数据-固定电话")
    @TableField("VTEL")
    private String vtel;

    @Schema(description = "数据-移动电话")
    @TableField("VMOBILE")
    private String vmobile;

    @SuppressWarnings("squid:S1948")
    @Schema(description = "数据-子节点 树结构使用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @TableField(exist = false)
    private  List<InstitutionDO> children = null;

    @TableField(exist = false)
    @Schema(description = "层级")
    private Integer nlevel;

    @TableField(exist = false)
    @Schema(description = "组织类型")
    private String vorgtype;

    @TableField(exist = false)
    @Schema(description = "组织类型名称")
    private String vinsttypename;

    @TableField(exist = false)
    @Schema(description = "机构类型标识")
    private String vstop1;

    @TableField(exist = false)
    @Schema(description = "名称")
    private String text;

    @TableField(exist = false)
    @Schema(description = "代码")
    private String value;

    @TableField(exist = false)
    @Schema(description = "组织ID")
    private String norgan;

}
