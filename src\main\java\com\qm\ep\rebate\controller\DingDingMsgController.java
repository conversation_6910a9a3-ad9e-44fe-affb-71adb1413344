package com.qm.ep.rebate.controller;


import com.qm.ep.rebate.domain.dto.*;
import com.qm.ep.rebate.domain.vo.DingPublishedVO;
import com.qm.ep.rebate.domain.vo.TimeAxisVO;
import com.qm.ep.rebate.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 *
 * Controller
 * 钉钉推送需求
 *
 */
@Tag(name = "钉钉消息推送", description = "[author: 10200571]")
@RestController
@RequestMapping("/dingDing")
public class DingDingMsgController extends BaseController {
    @Autowired
    private DingDingService dingDingService;



    /**
     * 钉钉看板查询
     */
    @Operation(summary = "钉钉看板查询", description = "[author: 10200571]")
    @PostMapping("/getList")
    public JsonResultVo<List<TimeAxisVO>> getListDing(@RequestBody PolicyDTO tempDTO) {
        JsonResultVo<List<TimeAxisVO>> ret = new JsonResultVo<>();
        ret.setData(dingDingService.getListDing(tempDTO));
        return ret;
    }

    /**
     * 钉钉看板查询-已公布政策
     */
    @Operation(summary = "钉钉看板查询-已公布政策", description = "[author: 10200571]")
    @PostMapping("/getListDingPublished")
    public JsonResultVo<List<DingPublishedVO>> getListDingPublished(@RequestBody PolicyDTO tempDTO) {
        JsonResultVo<List<DingPublishedVO>> ret = new JsonResultVo<>();
        ret.setData(dingDingService.getListDingPublished(tempDTO));
        return ret;
    }



}
