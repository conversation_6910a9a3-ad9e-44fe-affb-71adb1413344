package com.qm.ep.rebate.domain.bean;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("BUSINESSCONSTRUCTION")
@Schema(description = "数据:业务结构对象")
public class BusinessConstructionDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Excel(name = "业务底表名", orderNum = "1")
    @Schema(description = "数据-业务底表名")
    @TableField("TABLENAME")
    private String tableName;

    @Excel(name = "业务底表字段名", orderNum = "2")
    @Schema(description = "数据-业务底表字段名")
    @TableField("FIELDNAME")
    private String fieldName;

    @Schema(description = "数据-业务底表id")
    @TableField("table_id")
    private String tableId;

    @Schema(description = "数据-业务底表字段类型")
    @TableField("field_type")
    private String fieldType;

    @Schema(description = "数据-数值型保留的小数位数")
    @TableField("decimalPoint")
    private Integer decimalPoint;

    @Schema(description = "数据-必填项(0-不是,1-是)")
    @TableField("required")
    private Integer required;

    @Schema(description = "数据-联合唯一标识")
    @TableField("unique_key")
    private Integer uniqueKey;

    @Schema(description = "数据-排序")
    @TableField("sort")
    private Integer sort;

    @Excel(name = "业务底表关联字段", orderNum = "3")
    @Schema(description = "数据-业务底表关联字段")
    @TableField("RELEVANCEFIELDNAME")
    private String relevanceFieldName;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

}
