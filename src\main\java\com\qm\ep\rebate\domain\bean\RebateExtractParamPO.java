package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *  
 * 
 *
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Getter
@Setter
@TableName("rebate_extract_param")
public class RebateExtractParamPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 参数编码
     */
    @TableField("param_code")
    private String paramCode;

    /**
     * 参数名称
     */
    @TableField("param_name")
    private String paramName;

    /**
     * 参数值
     */
    @TableField("param_value")
    private String paramValue;

    /**
     * 参数说明
     */
    @TableField("param_remark")
    private String paramRemark;

    /**
     * 变更时间
     */
    @TableField("change_time")
    private String changeTime;

    /**
     * 变更人姓名
     */
    @TableField("change_user")
    private String changeUser;

    /**
     * 创建者
     */
    @TableField("CREATEBY")
    private String createBy;

    /**
     * 创建日期
     */
    @TableField("CREATEON")
    private LocalDateTime createOn;

    /**
     * 更新者
     */
    @TableField("UPDATEBY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATEON")
    private LocalDateTime updateOn;

    /**
     * 时间戳
     */
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;


}
