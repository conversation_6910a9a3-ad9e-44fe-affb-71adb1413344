package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:预测回扣")
@Data
public class PredicatedRebate {
    @Schema(description = "总数量")
    private int totalQty;
    @Schema(description = "总AMT")
    private int totalAmt;
    @Schema(description = "平均 AMT")
    private int avgAmt;

    @Schema(description = "系列")
    private String series;

    @Schema(description = "联合国真实名单")
    private List<Extra> unRealList;

    @Schema(description = "数据:额外")
    @Data
    public static class Extra {
        @Schema(description = "总数量")
        private int totalQty;
        @Schema(description = "总AMT")
        private int totalAmt;
        @Schema(description = "平均 AMT")
        private int avgAmt;
        @Schema(description = "差异数量")
        private int diffQty;
        @Schema(description = "系列")
        private String series;
        @Schema(description = "差异总 AMT")
        private int diffTotalAmt;
        @Schema(description = "数据diff avg amt")
        private int diffAvgAmt;

    }
}
