package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CALFACTORMAIN")
@Schema(description = "数据:计算因子主表对象")
public class CalFactorMainDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策主键")
    @TableField("POLICYID")
    private String policyId;

    @Schema(description = "数据-计算因子名称")
    @TableField("FACTORNAME")
    private String factorName;

    @Schema(description = "数据-计算因子描述")
    @TableField("FACTORDIC")
    private String description;

    @Schema(description = "数据-主数据源")
    @TableField("MAINTABLE")
    private String mainTable;

    @Schema(description = "数据-取值逻辑")
    @TableField("LOGIC")
    private String logic;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Schema(description = "是否政策结果")
    @TableField("is_policy_result")
    private String isPolicyResult;

    @Schema(description = "是否经销商可见")
    @TableField("is_visible")
    private String isVisible;

    @Schema(description = "是否可编辑")
    @TableField(exist = false)
    private String isEdit;

}
