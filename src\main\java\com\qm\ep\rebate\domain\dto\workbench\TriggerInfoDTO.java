package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Schema(description = "数据:实体类-触发信息 DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TriggerInfoDTO {

    /**
     * 云原生用户code, String(64)
     */
    @Schema(description = "数据-云原生用户code")
    private String userCode;

    /**
     * 能力中心侧业务ID, String(64), 业务单元下所有
     * 任务实例唯一
     */
    @Schema(description = "数据-能力中心侧业务ID")
    private String bizId;

    /**
     * 自定义逾期天数配置索引, 取值(1,2,3)
     */
    @Schema(description = "数据-自定义逾期天数")
    private Integer customOverdueDaysIndex;

    /**
     * 创建任务描述, String(500), (触发器-可空;)
     */
    @Schema(description = "数据-创建任务描述")
    private String description;

    /**
     * 自定义Url
     */
    @Schema(description = "数据-自定义Url")
    private String customUrl;

    /**
     * 自定义Url参数, 自动拼接参数到模板配置URL或者自定义Url
     */
    @Schema(description = "数据-自定义url参数")
    private Map<String, String> customUrlParams;

}
