package com.qm.ep.rebate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.*;
import com.qm.ep.rebate.domain.dto.workbench.*;
import com.qm.ep.rebate.domain.request.RebateQueryListRequest;
import com.qm.ep.rebate.domain.response.RebateQueryResponse;
import com.qm.ep.rebate.domain.vo.PolicyStructureVO;
import com.qm.ep.rebate.domain.vo.RebateCalculatorVO;
import com.qm.ep.rebate.domain.vo.RebateQueryVO;
import com.qm.ep.rebate.domain.vo.TimeAxisBoardVO;
import com.qm.ep.rebate.enumerate.*;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.ep.rebate.mapper.*;
import com.qm.ep.rebate.service.*;
import com.qm.ep.rebate.service.approve.ProcessService;
import com.qm.ep.rebate.service.async.RebateTriAsyncTask;
import com.qm.ep.rebate.service.workbench.PlatformUserService;
import com.qm.ep.rebate.service.workbench.WorkbenchService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.TableUtils;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.qm.ep.rebate.enumerate.RebateCodeEnum.FBJSJRZ;

/**
 *
 * Controller
 * 政策基础信息表JsonResultVo
 *
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Tag(name = "政策基础信息表")
@RestController
@RequestMapping("/policy")
@Slf4j
public class PolicyController extends BaseController {

    @Resource
    private PolicyAuditService policyAuditService;
    @Resource
    private PolicyBudgetMapper policyBudgetMapper;
    @Resource
    private ClassItemMapper classItemMapper;
    @Autowired
    private RebateTriAsyncTask rebateTriAsyncTask;

    @Autowired
    private BgtApplyMainService bgtApplyMainService;

    @Autowired
    private ExecFormalCalcHistoryMapper execFormalCalcHistoryMapper;
    @Autowired
    private PolicyBudgetService policyBudgetService;
    @Autowired
    private PolicyService policyService;

    @Autowired
    private PolicyDealerService policyDealerService;


    @Autowired
    private PolicyMapper policyMapper;
    @Resource
    private SystemConfigService systemConfigService;

    private static final String POLICY_ID_IS_NULL_ERR_MSG = "政策ID为空";

    private static final String POLICY_NOT_FOUND_ERR_MSG = "未找到相关政策配置";

    @Autowired
    private CombinationMainService combinationMainService;

    @Autowired
    private CombinationDetailService combinationDetailService;



    @Resource
    private PolicyCopyPathService policyCopyPathService;




    @Resource
    private ProcessService processService;

    @Resource
    private PlatformUserService platformUserService;

    @Resource
    private PolicyPublishedService policyPublishedService;

    @Resource
    private WorkbenchService workbenchService;


    @Resource
    private PolicyAllocationService policyAllocationService;

    @Resource
    private PolicyPublishConfigService policyPublishConfigService;

    @Resource
    private BusinessDataService businessDataService;

    @Autowired
    private PolicyPublishedMapper policyPublishedMapper;

    @Resource
    private PolicyFlagService policyFlagService;



    @Operation(summary = "政策保存和修改", description = "[author: ********]")
    @PostMapping("/save")
    public JsonResultVo<PolicyDO> savePolicy(@RequestBody PolicyDO request) {
        return policyService.savePolicy(request);
    }


    @Operation(summary = "政策作废", description = "[author: ********]")
    @PostMapping("/discardById")
    public JsonResultVo<PolicyDO> discardById(@RequestBody PolicyDO request) {
        return policyService.discardById(request.getId());
    }

    @Operation(summary = "政策取消作废", description = "[author: ********]")
    @PostMapping("/cancelDiscardById")
    public JsonResultVo<PolicyDO> cancelDiscardById(@RequestBody PolicyDO tempDO) {
        return policyService.cancelDiscardById(tempDO.getId());
    }

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: ********]")
    @PostMapping("/deleteById")
    public JsonResultVo<PolicyDO> deletePolicyAllInfoById(@RequestBody PolicyDO tempDO) {
        return policyService.deletePolicyAllInfoById(tempDO.getId());
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: ********]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<PolicyDO>> table(@RequestBody PolicyDTO tempDTO) {

        JsonResultVo<QmPage<PolicyDO>> ret = new JsonResultVo<>();

        QmPage<PolicyDO> page = policyService.listAll(tempDTO, getUserInfo());

        ret.setData(page);
        return ret;
    }

    @Operation(summary = "根据id查询政策详情", description = "[author: ********]")
    @PostMapping("/getByPolicyId")
    public JsonResultVo<PolicyDO> getByPolicyId(@RequestBody PolicyDTO tempDTO) {

        JsonResultVo<PolicyDO> ret = new JsonResultVo<>();

        String policyId = tempDTO.getPolicyId();

        PolicyDO policyDO = policyMapper.selectPolicyById(policyId);
        if (policyDO == null) {
            return ret;
        }
        // 查询是否存在预算申请
        BgtApplyMainDO mainDO = bgtApplyMainService.getExistSubmitApply(policyId, policyDO.getClassItemCode());

        if (mainDO != null) {
            policyDO.setExistApply("1");
        }

        //因为预算使用查询-关联政策-详情前端取值逻辑是按照policyId取值，所以这里将policyId装载到DO中
        policyDO.setPolicyId(policyId);

        ret.setData(policyDO);

        return ret;
    }


    @Operation(summary = "政策复制", description = "[author: ********]")
    @PostMapping("/copy")
    public JsonResultVo<PolicyDO> copyPolicy(@RequestBody PolicyDO tempDO) throws ParseException {
        LoginKeyDO loginKeyDO = getUserInfo();
        JsonResultVo<PolicyDO> resultObj = new JsonResultVo<>();
        resultObj.setData(policyService.copyPolicy(tempDO, loginKeyDO));
        resultObj.setMsg("保存成功！");
        return resultObj;

    }


    @Operation(summary = "获取政策详情", description = "[author: ********]")
    @PostMapping("/detail")
    public JsonResultVo<PolicyDO> detail(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<PolicyDO> resultObj = new JsonResultVo<>();
        if (BootAppUtil.isNullOrEmpty(policyDTO.getId())) {
            resultObj.setMsgErr(POLICY_ID_IS_NULL_ERR_MSG);
            return resultObj;
        }

        PolicyDO policyDO = policyService.getById(policyDTO.getId());
        if (null == policyDO) {
            resultObj.setMsgErr(POLICY_NOT_FOUND_ERR_MSG);
            return resultObj;
        }

        resultObj.setData(policyDO);
        return resultObj;
    }

    @Operation(summary = "获取政策组织", description = "[author: ********]")
    @PostMapping("/getOrgCode")
    public JsonResultVo<String> getOrgCode(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<String> resultObj = new JsonResultVo<>();
        if (policyDTO == null || StringUtils.isBlank(policyDTO.getId())) {
            return resultObj;
        }
        PolicyDO policyDO = policyService.getById(policyDTO.getId());
        if (policyDO == null || StringUtils.isBlank(policyDO.getId())) {
            return resultObj;
        }
        resultObj.setData(policyDO.getOrgCode());
        return resultObj;
    }

    @Operation(summary = "政策完成", description = "[author: ********]")
    @PostMapping("/complete")
    public JsonResultVo complete(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();

        if (BootAppUtil.isNullOrEmpty(policyDTO.getId())) {
            resultObj.setMsgErr(POLICY_ID_IS_NULL_ERR_MSG);
            return resultObj;
        }

        // if (StringUtils.isAnyBlank(policyDTO.getDbegin(),policyDTO.getDend())) {
        //     resultObj.setMsgErr("执行时间不能为空，必须维护");
        //     return resultObj;
        // }

        // 校验是否维护的红旗伙伴配置
        policyDealerService.validDataRationalityByPolicyId(policyDTO.getId());


        PolicyDO policyDO = policyService.getById(policyDTO.getId());
        if (null == policyDO) {
            resultObj.setMsgErr(POLICY_NOT_FOUND_ERR_MSG);
            return resultObj;
        }

        if (!PolicyStatusEnum.EDIT.getCode().equals(policyDO.getVfinishstate())) {
            resultObj.setMsgErr("该政策配置不是录入状态，不可完成！");
            return resultObj;
        }

        // 由计算方案完成改为->方案合并完成
        LambdaQueryWrapper<CombinationMainDO> lambdaWrapper = new QmQueryWrapper<CombinationMainDO>().lambda();
        lambdaWrapper.eq(CombinationMainDO::getPolicyId, policyDTO.getId());
        long combinationCount = combinationMainService.count(lambdaWrapper);
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);
        if (combinationCount > 0) {
            List<CombinationMainDO> combinationMainList = combinationMainService.getMainId(policyDTO.getId());
            for (CombinationMainDO combinationMainDO : combinationMainList) {
                List<String> fieldName = combinationDetailService.queryFieldNameByCombinationId(combinationMainDO.getId());
                boolean b1 = fieldName.contains("经销商代码");
                boolean b2 = fieldName.contains("系列");
                boolean b3 = fieldName.contains("VIN码");
                boolean b4 = fieldName.contains("返利金额");
                boolean b5 = fieldName.contains("含税市场指导价");
                boolean b6 = fieldName.contains("发票日期");
                boolean b7 = fieldName.contains("批售STD日期");
                boolean b8 = fieldName.contains("零售AAK日期");

                boolean b9 = fieldName.contains("车辆属性");
                boolean b10 = fieldName.contains("上报系统类型");
                boolean b11 = fieldName.contains("车辆大类");
                boolean b12 = fieldName.contains("含税返利金额");
                boolean b13 = fieldName.contains("不含税返利金额");
                boolean b14 = fieldName.contains("预估月份");

                boolean b15 = fieldName.contains("不含税金额");
                // boolean b16 = fieldName.contains("税率");

                if ("hq".equals(company)) {
                    if (StringUtils.equalsAny(policyDO.getBusinessType(), "00", "01", "02", "03")) {
                        if (!(b1 && b2 && b3 && b4)) {
                            resultObj.setMsgErr("要求必须字段：经销商代码，系列，VIN码，返利金额，请核对后提交！");
                            return resultObj;
                        }
                    } else {
                        if (!(b1 && b2 && b3 && b15 && b4)) {
                            resultObj.setMsgErr("要求必须字段：经销商代码，系列，VIN码，不含税金额,返利金额,请核对后提交！");
                            return resultObj;
                        }
                    }

                } else if ("bx".equals(company)) {
                    String skipFlag = policyDTO.getSkipFlag();
                    // 如果 skip 则不再判断必填字段
                    if (!"skip".equals(skipFlag)) {
                        if (policyDO.getCheckVinFlag() == YesOrNoEnum.YES) {
                            if (!(b1 && b3 && b5 && b6 && b7 && b8 && b9 && b10 && b11 && b12 && b13)) {
                                String fieldStr = "{}{}{}{}{}{}{}{}{}{}{}";
                                fieldStr = CharSequenceUtil.format(fieldStr, b1 ? "" : "经销商代码，", b3 ? "" : "VIN码，",
                                        b5 ? "" : "含税市场指导价，", b6 ? "" : "发票日期，", b7 ? "" : "批售STD日期，", b8 ? "" : "零售AAK日期，",
                                        b9 ? "" : "车辆属性，", b10 ? "" : "上报系统类型，", b11 ? "" : "车辆大类，", b12 ? "" : "含税返利金额，",
                                        b13 ? "" : "不含税返利金额，");
                                fieldStr = fieldStr.substring(0, fieldStr.length() - 1);
                                // 设置 skip 来判断是否要强制执行
                                resultObj.setData("skip");
                                resultObj.setMsg(CharSequenceUtil.format("目前合并方案中缺少{}等必须字段，是否需要强制提交？", fieldStr));
                                return resultObj;
                            }
                        } else {
                            if (!(b1 && b12 && b13)) {
                                String fieldStr = "{}{}{}";
                                fieldStr = CharSequenceUtil.format(fieldStr, b1 ? "" : "经销商代码，", b12 ? "" : "含税返利金额，",
                                        b13 ? "" : "不含税返利金额，");
                                fieldStr = fieldStr.substring(0, fieldStr.length() - 1);
                                // 设置 skip 来判断是否要强制执行
                                resultObj.setData("skip");
                                resultObj.setMsg(CharSequenceUtil.format("目前合并方案中缺少{}等必须字段，是否需要强制提交？", fieldStr));
                                return resultObj;
                            }
                        }
                    }
                }
            }

            policyDO.setVfinishstate(PolicyStatusEnum.COMPLETE_CONFIG.getCode());
            policyDO.setConfigCompletedOn(DateUtils.getSysdateTime());
            policyService.updateById(policyDO);

            resultObj.setMsg("该政策配置已完成");
            return resultObj;
        } else {
            resultObj.setMsgErr("该政策配置没有合并方案，无法完成！");
            return resultObj;
        }
    }

    @Operation(summary = "政策取消完成", description = "[author: ********]")
    @PostMapping("/cancelComplete")
    public JsonResultVo cancelComplete(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();
        if (BootAppUtil.isNullOrEmpty(policyDTO.getId())) {
            resultObj.setMsgErr(POLICY_ID_IS_NULL_ERR_MSG);
            return resultObj;
        }

        PolicyDO policyDO = policyService.getById(policyDTO.getId());
        if (null == policyDO) {
            resultObj.setMsgErr(POLICY_NOT_FOUND_ERR_MSG);
            return resultObj;
        }

        if (!PolicyStatusEnum.COMPLETE_CONFIG.getCode().equals(policyDO.getVfinishstate())) {
            resultObj.setMsgErr("该政策配置不是配置完成状态，不可取消完成！");
            return resultObj;
        }

        policyDO.setVfinishstate(PolicyStatusEnum.EDIT.getCode());
        policyDO.setConfigCompletedOn(null);
        policyService.updateById(policyDO);

        resultObj.setMsg("该政策配置已取消完成");
        return resultObj;
    }

    @Operation(summary = "政策已计算", description = "[author: ********]")
    @PostMapping("/calculated")
    public JsonResultVo calculated(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();
        if (BootAppUtil.isNullOrEmpty(policyDTO.getId())) {
            resultObj.setMsgErr(POLICY_ID_IS_NULL_ERR_MSG);
            return resultObj;
        }

        PolicyDO policyDO = policyService.getById(policyDTO.getId());
        if (null == policyDO) {
            resultObj.setMsgErr(POLICY_NOT_FOUND_ERR_MSG);
            return resultObj;
        }

        policyDO.setCalculateStatus(PolicyCalculateStatusEnum.CALCULATED);
        policyService.updateById(policyDO);
        resultObj.setMsg("该政策已计算");
        return resultObj;
    }

    @Operation(summary = "获取政策结构", description = "[author: ********]")
    @PostMapping("/structure")
    public JsonResultVo<List<PolicyStructureVO>> structure(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<List<PolicyStructureVO>> resultObj = new JsonResultVo<>();
        List<PolicyStructureVO> structure = policyService.getStructure(policyDTO);
        resultObj.setData(structure);
        return resultObj;
    }

    @Operation(summary = "返利计算器查询", description = "[author: ********]")
    @PostMapping("/CalculatorList")
    public JsonResultVo<QmPage<RebateCalculatorVO>> calculatorList(@RequestBody RebateCalculatorDTO rebateCalculatorDTO) {
        JsonResultVo<QmPage<RebateCalculatorVO>> resultObj = new JsonResultVo<>();
        LoginKeyDO loginKeyDO = getUserInfo();
        QmQueryWrapper<RebateCalculatorVO> queryWrapper = new QmQueryWrapper<>();
        TableUtils.appendTableAdditional(queryWrapper, rebateCalculatorDTO, RebateCalculatorVO.class);
        IPage<RebateCalculatorVO> queryPage = TableUtils.convertToIPage(rebateCalculatorDTO);
        IPage<RebateCalculatorVO> calculatorList = policyService.getCalculatorList(queryPage, loginKeyDO.getCompanyId(), rebateCalculatorDTO, queryWrapper);
        QmPage<RebateCalculatorVO> qmPage = TableUtils.convertQmPageFromMpPage(calculatorList);
        resultObj.setData(qmPage);
        return resultObj;
    }

    @Operation(summary = "工作流-更新政策状态", description = "[author: ********]")
    @PostMapping("/updateStatus")
    public JsonResultVo updateStatus(@RequestHeader("tenantId") String tenantId,
                                     @RequestBody FlowProcessResultDTO flowProcessResultDTO) {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();
        PolicyDO policyData = BeanUtil.toBean(flowProcessResultDTO.getExtraData(), PolicyDO.class);
        String policyId = policyData.getId();
        PolicyDO policyDO = policyService.getById(policyId);
        policyDO.setVfinishstate(flowProcessResultDTO.getStatusCode());
        policyService.updateById(policyDO);

        resultObj.setMsg("状态更新成功！");
        return resultObj;
    }

    @Operation(summary = "获取复制路线图数据", description = "[author: ********]")
    @PostMapping("/copyPathData")
    public JsonResultVo<CopyPathNodeDTO> getCopyPathData(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<CopyPathNodeDTO> resultObj = new JsonResultVo<>();
        CopyPathNodeDTO copyPathNodeDTO = policyCopyPathService.geneCopyPathData(policyDTO.getId());
        resultObj.setData(copyPathNodeDTO);
        return resultObj;
    }



    @Operation(summary = "发布和取消发布", description = "[author: ********]")
    @PostMapping("/updateStatusByButton")
    public JsonResultVo updateStatusByButton(@RequestBody PublishAuditDTO request) {

        String policyId = request.getPolicyDO().getId();
        PolicyDO policyDO = policyService.getById(policyId);
        if (policyDO == null) {
            throw new QmException("不存在该政策，id：" + policyId);
        }

        String vstatus = request.getVstatus();
        // vStatus="20"，前端目的在于提交，创建入账待办
        if ("20".equals(vstatus)) {
            return this.policyCommit(policyDO);
        }

        // vStatus="25"，前端在于取消提交，关闭入账待办
        if ("25".equals(vstatus)) {
            return this.policyCancelCommit(policyDO);
        }

        // vStatus="23"，前端目的在于取消发布,不关入账待办
        if ("23".equals(vstatus)) {
            return this.policyCancelPublish(policyDO);
        }
        // vStatus="30"，前端目的在于发布
        if ("30".equals(vstatus)) {
            return this.policyPublish(policyDO);
        }

        return new JsonResultVo<>();
    }

    @Operation(summary = "控制器接口", description = "[author: ********]")
    private JsonResultVo<Object> policyPublish(PolicyDO policyDO) {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();
        try {

            QmQueryWrapper<PolicyPublishConfigDO> queryWrapper = new QmQueryWrapper<>();
            queryWrapper.eq("POLICY_ID", policyDO.getId());
            List<PolicyPublishConfigDO> publishConfigDOS = policyPublishConfigService.list(queryWrapper);
            QmQueryWrapper<BusinessDataDO> wrapper = new QmQueryWrapper<>();
            wrapper.lambda()
                    .eq(BusinessDataDO::getField46, DateUtil.format(new Date(), "yyyyMM"))
                    .eq(BusinessDataDO::getTableName, "经销商档案");
            // 获取字典编码
            Map<String, String> codeMap = businessDataService
                    .list(wrapper)
                    .stream()
                    .collect(Collectors.toMap(BusinessDataDO::getField1
                            , BusinessDataDO::getField2
                            , (v1, v2) -> v2));
            List<Map<String, Object>> list = Lists.newArrayList();
            for (PolicyPublishConfigDO policyPublishConfigDO : publishConfigDOS) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("dealerCode", policyPublishConfigDO.getDealerCode());
                map.put("dealerName", codeMap.get(policyPublishConfigDO.getDealerCode()));
                String message = "厂家已发布政策“" + policyDO.getVpolicyname() + "”，请前往DMS系统，“财务管理-经销商返利-返利试算或返利查询”中查看详情。";
                map.put("message", message);
                list.add(map);
            }
            processService.sendRecord(list);
        } catch (Exception e) {
            log.error(" processService.sendRecord error message:{},stack:{}", e.getMessage(), e);
        }
        policyDO.setVfinishstate("30");
        policyDO.setPublishTime(DateUtil.toString(new Date()));
        policyDO.setSubmitCode(BootAppUtil.getLoginKey().getPersonCode());
        policyDO.setSubmitBy(BootAppUtil.getLoginKey().getOperatorId());
        policyMapper.updateById(policyDO);

        // 将政策存入返利试算器跑批的数据中
        PolicyDTO policyDTO = new PolicyDTO();
        policyDTO.setPolicyId(policyDO.getId());
        policyDTO.setVpolicyname(policyDO.getVpolicyname());
        rebateTriAsyncTask.saveTrailPolicyAsync(policyDTO);
        resultObj.setMsg("政策发布");
        return resultObj;
    }

    @Operation(summary = "控制器接口", description = "[author: ********]")
    private JsonResultVo<Object> policyCancelPublish(PolicyDO policyDO) {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();

        if (!PolicyStatusEnum.PUBLISH.getCode().equals(policyDO.getVfinishstate())) {
            throw new QmException("只有发布状态下的政策才可以取消发布");
        }

        // 入账中和已经完成的不允许取消发布
        List<ExecFormalCalcHistoryDO> applies = execFormalCalcHistoryMapper.selectApprovingEntryApply(policyDO.getId());

        if (CollUtil.isNotEmpty(applies)) {
            throw new QmException("存在审批中或已完成的入账，不允许取消发布");
        }

        policyDO.setVfinishstate(PolicyStatusEnum.SUBMIT.getCode());
        policyMapper.updateById(policyDO);
        // 将政策从返利试算器跑批的数据中删除
        PolicyDTO policyDTO = new PolicyDTO();
        policyDTO.setPolicyId(policyDO.getId());
        policyDTO.setVpolicyname(policyDO.getVpolicyname());
        rebateTriAsyncTask.deleteTrailPolicyAsync(policyDTO);

        resultObj.setMsg("政策已取消发布！");
        return resultObj;
    }

    @Operation(summary = "控制器接口", description = "[author: ********]")
    private JsonResultVo<Object> policyCancelCommit(PolicyDO policyDO) {
        JsonResultVo<Object> resultObj = new JsonResultVo<>();

        if (!PolicyStatusEnum.SUBMIT.getCode().equals(policyDO.getVfinishstate())) {
            throw new QmException("只有提交状态下的政策才可以取消提交");
        }
        policyDO.setVfinishstate(PolicyStatusEnum.COMPLETE_CONFIG.getCode());

        // PRD:此时如果存在对应的政策发布入账及审核任务，将此任务进行关闭处理；
        workbenchService.closeTask(FBJSJRZ.getCode(), policyDO.getTaskinsanceEndCode());
        policyDO.setTaskinsanceEndCode("");

        policyMapper.updateById(policyDO);
        resultObj.setMsg("取消提交成功");
        return resultObj;
    }


    @Operation(summary = "控制器接口", description = "[author: ********]")
    private JsonResultVo<Object> policyCommit(PolicyDO policyDO) {
        String submitCode = BootAppUtil.getLoginKey().getPersonCode();
        JsonResultVo<Object> resultObj = new JsonResultVo<>();

        // 校验政策是否维护了预算
        if (RebateTypeEnum.SPT_REBATE.getBizType().equals(policyDO.getBusinessType())) {
            //    校验备件不能分配预算
            BigDecimal budgetAmount = policyDO.getBudgetAmount();
            if (budgetAmount.compareTo(BigDecimal.ZERO) > 0) {
                throw new QmException("备件不能分配预算");
            }
            List<PolicyBudgetDO> policyBudgetDOS = policyBudgetMapper.selectListByPolicyId(policyDO.getId(), policyDO.getClassItemCode());
            if (CollUtil.isNotEmpty(policyBudgetDOS)) {
                throw new QmException("备件不能分配预算");
            }

        } else {
            String flag = policyBudgetService.commitCheck(policyDO);
            // "1"时前端会出现发起预算调整弹窗
            if ("1".equals(flag)) {
                resultObj.setData("1");
                return resultObj;
            }
        }


        if (!PolicyStatusEnum.COMPLETE_CONFIG.getCode().equals(policyDO.getVfinishstate())) {
            throw new QmException("请先确认配置已完成");
        }

        // 修改政策状态为提交-23
        policyDO.setVfinishstate(PolicyStatusEnum.SUBMIT.getCode());
        policyDO.setSubmitCode(getUserInfo().getPersonCode());
        policyDO.setSubmitBy(getUserInfo().getOperatorId());

        // taskinsanceEndCode有值表示创建多发布待办，再次提交不再创建
        if (StringUtils.isNotBlank(policyDO.getTaskinsanceCode()) && StringUtils.isBlank(policyDO.getTaskinsanceEndCode())) {
            TriggerInfoDTO triggerInfoDTO = TriggerInfoDTO.builder()
                    .userCode(submitCode)
                    .description("政策发布计算及入账-政策名称" + policyDO.getVpolicyname())
                    .build();
            List<TriggerInfoDTO> list = new ArrayList<>();
            list.add(triggerInfoDTO);
            try {
                WorkbenchResponseDTO workbenchResponseDTO = workbenchService.triggerAndCloseTask(policyDO.getTaskinsanceCode(), RebateCodeEnum.PZJSH.getCode(), RebateCodeEnum.FBJSJRZ.getCode(), list);
                TriggerTaskResponseDTO taskResponse = workbenchResponseDTO.getTriggerTaskResponse();
                List<TaskInstanceInfoResponseDTO> infoList = taskResponse.getTaskInstanceInfoList();
                if (!infoList.isEmpty()) {
                    policyDO.setTaskinsanceEndCode(infoList.get(0).getTaskInstanceCode());
                }
            } catch (Exception e) {
                log.error("关闭政策配置待办任务或开启发布审核待办任务失败error message:{}", e.getMessage(), e);
                throw new QmException("关闭政策配置待办任务或开启发布审核待办任务失败！");
            }

        }
        policyMapper.updateById(policyDO);


        resultObj.setMsg("政策处于提交状态，请到政策发布入账及审核页面或政策发布入账及审核待办进行发布！");
        return resultObj;
    }

    // 批量获取角色下面的 用户
    @Operation(summary = "批量获取角色下面的 用户", description = "[author: ********]")
    Single<Map<String, List<CloudNativeUserDTO>>> getLevelProcessAccount(List<String> codes) {
        return Flowable.fromIterable(codes)
                .parallel()
                .runOn(Schedulers.io())
                .flatMap(this::getLevelProcessAccountAction)
                .sequential()
                .toMap(key -> key.keySet().toArray()[0].toString(), value -> value.get(value.keySet().toArray()[0]));
    }

    @Operation(summary = "控制器接口", description = "[author: ********]")
    Flowable<Map<String, List<CloudNativeUserDTO>>> getLevelProcessAccountAction(String code) {
        return Flowable.unsafeCreate(sub -> {
            try {
                List<CloudNativeUserDTO> cloudNativeUserDTOS = platformUserService.getGetUserByRole(UserByRoleQueryDTO.builder().code(code).build());
                Map<String, List<CloudNativeUserDTO>> map = Maps.newHashMap();
                map.put(code, cloudNativeUserDTOS);
                sub.onNext(map);
                sub.onComplete();
            } catch (RuntimeException e) {
                sub.onError(new RuntimeException("获取" + code + "下的用户失败"));
            }
        });
    }

    @Operation(summary = "获取发布后的并且指定组织、指定销售目标的政策模型", description = "[author: ********]")
    @PostMapping("/getPolicyListByAim")
    public JsonResultVo<List<PolicyDO>> getPolicyListByAim(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<List<PolicyDO>> resultObj = new JsonResultVo<>();
        QmQueryWrapper<PolicyDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(PolicyDO::getVfinishstate, PolicyStatusEnum.PUBLISH.getCode())
                .eq(PolicyDO::getOrgCode, policyDTO.getOrgCode())
                .eq(PolicyDO::getAimVersion, policyDTO.getAimVersion());
        List<PolicyDO> data = policyService.list(wrapper);
        resultObj.setData(data);
        return resultObj;
    }

    @Operation(summary = "获取政策的方案合并对象", description = "[author: ********]")
    @PostMapping("/getPolicyCombination")
    public JsonResultVo<CombinationMainDO> getPolicyCombination(@RequestBody PolicyDTO policyDTO) {
        JsonResultVo<CombinationMainDO> resultObj = new JsonResultVo<>();
        QmQueryWrapper<CombinationMainDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<CombinationMainDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(CombinationMainDO::getPolicyId, policyDTO.getId());
        CombinationMainDO one = combinationMainService.getOne(wrapper);
        resultObj.setData(one);
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: ********]")
    @PostMapping("/dashBoardTable")
    public JsonResultVo<QmPage<PolicyDO>> dashBoardTable(@RequestBody PolicyDTO tempDTO) {
        // 定义查询构造器
        QmQueryWrapper<PolicyDO> queryWrapper = new QmQueryWrapper<>();
        // 拼装实体属性查询条件
        LambdaQueryWrapper<PolicyDO> lambdaWrapper = queryWrapper.lambda();

        LoginKeyDO userInfo = getUserInfo();

        lambdaWrapper.eq(PolicyDO::getCreateBy, userInfo.getOperatorId());

        // 起始截止日期
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getStartTime())) {
            // 操作日期

            lambdaWrapper.ge(PolicyDO::getCreateOn, tempDTO.getStartTime());
        }
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getEndTime())) {
            // 操作日期

            lambdaWrapper.le(PolicyDO::getCreateOn, tempDTO.getEndTime());

        }

        // 排序，按照创建日期（不要时分秒）倒序、政策名称正序
        queryWrapper.orderByDesc("date(createon)").orderByAsc("convert(vpolicyname using gbk)");

        // 查询数据，使用table函数。
        QmPage<PolicyDO> list = policyService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<PolicyDO>> ret = new JsonResultVo<>();

        ret.setData(list);
        return ret;
    }

    /**
     * 政策时间轴查询
     */
    // @Operation(summary = "政策节点跟踪查询", description = "[author: ********]")
    // @PostMapping("/timeAxisTable")
    // public JsonResultVo<List<TimeAxisVO>> timeAxisTable(@RequestBody PolicyDTO tempDTO) {
    //     JsonResultVo<List<TimeAxisVO>> ret = new JsonResultVo<>();
    //     ret.setData(policyService.gettimeAxisV3(tempDTO));
    //     return ret;
    // }


    /**
     * 红旗-保存时判断政策代码是否存在
     */
    @Operation(summary = "红旗-保存时判断政策代码是否存在", description = "[author: ********]")
    @PostMapping("/checkPolicyCode")
    public JsonResultVo<List<PolicyDO>> checkPolicyCode(@RequestBody PolicyDO tempDO) {
        JsonResultVo<List<PolicyDO>> ret = new JsonResultVo<>();
        QmQueryWrapper<PolicyDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyDO> wrapper = queryWrapper.lambda();
        if (!BootAppUtil.isNullOrEmpty(tempDO.getId())) {
            wrapper.ne(PolicyDO::getId, tempDO.getId());
        }
        wrapper.eq(PolicyDO::getVpolicycodehq, tempDO.getVpolicycodehq());
        List<PolicyDO> policyDOS = policyService.list(queryWrapper);
        ret.setData(policyDOS);
        return ret;
    }

    /**
     * 红旗-保存时判断政策代码是否存在
     */
    @Operation(summary = "红旗-查询政策列表", description = "[author: ********]")
    @PostMapping("/list")
    public JsonResultVo<List<PolicyDTO>> list(@RequestBody PolicyConditionDTO policyConditionDTO) {

        JsonResultVo<List<PolicyDTO>> ret = new JsonResultVo<>();

        List<PolicyDTO> list = policyService.getPolicyListByCondition(policyConditionDTO);

        ret.setData(list);

        return ret;
    }


    @Operation(summary = "红旗-查询政策列表", description = "[author: ********]")
    @PostMapping("/detailByProcess")
    public JsonResultVo<Map<String, Object>> detailByProcess(@RequestBody PolicyDO policyDO) {

        PolicyPublishedDO publishedDO = null;
        PolicyDO policyDO1 = null;
        CombinationMainDO combinationMainDO = null;
        JsonResultVo<Map<String, Object>> ret = new JsonResultVo<>();


        QmQueryWrapper<PolicyDO> queryWrapper = new QmQueryWrapper<>();
        String policyId = policyDO.getPolicyId();
        // 前端如果传了policyId就以id为主，否则以实例编码为准
        if (StringUtils.isNotBlank(policyId)) {
            queryWrapper.eq("ID", policyId);
        } else {
            queryWrapper.eq("taskFlowinstanceCode", policyDO.getTaskFlowinstanceCode());
            queryWrapper.and(it -> it.eq("taskinsanceCode", policyDO.getTaskinsanceCode()).or(od -> od.eq("taskinsanceEndCode", policyDO.getTaskinsanceCode())));
        }

        List<PolicyDO> list = policyService.list(queryWrapper);


        // 说明政策实体policy已经有数据了
        if (!list.isEmpty()) {
            policyDO1 = list.get(0);
            String pushId = policyDO1.getPublishedId();
            String actualAmount = policyService.getActualAmount(policyDO1.getPolicyId());
            if(StringUtils.isNotBlank(actualAmount) && !"0".equals(actualAmount)) {
                policyDO1.setActualAmount(new BigDecimal(actualAmount));
            }
            if (StringUtils.isNotBlank(pushId)) {
                if (pushId.contains("-")) {
                    publishedDO = policyPublishedService.getById(pushId.split("-")[0]);
                } else {
                    publishedDO = policyPublishedService.getById(pushId);

                }
            }
            QmQueryWrapper<CombinationMainDO> wrapper = new QmQueryWrapper<>();
            wrapper.eq("POLICYID", policyDO1.getId());
            List<CombinationMainDO> combinationMainDOS = combinationMainService.list(wrapper);
            if (!combinationMainDOS.isEmpty()) {
                combinationMainDO = combinationMainDOS.get(0);
            }

            boolean dateFlag = DateUtil.isBetweenDate(new Date(), publishedDO.getDbegin(), publishedDO.getDend());
            boolean entryFlag = policyFlagService.getEntryFlag(policyDO1.getId());
            if(dateFlag) {
                policyDO1.setIsCreate("1");
            } else {
                policyDO1.setIsCreate(entryFlag ? "0" : "1");
            }

        }

        // 查询政策配置专员信息
        QmQueryWrapper<PolicyAllocationInfoDO> wrapper = new QmQueryWrapper<>();
        wrapper.eq("TASKFLOWINSTANCECODE", policyDO.getTaskFlowinstanceCode());
        wrapper.eq("TASKINSTANCECODE", policyDO.getTaskinsanceCode());

        List<PolicyAllocationInfoDO> temp = policyAllocationService.list(wrapper);
        // 政策配置专员信息其实肯定存在(复制的政策除外)
        if (CollUtil.isNotEmpty(temp)) {
            PolicyAllocationInfoDO infoDO = temp.get(0);
            publishedDO = policyPublishedService.getById(infoDO.getVpolicyid());
            // 需要将政策维护信息下政策担当具体选择的返利项目代码带过来
            publishedDO.setClassItem(infoDO.getClassItem());
            publishedDO.setClassItemName(infoDO.getClassItemName());
            publishedDO.setBudgetType(infoDO.getBudgetType());
            ClassItemDO classItemDO = classItemMapper.selectByClassItem(publishedDO.getClassItem());
            publishedDO.setBusinessType(RebateTypeEnum.getBizType(classItemDO.getRebateType()));
        }

        // 判断该任务创建的配置待办是否是从政策审计发起的
        if(publishedDO==null){
            LambdaQueryWrapper<PolicyAuditPO> lambdaWrapper = new LambdaQueryWrapper<>();
            lambdaWrapper.eq(StringUtils.isNoneBlank(policyDO.getTaskinsanceCode()), PolicyAuditPO::getTaskInstanceCode, policyDO.getTaskinsanceCode());
            PolicyAuditPO one = policyAuditService.getOne(lambdaWrapper);
            if(one!=null){
                publishedDO = policyPublishedMapper.selectOneByPolicyCode(one.getPolicyCode());
            }
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("policyPublished", publishedDO);
        map.put("policy", policyDO1);
        map.put("combinationMain", combinationMainDO);
        ret.setData(map);
        return ret;
    }

    @Operation(summary = "返利查询用例查询", description = "[author: ********]")
    @PostMapping("/rebateQuery")
    public JsonResultVo<QmPage<RebateQueryVO>> rebateQuery(@RequestBody PolicyConditionDTO policyConditionDTO) {

        JsonResultVo<QmPage<RebateQueryVO>> ret = new JsonResultVo<>();

        QmPage<RebateQueryVO> list = policyService.rebateQuery(policyConditionDTO);

        ret.setData(list);

        return ret;
    }

    @Operation(summary = "全量政策查询", description = "[author: ********]")
    @PostMapping("/listAllPolicies")
    public JsonResultVo<List<PolicyDO>> listAllPolicies() {

        JsonResultVo<List<PolicyDO>> ret = new JsonResultVo<>();

        List<PolicyDO> list = policyMapper.listAllPolicies();

        ret.setData(list);

        return ret;
    }

    /**
     * 政策时间轴查询
     */
    @Operation(summary = "政策节点跟踪查询", description = "[author: ********]")
    @PostMapping("/timeAxisTableNew")
    public JsonResultVo<QmPage<TimeAxisBoardDTO>> timeAxisTableNew(@RequestBody TimeAxisBoardVO boardVO) {
        JsonResultVo<QmPage<TimeAxisBoardDTO>> ret = new JsonResultVo<>();
        QmQueryWrapper<TimeAxisBoardDTO> queryWrapper = new QmQueryWrapper<>();
        TableUtils.appendTableAdditional(queryWrapper, boardVO, TimeAxisBoardDTO.class);
        IPage<TimeAxisBoardDTO> queryPage = TableUtils.convertToIPage(boardVO);
        IPage<TimeAxisBoardDTO> boardList = policyService.gettimeAxisV4(queryPage, boardVO, queryWrapper);
        QmPage<TimeAxisBoardDTO> qmPage = TableUtils.convertQmPageFromMpPage(boardList);
        ret.setData(qmPage);
        return ret;
    }

    @Operation(summary = "返利计算器-查询政策列表", description = "[author: ********]")
    @PostMapping("/queryPolicyList")
    public List<RebateQueryResponse> queryPolicyList(@RequestBody RebateQueryListRequest request) {
        return policyMapper.queryPolicyList(request);
    }





}
