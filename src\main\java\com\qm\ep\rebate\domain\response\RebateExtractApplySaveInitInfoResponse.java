package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * 返利折让申请单与处理单合并表
 *
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Schema(description = "返利折让申请单与处理单合并表  ")
@Getter
@Setter
@Builder
public class RebateExtractApplySaveInitInfoResponse implements Serializable {


    /**
     * 重点关注店(0-不是，1-是)
     */
    @Schema(description = "重点关注店(0-不是，1-是)")
    private String canFocusStore;

    /**
     * 上月STD达成率
     */
    @Schema(description = "上月STD达成率")
    private BigDecimal stdAchievementRate;


    /**
     * 申请时返利账户可用金额（万元）
     */
    @Schema(description = "申请时返利账户可用金额（万元）")
    private BigDecimal availableRebateAccount;

}
