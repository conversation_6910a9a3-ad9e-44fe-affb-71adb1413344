package com.qm.ep.rebate.domain.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(description = "数据:实体类-表结构做")
@Data
public class TableStructureDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-字段名称")
    private String fieldName;

    @Schema(description = "数据-字段类型")
    private String fieldType;

    @Schema(description = "数据-相关性字段名称")
    private String relevanceFieldName;

}
