package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-04-10 09:36
 */
@Schema(description = "用户信息查询 DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryUserInfoDTO {

    /**
     * 登录名
     */
    @Schema(description = "查询用户的域账号")
    private String loginName;

    /**
     * 员工HrId
     */
    @Schema(description = "员工HrId")
    private String code;

    /**
     * 员工idmId
     */
    @Schema(description = "员工idmId")
    private String idmid;


}
