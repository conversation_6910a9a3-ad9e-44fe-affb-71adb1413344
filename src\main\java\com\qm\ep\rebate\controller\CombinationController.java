package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.CombinationDetailDO;
import com.qm.ep.rebate.domain.bean.CombinationMainDO;
import com.qm.ep.rebate.domain.bean.PlanMainDO;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.dto.CombinationMainDTO;
import com.qm.ep.rebate.domain.dto.PlanMainDTO;
import com.qm.ep.rebate.domain.dto.ValidationResultDTO;
import com.qm.ep.rebate.domain.dto.structure.CombinationDTO;
import com.qm.ep.rebate.mapper.CombinationMainMapper;
import com.qm.ep.rebate.remote.feign.RebateDataFeignClient;
import com.qm.ep.rebate.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/combination")
@Tag(name = "计算方案合并", description = "[author: 10200571]")
public class CombinationController extends BaseController {

    @Resource
    private CombinationService combinationService;

    @Resource
    private CombinationMainService combinationMainService;

    @Resource
    private CombinationDetailService combinationDetailService;

    @Resource
    private CombinationMainMapper combinationMainMapper;
    @Resource
    private PlanMainService planMainService;


    @Resource
    private PolicyService policyService;

    @Autowired
    private RebateDataFeignClient rebateDataFeignClient;

    @Operation(summary = "查询合并方案列表", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<CombinationMainDO>> table(@RequestBody CombinationMainDTO mainDTO){
        JsonResultVo<QmPage<CombinationMainDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<CombinationMainDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<CombinationMainDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(CombinationMainDO::getPolicyId, mainDTO.getPolicyId());

        QmPage<CombinationMainDO> data = combinationMainService.table(queryWrapper, mainDTO);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "查询合并方案详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<CombinationDTO> detail(@RequestBody CombinationDTO combinationDTO){
        JsonResultVo<CombinationDTO> jsonResultVo = new JsonResultVo<>();

        if(BootAppUtil.isNullOrEmpty(combinationDTO) || BootAppUtil.isNullOrEmpty(combinationDTO.getMain()) || BootAppUtil.isNullOrEmpty(combinationDTO.getMain().getId())) {
            jsonResultVo.setMsgErr("未找到计算方案主键");
            return jsonResultVo;
        }

        CombinationMainDO mainDO = combinationMainService.getById(combinationDTO.getMain().getId());

        QmQueryWrapper<CombinationDetailDO> queryDetailWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<CombinationDetailDO> lambdaDetailWrapper = queryDetailWrapper.lambda();
        lambdaDetailWrapper.eq(CombinationDetailDO::getCombinationId, combinationDTO.getMain().getId());
        List<CombinationDetailDO> details = combinationDetailService.list(lambdaDetailWrapper);
        
        CombinationDTO data = new CombinationDTO();
        data.setMain(mainDO);
        data.setDetails(details);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    /**
     * 获取输出字段下拉选
     */
    @Operation(summary = "获取输出字段", description = "[author: 10200571]")
    @PostMapping("/getOutPutFields")
    public JsonResultVo getOutPutFields(@RequestBody PlanMainDTO planDTO){
        JsonResultVo<Object> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<PlanMainDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PlanMainDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(PlanMainDO::getPolicyId, planDTO.getPolicyId());

        QmPage<PlanMainDO> data = planMainService.table(queryWrapper, planDTO);
        data.getItems().forEach(item->{
            planDTO.setPlanName(item.getPlanName());
            item.setOutPutFields(combinationMainMapper.getOutPutFields(planDTO));
        }
        );
        jsonResultVo.setData(data);
        return jsonResultVo;
    }
    /**
     * 保存计算方案
     * @param combinationDTO 计算方案配置数据
     * @return 返回计算方案主键
     */
    @Operation(summary = "保存计算方案", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<String> save(@RequestBody CombinationDTO combinationDTO) {
        JsonResultVo<String> res = new JsonResultVo<>();
        ValidationResultDTO validationResult = combinationService.validateData(combinationDTO);
        if(!validationResult.getOk()) {
            res.setMsgErr(validationResult.getMsg());
            return res;
        }

        validationResult = combinationService.validateName(combinationDTO);
        if(!validationResult.getOk()) {
            res.setMsgErr(validationResult.getMsg());
            return res;
        }

        // 校验计算方案的SQL是否正确
        validationResult = combinationService.validateConfig(combinationDTO);
        if(!validationResult.getOk()) {
            res.setMsgErr(validationResult.getMsg());
            res.setData(String.valueOf(validationResult.getData()));
            return res;
        }

        // 有主键，更新内容
        if(BootAppUtil.isnotNullOrEmpty(combinationDTO.getMain().getId())) {
            combinationService.deleteDetailByIds(Collections.singletonList(combinationDTO.getMain().getId()));
        }

        String id = combinationService.save(combinationDTO);
        res.setData(id);
        return res;

    }

    /**
     * 删除计算方案记录
     * @param deleteIds 计算方案ID列表
     * @return 返回
     */
    @Operation(summary = "删除合并方案记录", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo deleteByIds(@RequestBody List<String> deleteIds) {
        JsonResultVo<Object> ret = new JsonResultVo<>();
        //判断是否转换底表
        //取方案数据
        for(String ids:deleteIds) {
            QmQueryWrapper<CombinationMainDO> wrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<CombinationMainDO> lambdaQueryWrapper = wrapper.lambda();
            lambdaQueryWrapper.eq(CombinationMainDO::getId,ids);
            List<CombinationMainDO> combinationMain = combinationMainService.list(lambdaQueryWrapper);

            //取政策名称
            for(CombinationMainDO mainDO:combinationMain){
                QmQueryWrapper<PolicyDO> policyWrapper = new QmQueryWrapper<>();
                LambdaQueryWrapper<PolicyDO> policyLambdaQueryWrapper = policyWrapper.lambda();
                policyLambdaQueryWrapper.eq(PolicyDO::getId,mainDO.getPolicyId());
                List<PolicyDO> policy = policyService.list(policyWrapper);
                //校验是否存在
                for(PolicyDO policyDO:policy){
                    //传入参数，objectid和policyId判断历史表中，是否在转换中（状态为3），转换中不可删除
                    Map<String,Object> objectMap=new HashMap<>();
                    objectMap.put("policyId",policyDO.getId());
                    objectMap.put("objectId",ids);
                    JsonResultVo<Object> resultObj = rebateDataFeignClient.selectHistory(objectMap);
                    if(resultObj.isOk()){
                        Map<String, Object> data = (Map<String, Object>)resultObj.getData();
                        boolean isOK = Boolean.parseBoolean(data.get("isOk").toString());
                        if(!isOK) {
                            ret.setMsgErr(data.get("msg").toString());
                            return ret;
                        }
                    } else {
                        ret.setMsgErr(resultObj.getMsg());
                        return ret;
                    }
                    if(deleteIds.isEmpty()) {
                        ret.setMsgErr("未选择删除记录！");
                    } else {
                        boolean isOk = combinationService.deleteByIds(deleteIds);
                        if(isOk) {
                            ret.setMsg("删除成功");
                        } else {
                            ret.setMsgErr("删除失败");
                        }
                    }
                }

            }
        }
        return ret;
    }

    /**
     * 判断考核对象是否发生变更
     * @param combinationDTO 计算方案ID
     * @return 返回
     */
    @Operation(summary = "判断考核对象是否发生变更", description = "[author: 10200571]")
    @PostMapping("/getChange")
    public JsonResultVo getChange(@RequestBody CombinationDTO combinationDTO){
        JsonResultVo<Object> res = new JsonResultVo<>();
        CombinationMainDO mainDO = combinationMainService.getById(combinationDTO.getMain().getId());
        JSONArray calcObjects  = JSON.parseArray(mainDO.getCalcObjects());
        List<String> calcList = calcObjects.toJavaList(String.class);
        List<String> tableNames = new ArrayList<>();
        calcList.forEach(item->{
            JSONObject object = JSON.parseObject(item);
            String  objectName = object.getString("objectName");
            JSONArray savedFields = object.getJSONArray("fields");
            List<String> actualFields  = combinationMainService.getDataColumn(mainDO.getPolicyId(), objectName);
            if(savedFields.size() != actualFields.size()){
                tableNames.add(objectName);
            }else{
                savedFields.forEach(field->{
                    if(!actualFields.contains(field.toString()) && !tableNames.contains(objectName)){
                        tableNames.add(objectName);
                    }
                });
            }
        });
        if(CollUtil.isNotEmpty(tableNames)){
            res.setData(tableNames.toString());
        }
    return res;
    }


}
