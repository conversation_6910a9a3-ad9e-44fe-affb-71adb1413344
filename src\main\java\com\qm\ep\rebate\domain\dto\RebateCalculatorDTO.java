package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.domain.vo.DealerSeriesVO;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 返利计算器
 * <AUTHOR>
 */
@Data
@Schema(description = "返利计算器")
public class RebateCalculatorDTO extends JsonParamDto {
    @Schema(description = "数据-日期类型")
    private String datetype;
    @Schema(description = "数据-创建开始时间")
    private String dbegin;
    @Schema(description = "数据-创建结束时间")
    private String dend;
    @Schema(description = "数据-政策名称")
    private String vpolicyname;
    @Schema(description = "数据-政策id")
    private String id;
    @Schema(description = "数据-计算历史id")
    private String historyId;
    @Schema(description = "数据-经销商代码")
    private String dealerCode;

    @Schema(description = "数据-前端传递List")
    private List<DealerSeriesVO>  dealerSeriesList;

}
