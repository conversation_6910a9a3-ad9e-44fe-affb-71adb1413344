package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.qm.ep.rebate.domain.bean.CalcObjectSourceDO;
import com.qm.ep.rebate.domain.bean.PlanMainDO;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.dto.PlanMainDTO;
import com.qm.ep.rebate.domain.dto.structure.PlanDTO;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.SourceTypeEnum;
import com.qm.ep.rebate.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/plan")
@Tag(name = "计算方案")
public class PlanController extends BaseController {

    @Resource
    private PlanService planService;

    @Resource
    private PlanMainService planMainService;

    @Resource
    private PlanDetailService planDetailService;

    @Resource
    private PlanConditionsService planConditionsService;

    @Resource
    private PolicyService policyService;
    @Resource
    private CalcObjectSourceService calcObjectSourceService;
    @Resource
    private FormulaService formulaService;


    @Operation(summary = "查询计算方案列表", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<PlanMainDO>> table(@RequestBody PlanMainDTO planMainDTO) {
        JsonResultVo<QmPage<PlanMainDO>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(planMainService.tableList(planMainDTO));
        return jsonResultVo;
    }

    @Operation(summary = "获取详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<PlanDTO> detail(@RequestBody PlanDTO planDTO) {
        JsonResultVo<PlanDTO> jsonResultVo = new JsonResultVo<>();
        if (BootAppUtil.isNullOrEmpty(planDTO) || BootAppUtil.isNullOrEmpty(planDTO.getMain()) || BootAppUtil.isNullOrEmpty(planDTO.getMain().getId())) {
            jsonResultVo.setMsgErr("未找到计算方案主键");
            return jsonResultVo;
        }
        jsonResultVo.setData(planMainService.detail(planDTO));
        return jsonResultVo;
    }

    /**
     * 保存计算方案
     *
     * @param planDTO 计算方案配置数据
     * @return 返回计算方案主键
     */
    @Operation(summary = "保存计算方案", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<String> save(@RequestBody PlanDTO planDTO) {
        JsonResultVo<String> res = new JsonResultVo<>();
        res.setData(planService.savePlan(planDTO));
        return res;

    }

    @Operation(summary = "复制计算方案", description = "[author: 10200571]")
    @PostMapping("/copy")
    public JsonResultVo copy(@RequestBody PlanMainDTO planMainDTO) {
        JsonResultVo<Object> res = new JsonResultVo<>();
        if (planMainDTO == null || CharSequenceUtil.isEmpty(planMainDTO.getId())
                || CharSequenceUtil.isEmpty(planMainDTO.getPolicyId()) || CharSequenceUtil.isEmpty(planMainDTO.getPlanName())) {
            throw new QmException("复制操作失败！");
        }
        planService.copyPlan(planMainDTO);
        return res;
    }

    /**
     * 删除计算方案记录
     *
     * @param deleteIds 计算方案ID列表
     * @return 返回
     */
    @Operation(summary = "删除计算方案记录", description = "[author: 10200571]")
    @PostMapping("/deleteByIds")
    public JsonResultVo deleteByIds(@RequestBody List<String> deleteIds) {
        return planService.deletePlanByIds(deleteIds);
    }

    /**
     * 判断考核对象是否发生变更
     *
     * @param planMainDO 计算方案ID
     * @return 返回
     */
    @Operation(summary = "判断考核对象是否发生变更", description = "[author: 10200571]")
    @PostMapping("/getChange")
    public JsonResultVo getChange(@RequestBody PlanMainDO planMainDO) {
        JsonResultVo<Object> res = new JsonResultVo<>();
        Map<String, Set<String>> tableWithFieldMap = planMainService.getChangedDataSourceName(planMainDO.getId());
        if (CollUtil.isNotEmpty(tableWithFieldMap)) {
            res.setData(tableWithFieldMap);
        }
        return res;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @PostMapping("/turnDataSource")
    public JsonResultVo turnDataSource() {
        JsonResultVo<Object> res = new JsonResultVo<>();
        List<PlanMainDO> mainDOList = planMainService.list();
        mainDOList.forEach(main -> {
            String policyId = main.getPolicyId();
            PolicyDO policyDO = policyService.getById(policyId);
            String policyName = policyDO == null ? "" : policyDO.getVpolicyname();
            String planId = main.getId();

            String calcObjects = main.getCalcObjects();
            if (StringUtils.isNotEmpty(calcObjects) && calcObjects.contains("[")) {
                List<CalcObjectSourceDO> sourceDOList = JSON.parseArray(calcObjects, CalcObjectSourceDO.class);
                if (CollUtil.isNotEmpty(sourceDOList)) {
                    sourceDOList.forEach(source -> {
                        source.setForPolicyId(policyId);
                        source.setForObjectId(planId);
                        source.setForType(CalcObjectTypeEnum.PLAN);
                        source.setForSourceType(SourceTypeEnum.MAIN);
                        if (!CalcObjectTypeEnum.BASIC.equals(source.getObjectType())) {
                            source.setPolicyId(policyId);
                            source.setPolicyName(policyName);
                        }
                    });
                    calcObjectSourceService.saveBatch(sourceDOList);
                    List<String> idList = sourceDOList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                    main.setCalcObjects(CharSequenceUtil.join(",", idList));
                } else {
                    main.setCalcObjects("");
                }
            }

            String payObjects = main.getPayObjects();
            if (StringUtils.isNotEmpty(payObjects) && payObjects.contains("[")) {
                List<CalcObjectSourceDO> sourceDOList = JSON.parseArray(payObjects, CalcObjectSourceDO.class);
                if (CollUtil.isNotEmpty(sourceDOList)) {
                    sourceDOList.forEach(source -> {
                        source.setForPolicyId(policyId);
                        source.setForObjectId(planId);
                        source.setForType(CalcObjectTypeEnum.PLAN);
                        source.setForSourceType(SourceTypeEnum.PAY);
                        if (!CalcObjectTypeEnum.BASIC.equals(source.getObjectType())) {
                            source.setPolicyId(policyId);
                            source.setPolicyName(policyName);
                        }
                    });
                    calcObjectSourceService.saveBatch(sourceDOList);
                    List<String> idList = sourceDOList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                    main.setPayObjects(CharSequenceUtil.join(",", idList));
                } else {
                    main.setPayObjects("");
                }
            }

            String limitObjects = main.getLimitObjects();
            if (StringUtils.isNotEmpty(limitObjects) && limitObjects.contains("[")) {
                List<CalcObjectSourceDO> sourceDOList = JSON.parseArray(limitObjects, CalcObjectSourceDO.class);
                if (CollUtil.isNotEmpty(sourceDOList)) {
                    sourceDOList.forEach(source -> {
                        source.setForPolicyId(policyId);
                        source.setForObjectId(planId);
                        source.setForType(CalcObjectTypeEnum.PLAN);
                        source.setForSourceType(SourceTypeEnum.LIMIT);
                        if (!CalcObjectTypeEnum.BASIC.equals(source.getObjectType())) {
                            source.setPolicyId(policyId);
                            source.setPolicyName(policyName);
                        }
                    });
                    calcObjectSourceService.saveBatch(sourceDOList);
                    List<String> idList = sourceDOList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                    main.setLimitObjects(CharSequenceUtil.join(",", idList));
                } else {
                    main.setLimitObjects("");
                }
            }

            main.setDtstamp(DateUtils.getSysdateTime());
            planMainService.updateById(main);
        });
        return res;
    }

    /*@Operation("迁移公式内容数据")
    @PostMapping("/migrateFormulaData")
    public void migrateFormulaData(){
        List<PlanMainDO> planMainDOS = planMainService.list();
        planMainDOS.forEach(p->{
            FormulaDO formulaDO = new FormulaDO();
            formulaDO.setPolicyId(p.getPolicyId());
            formulaDO.setObjectId(p.getId());
            formulaDO.setObjectType(CalcObjectTypeEnum.PLAN);

            QmQueryWrapper<PlanDetailDO> planDetailWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<PlanDetailDO> planDetailLambdaQueryWrapper = planDetailWrapper.lambda();
            planDetailLambdaQueryWrapper.eq(PlanDetailDO::getPlanId, p.getId()).eq(PlanDetailDO::getType, "默认字段");
            PlanDetailDO detailDO = planDetailService.getOne(planDetailLambdaQueryWrapper);
            if(detailDO==null) {
                formulaDO.setName(p.getPlanName());
            } else {
                formulaDO.setName(detailDO.getAliasName());
            }
            CommonFormulaDTO formulaContent = JSONUtils.packingDOFromJsonStr(p.getFormulaContent(), CommonFormulaDTO.class);
            formulaDO.setDecimal(formulaContent.getDecimal());
            formulaDO.setRule(formulaContent.getRules());
            formulaDO.setFetchType(formulaContent.getValueType());
            formulaDO.setCreateOn(p.getCreateOn());
            formulaDO.setCreateBy(p.getCreateBy());
            formulaDO.setUpdateOn(p.getUpdateOn());
            formulaDO.setUpdateBy(p.getUpdateBy());
            formulaDO.setDtstamp(p.getDtstamp());
            formulaService.save(formulaDO);

            formulaContent.getFormula().forEach(i->{
                FormulaItemDO formulaItemDO = new FormulaItemDO();
                formulaItemDO.setFormulaId(formulaDO.getId());
                formulaItemDO.setValue(i.getValue());
                formulaItemDO.setType(i.getType());
                formulaItemDO.setTableName(i.getTableName());
                formulaItemDO.setTableField(i.getTableField());
                formulaItemDO.setRule(i.getRule());
                formulaItemService.save(formulaItemDO);
            });
        });

    }*/

}
