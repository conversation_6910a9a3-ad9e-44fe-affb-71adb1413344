package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-前提细节 DTO")
@Data
public class PremiseDetailDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-前提 ID")
    private String premiseId;

    @Schema(description = "数据-表 ID")
    private String tableId;

    @Schema(description = "数据-字段名称")
    private String fieldName;

    @Schema(description = "数据-类型")
    private String type;
}