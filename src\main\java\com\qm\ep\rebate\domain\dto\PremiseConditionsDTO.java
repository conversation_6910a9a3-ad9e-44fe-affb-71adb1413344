package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-前提条件 DTO")
@Data
public class PremiseConditionsDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-前提 ID")
    private String premiseId;
    @Schema(description = "数据-字段名称")
    private String fieldName;
    @Schema(description = "数据-Cal 型")
    private String calType;
    @Schema(description = "数据-骗局类型")
    private String conType;
    @Schema(description = "数据-滤液")
    private String filtrate;
    @Schema(description = "数据-滤液类型")
    private String filtrateType;
    @Schema(description = "数据-括号后")
    private String bracketAfter;
    @Schema(description = "数据-括号前")
    private String bracketBefore;
    @Schema(description = "数据-滤液来自")
    private String filtrateFrom;
    @Schema(description = "数据-字段名称来自")
    private String fieldNameFrom;
    @Schema(description = "数据-排")
    private String rank;
}