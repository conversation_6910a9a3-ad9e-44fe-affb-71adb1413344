package com.qm.ep.rebate.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "数据:实体类-返佣经销商信息")
@NoArgsConstructor
@Data
public class RebateDealerInfo {
    @Schema(description = "数据-经销商代码")
    @JsonProperty("dealerCode")
    private String dealerCode;
    @Schema(description = "数据-经销商名称")
    @JsonProperty("dealerName")
    private String dealerName;
    @Schema(description = "数据-大区域代码")
    @JsonProperty("bigRegionCode")
    private String bigRegionCode;
    @Schema(description = "数据-大区域名称")
    @JsonProperty("bigRegionName")
    private String bigRegionName;
}
