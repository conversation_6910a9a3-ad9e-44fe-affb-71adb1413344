
package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * iwork分页查询平台级用户实体
 *
 */
@Schema(description = "iwork分页查询平台级用户实体")
@Data
public class IworkCenterUserDTO {

    @Schema(description = "登录账号")
    private String loginName;

    @Schema(description = "用户id")
    private String id;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "用户姓名")
    private String name;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "用户编码", example = "10027706")
    private String code;

    @Schema(description = "组织信息")
    private List<IworkCenterDeptDTO> orgVOList;

    @Schema(description = "角色信息")
    private List<IworkRoleEntity> roleList;

    @Schema(description = "人员idmId")
    private String idmid;
    @Schema(description = "部门名称")
    private String departName;
}
