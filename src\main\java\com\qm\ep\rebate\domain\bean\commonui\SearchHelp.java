package com.qm.ep.rebate.domain.bean.commonui;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "数据:搜索帮助")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sresearchhelp")
public class SearchHelp implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据DTSTAMP")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Date dtstamp;

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 微服务（数据源）
     */
    @Schema(description = "微服务（数据源）")
    @TableField("MICROSERVICE")
    private String microService;

    /**
     * 值属性
     */
    @Schema(description = "值属性")
    @TableField("VALUEPROPERTYNAME")
    private String valuePropertyName;

    /**
     * 显示属性
     */
    @Schema(description = "显示属性")
    @TableField("DISPLAYPROPERTYNAME")
    private String displayPropertyName;

    /**
     * 描述属性
     */
    @Schema(description = "描述属性")
    @TableField("DESCRIPTIONPROPERTY")
    private String descriptionProperty;

    /**
     * 字段信息
     */
    @Schema(description = "字段信息")
    @TableField("COLINFO")
    private String colInfo;

    /**
     * 查询条件
     */
    @Schema(description = "查询条件")
    @TableField("PARAMNAMES")
    private String paramNames;

    /**
     * 固定条件
     */
    @Schema(description = "固定条件")
    @TableField("FIXEDCONDITIONS")
    private String fixedConditions;

    /**
     * 表格列名称
     */
    @Schema(description = "表格列名称")
    @TableField("COLNAMES")
    private String colNames;

    /**
     * 查询语句
     */
    @Schema(description = "查询语句")
    @TableField("SHSQL")
    private String shSQL;
    /**
     * 搜索帮助描述
     */
    @Schema(description = "搜索帮助描述")
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 搜索帮助名称
     */
    @Schema(description = "搜索帮助名称")
    @TableField("NAME")
    private String name;

    /**
     * 是否进行行转列 0不进行  1进行
     */
    @Schema(description = "是否进行行转列 0不进行  1进行")
    @TableField("status")
    private String status;

    @Schema(description = "页眉")
    @TableField("header")
    private String header;


    @Schema(description = "组名称")
    @TableField("groupName")
    private String groupName;

    @Schema(description = "计数名称")
    @TableField("countName")
    private String countName;

    /**
     * 分页类型  0后端分页 1前端分页
     */
    @Schema(description = "分页类型  0后端分页 1前端分页")
    @TableField("pageTye")
    private String pageTye;

    @Schema(description = "函数名称")
    @TableField("functionName")
    private String functionName;
}
