package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "数据:董事会政策获取回应")
@Data
public class BoardPolicyAcquireResponse {


    /**
     * 政策名称
     */
    @Schema(description = "政策名称")
    private String policyName;

    /**
     * 创建人部门
     */
    @Schema(description = "创建人部门")
    private String creatorPart;

    /**
     * 政策创建人
     */
    @Schema(description = "政策创建人")
    private String creator;

    /**
     * 政策获取率
     */
    @Schema(description = "政策获取率")
    private BigDecimal acquireRate;

    /**
     * 政策获取经销商数
     */
    @Schema(description = "政策获取经销商数")
    private int up;

    /**
     * 政策覆盖经销商数
     */
    @Schema(description = "政策覆盖经销商数")
    private int down;

}
