package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * 车系销量预测表
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Data
@Schema(description = "数据:车系销量预测表")
public class BgtSeriesSaleResponse implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-年度")
    private String saleYear;

    @Schema(description = "数据-季度")
    private String saleQuarter;

    @Schema(description = "数据-销售类型（00-aak，01-std")
    private String saleType;

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-销量（aak/std）")
    private BigDecimal saleCount;

    @Schema(description = "数据-点数")
    private BigDecimal salePoint;

    @Schema(description = "数据-金额")
    private BigDecimal saleAmount;

    @Schema(description = "数据-销量（aak/std）")
    private BigDecimal avgSaleCount;

    @Schema(description = "数据-金额")
    private BigDecimal avgSaleAmount;

    @Schema(description = "数据-销量（aak/std）")
    private BigDecimal tmpSaleCount;

    @Schema(description = "数据-金额")
    private BigDecimal tmpAveragePrice;

    @Schema(description = "数据-查询范围的收入")
    private BigDecimal quarterSaleAmount;

}