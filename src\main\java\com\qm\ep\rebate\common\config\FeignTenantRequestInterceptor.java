package com.qm.ep.rebate.common.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class FeignTenantRequestInterceptor implements RequestInterceptor {
    private static final Logger log = LoggerFactory.getLogger(FeignTenantRequestInterceptor.class);
    public FeignTenantRequestInterceptor() {
    }
    /**
     * 增加请求头租户信息。
     * @param requestTemplate
     */
    public void apply(RequestTemplate requestTemplate) {
        log.info("execute method apply");
        String tenantId="tenantId";
        String tenantIdValue="15";
        if(!requestTemplate.headers().containsKey(tenantId)) {
            requestTemplate.header(tenantId, tenantIdValue);
            log.info("add header {}:{}",tenantId,tenantIdValue);
        }
    }
}
