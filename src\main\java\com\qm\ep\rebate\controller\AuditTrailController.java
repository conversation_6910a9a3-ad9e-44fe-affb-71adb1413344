package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.AuditTrailDO;
import com.qm.ep.rebate.domain.dto.AuditTrailDTO;
import com.qm.ep.rebate.domain.response.AuditTrailResponse;
import com.qm.ep.rebate.service.AuditTrailService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auditTrail")
@Tag(name = "审核轨迹", description = "[author: 10200571]")
public class AuditTrailController extends BaseController {

    @Resource
    private AuditTrailService auditTrailService;

    @Operation(summary = "查询计算方案列表", description = "[author: 10200571]")
    @PostMapping("/list")
    public JsonResultVo<List<AuditTrailDO>> list(@RequestBody AuditTrailDTO auditTrailDTO){
        JsonResultVo<List<AuditTrailDO>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(auditTrailService.listAuditTrail(auditTrailDTO));
        return jsonResultVo;
    }

    @Operation(summary = "获取每级审核最后的时间", description = "[author: 10200571]")
    @PostMapping("/getLastAuditTime")
    public JsonResultVo<List<AuditTrailDO>> getLastAuditTime(@RequestBody AuditTrailDTO auditTrailDTO){
        JsonResultVo<List<AuditTrailDO>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(auditTrailService.getLastAuditTime(auditTrailDTO));
        return jsonResultVo;
    }

    @Operation(summary = "根据预算申请表ID查审批轨迹", description = "[author: 10200571]")
    @PostMapping("/getAuditTrailByApplyMainId")
    public JsonResultVo<List<AuditTrailResponse>> getAuditTrailByApplyMainId(@RequestBody AuditTrailDTO auditTrailDTO){
        JsonResultVo<List<AuditTrailResponse>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(auditTrailService.getAuditTrailResponses(auditTrailDTO));
        return jsonResultVo;
    }

}
