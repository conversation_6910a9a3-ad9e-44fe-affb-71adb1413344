package com.qm.ep.rebate.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@Data
@RefreshScope
public class RolesConfig {

    /**
     * 商务政策管理经理
     */
    @Value(("${swzc-roles.management-manager:WB008100077}"))
    private String managementManager;

    /**
     * 商务政策录入及确认专员
     */
    @Value(("${swzc-roles.entry-and-confirmation:WB0081000771}"))
    private String entryAndConfirmation;

    /**
     * 商务政策分配专员
     */
    @Value(("${swzc-roles.policy-allocation:WB0081000772}"))
    private String policyAllocation;


    /**
     * 商务政策销售运营经理
     */
    @Value(("${swzc-roles.operations-manager:WB008100080}"))
    private String operationsManager;

    /**
     * 商务政策财务管理专员
     */
    @Value(("${swzc-roles.financial-business-policy-officer:WB008100081}"))
    private String financialBusinessPolicyOfficer;


    /**
     * 超级管理员
     */
    @Value(("${swzc-roles.super-administrator:WB008100084}"))
    private String superAdministrator;



}
