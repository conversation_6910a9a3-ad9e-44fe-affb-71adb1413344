package com.qm.ep.rebate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.common.aop.idempotent.IdempotentLock;
import com.qm.ep.rebate.common.config.RolesConfig;
import com.qm.ep.rebate.domain.bean.PolicyPublishedDO;
import com.qm.ep.rebate.domain.bean.PolicyPublishedExcelDO;
import com.qm.ep.rebate.domain.dto.FawDepartmentDTO;
import com.qm.ep.rebate.domain.dto.PolicyPublishedDTO;
import com.qm.ep.rebate.domain.request.PolicyPublishedQueryRequest;
import com.qm.ep.rebate.domain.request.PolicyPublishedRemoteRequest;
import com.qm.ep.rebate.domain.vo.FawAgentVO;
import com.qm.ep.rebate.domain.vo.FawDepartmentNoChildVO;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.ep.rebate.infrastructure.util.JsonUtil;
import com.qm.ep.rebate.service.PolicyConfirmInfoService;
import com.qm.ep.rebate.service.PolicyPublishedService;
import com.qm.ep.rebate.service.workbench.PlatformUserService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Controller
 * 维护已发布政策信息  JsonResultVo
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Tag(name = "维护政策信息")
@RestController
@RequestMapping("/policyPublished")

@Slf4j
public class PolicyPublishedController extends BaseController {


    @Resource
    private PolicyPublishedService policyPublishedService;

    @Resource
    private PolicyConfirmInfoService policyConfirmInfoService;

    @Resource
    private PlatformUserService platformUserService;

    @Resource
    private RolesConfig rolesConfig;


    @Operation(summary = "维护政策信息新建和保存", description = "[author: 10200571]")
    @PostMapping("/oldSave")
    public JsonResultVo<PolicyPublishedDO> oldSave(@RequestBody PolicyPublishedDO tempDO) {
        return policyPublishedService.savePolicyPublishedDO(tempDO);
    }


    /**
     * 维护信息列表页
     */
    @Operation(summary = "维护信息列表页", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<PolicyPublishedDO>> table(@RequestBody PolicyPublishedDTO tempDTO) {

        // List<String> policyIdList = new ArrayList<>();
        //
        // // 左侧菜单进查看所有数据，工作台进只看自己数据
        // if (StringUtils.isNoneBlank(tempDTO.getTaskFlowInstanceCode()) || StringUtils.isNoneBlank(tempDTO.getTaskInstanceCode())) {
        //     // 权限过滤
        //     LoginKeyDO loginKeyDO = getUserInfo();
        //     Map<String, String> paramMap = new HashMap<>();
        //     paramMap.put("loginName", loginKeyDO.getPersonCode());
        //
        //     List<UserInfoResponseDTO> userInfoList = platformUserService.getUserInfo(paramMap);
        //
        //     if (userInfoList.isEmpty()) {
        //         throw new QmException("获取用户信息失败!");
        //     }
        //
        //     List<RoleDTO> roleDTOList = userInfoList.get(0).getRoleVoList();
        //     List<String> roleCodeList = roleDTOList.stream().map(RoleDTO::getCode).toList();
        //
        //
        //     if (!roleCodeList.contains(rolesConfig.getSuperAdministrator())) {
        //         policyIdList = permissionFiltering();
        //         if (policyIdList.isEmpty()) {
        //             return new JsonResultVo<>();
        //         }
        //     }
        // }

        // 定义查询构造器
        QmQueryWrapper<PolicyPublishedDO> queryWrapper = new QmQueryWrapper<>();

        LambdaQueryWrapper<PolicyPublishedDO> lambdaWrapper = queryWrapper.lambda();
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getId())) {
            lambdaWrapper.eq(PolicyPublishedDO::getId, tempDTO.getId());
        }
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVpolicyname())) {
            lambdaWrapper.like(PolicyPublishedDO::getVpolicyname, tempDTO.getVpolicyname());
        }
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getVpolicycode())) {
            lambdaWrapper.like(PolicyPublishedDO::getVpolicycode, tempDTO.getVpolicycode());
        }
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getStage())) {
            lambdaWrapper.eq(PolicyPublishedDO::getStage, tempDTO.getStage());
        }
        if (Objects.nonNull(tempDTO.getPublishstartdate())) {
            lambdaWrapper.ge(PolicyPublishedDO::getPublishdate, DateUtil.toString(tempDTO.getPublishstartdate()));
        }
        if (Objects.nonNull(tempDTO.getPublishenddate())) {
            lambdaWrapper.le(PolicyPublishedDO::getPublishdate, DateUtil.toString(tempDTO.getPublishenddate()));
        }

        lambdaWrapper.orderByDesc(PolicyPublishedDO::getUpdateon);


        QmPage<PolicyPublishedDO> list = policyPublishedService.table(queryWrapper, tempDTO);

        JsonResultVo<QmPage<PolicyPublishedDO>> ret = new JsonResultVo<>();

        for (PolicyPublishedDO temp : list.getItems()) {
            if (StringUtils.isNotBlank(temp.getPolicyTargetSeries())) {
                temp.setSeries(Arrays.asList(temp.getPolicyTargetSeries().split(",")));
            }
        }

        ret.setData(list);
        return ret;
    }

    // @Operation(summary = "控制器接口", description = "[author: 10200571]")
    // private List<String> permissionFiltering() {
    //
    //     LoginKeyDO loginKey = getUserInfo();
    //
    //     QmQueryWrapper<PolicyPublishedDO> queryWrapper1 = new QmQueryWrapper<>();
    //     LambdaQueryWrapper<PolicyPublishedDO> lambdaWrapper1 = queryWrapper1.lambda();
    //     lambdaWrapper1.eq(PolicyPublishedDO::getStage, String.valueOf(PolicyStageEnum.INITIAL.getCode()));
    //     lambdaWrapper1.eq(PolicyPublishedDO::getCreateby, loginKey.getPersonCode());
    //     List<PolicyPublishedDO> initialdataList = policyPublishedService.list(queryWrapper1);
    //
    //     QmQueryWrapper<PolicyPublishedDO> queryWrapper3 = new QmQueryWrapper<>();
    //     LambdaQueryWrapper<PolicyPublishedDO> lambdaWrapper3 = queryWrapper3.lambda();
    //     lambdaWrapper3.eq(PolicyPublishedDO::getStage, String.valueOf(PolicyStageEnum.NO_CONFIGURATION_CONFIGURATION_REQUIRED.getCode()));
    //     lambdaWrapper3.eq(PolicyPublishedDO::getCreateby, loginKey.getPersonCode());
    //     List<PolicyPublishedDO> noConfigdataList = policyPublishedService.list(queryWrapper3);
    //
    //     QmQueryWrapper<PolicyPublishedDO> queryWrapper2 = new QmQueryWrapper<>();
    //     LambdaQueryWrapper<PolicyPublishedDO> lambdaWrapper2 = queryWrapper2.lambda();
    //     lambdaWrapper2.ne(PolicyPublishedDO::getStage, String.valueOf(PolicyStageEnum.INITIAL.getCode()));
    //     List<PolicyPublishedDO> unInitialdataList = policyPublishedService.list(queryWrapper2);
    //
    //     List<String> unInitialPolicyIdList = unInitialdataList.stream().map(PolicyPublishedDO::getId).collect(Collectors.toList());
    //
    //     List<PolicyConfirmInfoDO> policyConfirmInfoDOList = new ArrayList<>();
    //
    //     if (!unInitialPolicyIdList.isEmpty()) {
    //
    //         QmQueryWrapper<PolicyConfirmInfoDO> queryWrapper = new QmQueryWrapper<>();
    //         LambdaQueryWrapper<PolicyConfirmInfoDO> lambdaWrapper = queryWrapper.lambda();
    //         lambdaWrapper.in(PolicyConfirmInfoDO::getVpolicyid, unInitialPolicyIdList);
    //         lambdaWrapper.eq(PolicyConfirmInfoDO::getAgent, loginKey.getPersonCode());
    //
    //         policyConfirmInfoDOList = policyConfirmInfoService.list(queryWrapper);
    //     }
    //
    //
    //     List<String> policyConfirmPolicyIdList = policyConfirmInfoDOList.stream().map(PolicyConfirmInfoDO::getVpolicyid).collect(Collectors.toList());
    //     List<String> initialdataPolicyIdList = initialdataList.stream().map(PolicyPublishedDO::getId).collect(Collectors.toList());
    //     List<String> noConfigdataPolicyIdList = noConfigdataList.stream().map(PolicyPublishedDO::getId).collect(Collectors.toList());
    //
    //     List<String> policyIdList = new ArrayList<>();
    //     policyIdList.addAll(policyConfirmPolicyIdList);
    //     policyIdList.addAll(initialdataPolicyIdList);
    //     policyIdList.addAll(noConfigdataPolicyIdList);
    //
    //     return policyIdList;
    // }

    /**
     * excel导入
     * create by :zhangyanpeng
     * time:2020/11/27
     */
    @Operation(summary = "维护政策信息excel导入", description = "[author: 10200571]")
    @PostMapping("/commit")
    @IdempotentLock(lockName = "com.qm.ep.rebate.controller.PolicyPublishedController.commit")
    public JsonResultVo<List<PolicyPublishedExcelDO>> commit(@RequestParam MultipartFile file) throws QmException {
        JsonResultVo<List<PolicyPublishedExcelDO>> resultVo = new JsonResultVo<>();
        List<PolicyPublishedExcelDO> list = policyPublishedService.commit(file);
        for (PolicyPublishedExcelDO temp : list) {
            if (temp.getDbegin() == null || temp.getDend() == null) {
                resultVo.setMsgErr("导入数据必须包含如下列:执行开始时间和执行结束时间");
                return resultVo;
            }
        }
        if (list.isEmpty()) {
            resultVo.setMsgErr("导入数据为空");
            return resultVo;
        }
        resultVo.setData(list);
        return resultVo;

    }

    // @Operation(summary = "导入后数据处理", description = "[author: 10200571]")
    // @PostMapping("/commitAfter")
    // @IdempotentLock
    // public JsonResultVo<List<PolicyPublishedExcelDO>> commitAfter(@RequestBody PolicyPublishedExcelDTO tempDTO) throws QmException {
    //     LoginKeyDO loginKey = getUserInfo();
    //     JsonResultVo<List<PolicyPublishedExcelDO>> resultVo = new JsonResultVo<>();
    //     List<PolicyPublishedExcelDO> list = policyPublishedService.commitAfter(tempDTO, loginKey);
    //
    //     resultVo.setData(list);
    //     if (!BootAppUtil.isNullOrEmpty(list) && list.isEmpty()) {
    //         resultVo.setMsg("导入成功!");
    //     } else {
    //         resultVo.setMsg("导入不全成功!");
    //     }
    //     return resultVo;
    // }

    @Operation(summary = "获取全量部门", description = "[author: 10200571]")
    @PostMapping("/getDepartment")
    public JsonResultVo<List<FawDepartmentNoChildVO>> getDepartment() {
        JsonResultVo<List<FawDepartmentNoChildVO>> resultVo = new JsonResultVo<>();
        resultVo.setData(policyPublishedService.getDepartment());
        return resultVo;
    }

    @Operation(summary = "根据部门获取人员", description = "[author: 10200571]")
    @PostMapping("/getAgent")
    public JsonResultVo<List<FawAgentVO>> getAgent(@RequestBody FawDepartmentDTO tempDTO) {
        JsonResultVo<List<FawAgentVO>> resultVo = new JsonResultVo<>();
        resultVo.setData(policyPublishedService.getAgent(tempDTO.getDepartments()));
        return resultVo;
    }

    @Operation(summary = "分配经办人-确认", description = "[author: 10200571]")
    @PostMapping("/batchAllocationHandling")
    @IdempotentLock
    public JsonResultVo<Integer> batchAllocationHandling(@RequestBody List<PolicyPublishedDO> list) {
        JsonResultVo<Integer> resultVo = new JsonResultVo<>();
        policyPublishedService.validatePolicyPublishedStatus(list);
        //policyPublishedService.validPolicyPublishOperateAuth();
        for (PolicyPublishedDO policyPublishedDO : list) {
            policyPublishedService.batchAllocationHandling(policyPublishedDO.getId());
        }
        resultVo.setData(0);
        return resultVo;
    }

    @Operation(summary = "定时新增政策任务", description = "[author: 10200571]")
    @PostMapping("/scheduledStartTasks")
    public JsonResultVo<Integer> scheduledStartTasks() {
        log.info("定时新增政策任务scheduledStartTasks触发了");
        JsonResultVo<Integer> resultVo = new JsonResultVo<>();
        policyPublishedService.scheduledStartTasks();
        return resultVo;
    }

    @Operation(summary = "取消确认", description = "[author: 10200571]")
    @GetMapping("/cancelConfirm")
    public JsonResultVo<Integer> cancelConfirm(String id) {
        JsonResultVo<Integer> resultVo = new JsonResultVo<>();
        resultVo.setData(policyPublishedService.cancelConfirm(id));
        return resultVo;
    }

    @Operation(summary = "接收知识中心政策文件推送", description = "[author: 10200571]")
    @PostMapping("/receivePolicyPublished")
    public JsonResultVo<String> receivePolicyPublished(@Valid @RequestBody PolicyPublishedRemoteRequest request) {
        log.info("开始接收知识中心政策文件推送-参数：{}", JsonUtil.toJsonString(request));
        JsonResultVo<String> resultVo = new JsonResultVo<>();
        policyPublishedService.receivePolicyPublished(request);
        resultVo.setData(System.currentTimeMillis() + "接收成功:"+request.getPolicyCode());
        return resultVo;
    }

    @Operation(summary = "维护信息详情-根据待办code获取", description = "[author: 10200571]")
    @PostMapping("/queryPolicyPublishedByTaskInstanceCode")
    public JsonResultVo<PolicyPublishedDO> queryPolicyPublishedByTaskInstanceCode(@RequestBody PolicyPublishedQueryRequest request) {
        JsonResultVo<PolicyPublishedDO> resultVo = new JsonResultVo<>();
        resultVo.setData(policyPublishedService.queryPolicyPublishedByTaskInstanceCode(request));
        return resultVo;
    }


    @Operation(summary = "维护政策信息新建和保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<PolicyPublishedDO> save(@RequestBody PolicyPublishedDO tempDO) {
        String stop =tempDO.getStop();
        JsonResultVo<PolicyPublishedDO> policyPublished = policyPublishedService.savePolicyPublishedDO(tempDO);
        return policyPublished;
    }


}
