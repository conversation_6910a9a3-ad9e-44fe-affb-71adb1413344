package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.PremiseMainDTO;
import com.qm.ep.rebate.domain.dto.ValidationResultDTO;
import com.qm.ep.rebate.domain.dto.structure.PremiseDTO;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.SourceTypeEnum;
import com.qm.ep.rebate.remote.feign.RebateDataFeignClient;
import com.qm.ep.rebate.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/premise")
@Tag(name = "前置条件")
public class PremiseController extends BaseController {

    @Resource
    private PremiseService premiseService;
    @Resource
    private CalcObjectSourceService calcObjectSourceService;
    @Resource
    private PremiseMainService premiseMainService;
    @Resource
    private PremiseDetailService premiseDetailService;
    @Resource
    private RankService rankService;
    @Resource
    private PremiseConditionsService premiseConditionsService;
    @Resource
    private CompareService compareService;
    @Resource
    private PolicyService policyService;

    @Autowired
    private RebateDataFeignClient rebateDataFeignClient;

    @Resource
    private PolicyFlagService policyFlagService;

    @Operation(summary = "查询前置条件列表", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<PremiseMainDO>> table(@RequestBody PremiseMainDTO premiseMainDTO){
        JsonResultVo<QmPage<PremiseMainDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<PremiseMainDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PremiseMainDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(PremiseMainDO::getPolicyId, premiseMainDTO.getPolicyId());

        QmPage<PremiseMainDO> data = premiseMainService.table(queryWrapper, premiseMainDTO);
        // 添加可编辑标识
        List<PremiseMainDO> premiseList = data.getItems();
        if(CollectionUtils.isNotEmpty(premiseList)) {
            Map<String, String> premiseMap = policyFlagService.getPremiseEditFlagMap(premiseMainDTO.getPolicyId(), premiseList);
            premiseList.forEach(premise -> {
                premise.setIsEdit(premiseMap.get(premise.getId()));
            });
        }
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "获取详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<PremiseDTO> detail(@RequestBody PremiseDTO premiseDTO){
        JsonResultVo<PremiseDTO> jsonResultVo = new JsonResultVo<>();

        if(BootAppUtil.isNullOrEmpty(premiseDTO) || BootAppUtil.isNullOrEmpty(premiseDTO.getMain()) || BootAppUtil.isNullOrEmpty(premiseDTO.getMain().getId())) {
            jsonResultVo.setMsgErr("未找到前置条件主键");
            return jsonResultVo;
        }

        PremiseMainDO mainDO = premiseMainService.getById(premiseDTO.getMain().getId());

        mainDO.setCalcObjectSources(calcObjectSourceService.getCalcObjectSourceList(mainDO.getCalcObjects()));

        QmQueryWrapper<PremiseDetailDO> queryDetailWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PremiseDetailDO> lambdaDetailWrapper = queryDetailWrapper.lambda();
        lambdaDetailWrapper.eq(PremiseDetailDO::getPremiseId, premiseDTO.getMain().getId());
        List<PremiseDetailDO> details = premiseDetailService.list(lambdaDetailWrapper);

        QmQueryWrapper<RankDO> queryRankWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<RankDO> lambdaRankWrapper = queryRankWrapper.lambda();
        lambdaRankWrapper.eq(RankDO::getObjectId, premiseDTO.getMain().getId()).eq(RankDO::getObjectType, CalcObjectTypeEnum.PREMISE);
        List<RankDO> ranks = rankService.list(lambdaRankWrapper);

        QmQueryWrapper<PremiseConditionsDO> queryConditionsWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PremiseConditionsDO> lambdaConditionsWrapper = queryConditionsWrapper.lambda();
        lambdaConditionsWrapper.eq(PremiseConditionsDO::getPremiseId, premiseDTO.getMain().getId());
        lambdaConditionsWrapper.orderByAsc(PremiseConditionsDO::getSort);
        List<PremiseConditionsDO> conditions = premiseConditionsService.list(lambdaConditionsWrapper);

        QmQueryWrapper<CompareDO> compareWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<CompareDO> lambdaCompareWrapper = compareWrapper.lambda();
        lambdaCompareWrapper.eq(CompareDO::getObjectId, premiseDTO.getMain().getId());
        List<CompareDO> compares = compareService.list(lambdaCompareWrapper);

        PremiseDTO data = new PremiseDTO();
        data.setMain(mainDO);
        data.setDetails(details);
        data.setRanks(ranks);
        data.setConditions(conditions);
        data.setCompares(compares);
        jsonResultVo.setData(data);

        return jsonResultVo;
    }

    @Operation(summary = "获取详情", description = "[author: 10200571]")
    @PostMapping("/copy")
    public JsonResultVo<PremiseDTO> copy(@RequestBody PremiseDTO premiseDTO){
        String newPremiseName = premiseDTO.getPremiseName();

        JsonResultVo<PremiseDTO> jsonResultVo = new JsonResultVo<>();
        PremiseMainDO request = new PremiseMainDO();
        request.setId(premiseDTO.getId());
        premiseDTO.setMain(request);
        JsonResultVo<PremiseDTO> detail = this.detail(premiseDTO);
        PremiseDTO input = detail.getData();

        PremiseMainDO main = input.getMain();
        main.setPremiseName(newPremiseName);
        main.setId(null);
        main.setCalcObjects(JSONUtil.toJsonStr(main.getCalcObjectSources()));
        List<PremiseDetailDO> details = input.getDetails();
        details.forEach(ele->ele.setId(null));
        List<RankDO> ranks = input.getRanks();
        ranks.forEach(ele->ele.setId(null));
        List<CompareDO> compares = input.getCompares();
        compares.forEach(ele->ele.setId(null));
        List<PremiseConditionsDO> conditions = input.getConditions();
        conditions.forEach(ele->ele.setId(null));

        this.save(input);

        return jsonResultVo;
    }

    /**
     * 保存前置条件
     * @param premiseDTO 前置条件配置数据
     * @return 返回前置条件主键
     */
    @Operation(summary = "保存前置条件", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<String> save(@RequestBody PremiseDTO premiseDTO) {
        JsonResultVo<String> resultObj = new JsonResultVo<>();

        ValidationResultDTO validationResult = premiseService.validateData(premiseDTO);
        if(!validationResult.getOk()) {
            throw new QmException(validationResult.getMsg());
        }

        validationResult = premiseService.validateName(premiseDTO);
        if(!validationResult.getOk()) {
            throw new QmException(validationResult.getMsg());
        }

        // 校验前置条件的SQL是否正确
        validationResult = premiseService.validateConfig(premiseDTO);
        if(!validationResult.getOk()) {
            resultObj.setMsgErr(validationResult.getMsg());
            resultObj.setData(String.valueOf(validationResult.getData()));
            return resultObj;
        }

        // 有主键，更新内容
        if(BootAppUtil.isnotNullOrEmpty(premiseDTO.getMain().getId())) {
            premiseService.deleteDetailByIds(Collections.singletonList(premiseDTO.getMain().getId()));
        }

        String id = premiseService.save(premiseDTO);
        resultObj.setData(id);
        return resultObj;

    }

    /**
     * 删除前提条件记录
     * @param deleteIds 前置条件ID列表
     * @return
     */
    @Operation(summary = "删除前提条件记录", description = "[author: 10200571]")
    @PostMapping("/deleteByIds")
    public JsonResultVo deleteByIds(@RequestBody List<String> deleteIds) {
        JsonResultVo<Object> ret = new JsonResultVo<>();
        String deleteId=deleteIds.get(0);
        PremiseMainDO premiseMainDo = premiseMainService.getById(deleteId);
        //传入参数，objectid和policyId判断历史表中，是否在转换中（状态为3），转换中不可删除
        Map<String,Object> map=new HashMap<>();
        map.put("policyId",premiseMainDo.getPolicyId());
        map.put("objectId",deleteId);
        JsonResultVo<Object> resultObj = rebateDataFeignClient.selectHistory(map);
        if(resultObj.isOk()){
            Map<String, Object> data = (Map<String, Object>)resultObj.getData();
            boolean isOK = Boolean.parseBoolean(data.get("isOk").toString());
            if(!isOK) {
                ret.setMsgErr(data.get("msg").toString());
                return ret;
            }
        } else {
            ret.setMsgErr(ret.getMsg());
            return ret;
        }
            boolean isOk = premiseService.deleteByIds(deleteIds);
            if (isOk) {
                ret.setMsg("删除成功");
            } else {
                ret.setMsgErr("删除失败");
            }
        return ret;
    }

    @Operation(summary = "判断规则公式选择是否发生变更 ", description = "[author: 10200571]")
    @PostMapping("/getPremiseChange")
    public JsonResultVo getChange(@RequestBody PremiseMainDO premiseMainDO){
        JsonResultVo<Object> res = new JsonResultVo<>();
        Map<String, Set<String>> tableWithFieldMap = premiseMainService.getChangedDataSourceName(premiseMainDO.getId());
        if(CollUtil.isNotEmpty(tableWithFieldMap)){
            res.setData(tableWithFieldMap);
        }
        return res;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @PostMapping("/turnDataSource")
    public JsonResultVo turnDataSource(){
        JsonResultVo<Object> res = new JsonResultVo<>();
        List<PremiseMainDO> mainDOList = premiseMainService.list();
        mainDOList.forEach(main -> {
            String policyId = main.getPolicyId();
            PolicyDO policyDO= policyService.getById(policyId);
            String policyName = policyDO == null ? "" : policyDO.getVpolicyname();
            String premiseId = main.getId();
            String calcObjects = main.getCalcObjects();
            if(StringUtils.isNotEmpty(calcObjects) && calcObjects.contains("{")){
                List<CalcObjectSourceDO> sourceDOList = JSON.parseArray(calcObjects, CalcObjectSourceDO.class);
                sourceDOList.forEach(source -> {
                    source.setForPolicyId(policyId);
                    source.setForObjectId(premiseId);
                    source.setForType(CalcObjectTypeEnum.PREMISE);
                    source.setForSourceType(SourceTypeEnum.MAIN);
                    if(!CalcObjectTypeEnum.BASIC.equals(source.getObjectType())){
                        source.setPolicyId(policyId);
                        source.setPolicyName(policyName);
                    }
                });
                calcObjectSourceService.saveBatch(sourceDOList);
                List<String> idList = sourceDOList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                main.setCalcObjects(CharSequenceUtil.join(",", idList));
                main.setDtstamp(DateUtils.getSysdateTime());
                premiseMainService.updateById(main);
            }
        });

        return res;
    }

}
