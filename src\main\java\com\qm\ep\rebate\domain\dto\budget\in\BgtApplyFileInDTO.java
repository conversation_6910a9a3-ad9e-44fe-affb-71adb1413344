package com.qm.ep.rebate.domain.dto.budget.in;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "预算申请 - 文件列表")
public class BgtApplyFileInDTO {

    @Schema(description = "数据-文件名称")
    private String fileName;

    @Schema(description = "数据-文件路径")
    private String filePath;

    @Schema(description = "数据-上传时间")
    private String updateTime;

}
