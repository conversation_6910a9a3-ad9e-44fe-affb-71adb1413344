package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 预算申请单
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_apply_main")
@Schema(description = "数据:预算申请单")
public class BgtApplyMainDO extends Model<BgtApplyMainDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-预算申请单名")
    @TableField("apply_name")
    private String applyName;

    @Schema(description = "数据-申请状态（10-初始、11-提交、12-通过、13驳回）")
    @TableField("apply_status")
    private String applyStatus;

    @Schema(description = "数据-申请日期")
    @TableField("apply_time")
    private String applyTime;

    @Schema(description = "数据-预算申请类型（0-常规调整，1-专项调整）")
    @TableField("apply_type")
    private String applyType;

    @Schema(description = "数据-年")
    @TableField("breakdown_year")
    private String breakdownYear;

    @Schema(description = "数据-季度")
    @TableField("breakdown_quarter")
    private String breakdownQuarter;

    @Schema(description = "数据-申请数")
    @TableField("apply_value")
    private BigDecimal applyValue;

    @Schema(description = "数据-返利项目代码")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-调整方式（0-点数，1-金额）")
    @TableField("modify_way")
    private String modifyWay;

    @Schema(description = "数据-描述（补充说明）")
    @TableField("remark")
    private String remark;

    @Schema(description = "数据-申请来源（01-业务专员，02-运营专员）")
    @TableField("apply_source")
    private String applySource;

    @Schema(description = "数据-政策id")
    @TableField("policy_id")
    private String policyId;

    @Schema(description = "数据-域账号")
    @TableField("submit_code")
    private String submitCode;

    @Schema(description = "数据-姓名")
    @TableField("submit_name")
    private String submitName;

    @Schema(description = "数据-文件路径（逗号拼接）")
    @TableField("file_path")
    private String filePath;

    @Schema(description = "数据-待办实例编码")
    @TableField("taskInstanceCode")
    private String taskInstanceCode;

    @Schema(description = "数据-OA审批流id")
    @TableField("processInstanceId")
    private String processInstanceId;

    @Schema(description = "数据-预算总申请来源预算申请id")
    @TableField("from_apply_main_id")
    private Integer fromApplyMainId;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
