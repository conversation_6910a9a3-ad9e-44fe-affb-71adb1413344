package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.qm.ep.rebate.enumerate.ReportTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REPORT_MAIN")
@Schema(description = "数据:报表明细主表对象")
public class ReportMainDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-报表名称")
    @TableField("REPORT_NAME")
    private String reportName;

    @Schema(description = "数据-报表类型")
    @TableField("REPORT_TYPE")
    private ReportTypeEnum reportType;

    @Schema(description = "数据-兑付对象")
    @TableField("CASH_OBJECT")
    private String cashObject;

    @Schema(description = "数据-兑付对象数据源")
    @TableField(exist = false)
    private CalcObjectSourceDO cashObjectSource;

    @Schema(description = "数据-经销商代码字段")
    @TableField("DEALER_CODE_FIELD")
    private String dealerCodeField;

    @Schema(description = "数据-合计字段")
    @TableField("SUM_FIELD")
    private String sumField;

    @Schema(description = "数据-报表描述")
    @TableField("DESCRIPTION")
    private String description;

    @Schema(description = "数据-计算状态")
    @TableField("CALCULATION_STATUS")
    private Integer calculationStatus;

    @Schema(description = "数据-开始计算时间")
    @TableField(value = "BEGIN_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date beginTime;

    @Schema(description = "数据-结束计算时间")
    @TableField(value = "END_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    /** 通过该字段判断，当前用户是否可以在页面上对该政策编辑 */
    @Schema(description = "数据-是否可编辑")
    @TableField(exist = false)
    private Boolean editable;
}
