package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *  
 * 预算分解详情-返利项目
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Data
@Schema(description = "数据:预算分解详情-返利项目")
public class BgtBreakdownDetailResponse implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "数据-预算分解id")
    private Integer bgtBreakdownMainId;

    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-兑付依据（00-aak，01-std）")
    private String saleType;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    private String bgtType;

    @Schema(description = "数据-车系")
    private String series;

    @Schema(description = "数据-车系点数")
    private BigDecimal seriesPoint;

    @Schema(description = "数据-返利项目预算总点数（常规）")
    private BigDecimal classitemTotalPoint;

    @Schema(description = "数据-返利项目预算使用点数（常规）")
    private BigDecimal classitemUsedPoint;

    @Schema(description = "数据-该分解预算总点数余额")
    private BigDecimal classitemPointBalance;

    @Schema(description = "数据-返利项目预算总金额（专项）")
    private BigDecimal classitemTotalAmount;

    @Schema(description = "数据-返利项目已使用金额（专项）")
    private BigDecimal classitemUsedAmount;

    @Schema(description = "数据-返利项目金额余额（专项）")
    private BigDecimal classitemAmountBalance;

    @Schema(description = "数据-创建日期")
    private Date createOn;

    @Schema(description = "数据-创建人")
    private String createBy;

    @Schema(description = "数据-更新者")
    private String updateBy;

    @Schema(description = "数据-更新日期")
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    private Date dtstamp;

    @Schema(description = "数据-STD平均指导价")
    private BigDecimal stdAveragePrice;

    @Schema(description = "数据-STD销量")
    private BigDecimal stdSaleCount;

    @Schema(description = "数据-STD当前季度收入：销量*收入")
    private BigDecimal currStdAmount;

    @Schema(description = "数据-AAK平均指导价")
    private BigDecimal aakAveragePrice;

    @Schema(description = "数据-AAK销量")
    private BigDecimal aakSaleCount;

    @Schema(description = "数据-AAK当前季度收入：销量*收入")
    private BigDecimal currAakAmount;

    @Schema(description = "数据-截止目前STD季度收入：销量*收入")
    private BigDecimal uptoNowStdAmount;

    @Schema(description = "数据-截止目前季AAK度收入：销量*收入")
    private BigDecimal uptoNowAakAmount;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "类项目系列")
    private String classItemSeries;
}
