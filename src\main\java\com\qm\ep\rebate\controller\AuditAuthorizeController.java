package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.AuditAuthorizeDO;
import com.qm.ep.rebate.domain.dto.AuditAuthorizeDTO;
import com.qm.ep.rebate.domain.dto.IWorkDepartmentDTO;
import com.qm.ep.rebate.domain.request.AuditAuthorizeSaveRequest;
import com.qm.ep.rebate.domain.vo.IWorkDepartmentVO;
import com.qm.ep.rebate.domain.vo.IWorkUserVO;
import com.qm.ep.rebate.domain.vo.UserByRoleVO;
import com.qm.ep.rebate.service.AuditAuthorizeService;
import com.qm.ep.rebate.service.workbench.PlatformUserService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller
 * JsonResultVo
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Tag(name = "审核授权", description = "[author: ********]")
@RestController
@RequestMapping("/auditAuthorize")
public class AuditAuthorizeController extends BaseController {

    @Autowired
    private AuditAuthorizeService auditAuthorizeService;

    @Autowired
    private PlatformUserService platformUserService;

    /**
     * 保存审核授权关系
     */
    @Operation(summary = "保存审核授权关系", description = "[author: ********]")
    @PostMapping("/save")
    public JsonResultVo<AuditAuthorizeDO> save(@Valid @RequestBody AuditAuthorizeSaveRequest request) {
        JsonResultVo<AuditAuthorizeDO> resultObj = new JsonResultVo<>();

        AuditAuthorizeDO insertDO = new AuditAuthorizeDO();
        BeanUtils.copyProperties(request, insertDO);
        insertDO.setCompany("hq");
        insertDO.setFirstLoginAccount(request.getFirstLoginAccount());
        insertDO.setFirstAuditName(request.getFirstAuditName());
        insertDO.setSecondLoginAccount(request.getSecondLoginAccount());
        insertDO.setSecondAuditName(request.getSecondAuditName());
        AuditAuthorizeDO old = auditAuthorizeService.getById(request.getId());
        if (old != null){
            insertDO.setDtstamp(old.getDtstamp());
        }
        auditAuthorizeService.saveOrUpdate(insertDO);


        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: ********]")
    @PostMapping("/deleteByIds")
    public JsonResultVo<AuditAuthorizeDO> deleteByIds(@RequestBody AuditAuthorizeDO tempDO) {
        JsonResultVo<AuditAuthorizeDO> resultObj = new JsonResultVo<>();
        boolean flag = auditAuthorizeService.removeByIds(tempDO.getIds());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询-红旗", description = "[author: ********]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<AuditAuthorizeDO>> table(@RequestBody AuditAuthorizeDTO tempDTO) {
        QmQueryWrapper<AuditAuthorizeDO> queryWrapper = new QmQueryWrapper<>();
        QmPage<AuditAuthorizeDO> list = auditAuthorizeService.table(queryWrapper, tempDTO);
        JsonResultVo<QmPage<AuditAuthorizeDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }





    @Operation(summary = "获取工作台组织树", description = "[author: ********]")
    @PostMapping("/getOrganizationTree")
    public JsonResultVo<IWorkDepartmentVO> getOrganizationTree() {
        JsonResultVo<IWorkDepartmentVO> resultObj = new JsonResultVo<>();
        List<IWorkDepartmentVO> deptTree = auditAuthorizeService.getOrganizationTree();
        resultObj.setDataList(deptTree);
        return resultObj;
    }

    @Operation(summary = "根据部门获取人员-工作台", description = "[author: ********]")
    @PostMapping("/findUserByOrganization")
    public JsonResultVo<IWorkUserVO> findUserByOrganization(@RequestBody IWorkDepartmentDTO tempDTO) {
        JsonResultVo<IWorkUserVO> resultVo = new JsonResultVo<>();
        resultVo.setDataList(auditAuthorizeService.findUserByOrganization(tempDTO));
        return resultVo;
    }

    // @Operation(summary = "更新政策专员和审核人的姓名", description = "[author: ********]")
    // @PostMapping("/updateFirstAndSecondAuditName")
    // public JsonResultVo<List<AuditAuthorizeDO>> updateFirstAndSecondAuditName(@RequestBody HashMap<String, String> id) {
    //     JsonResultVo<List<AuditAuthorizeDO>> resultObj = new JsonResultVo<>();
    //     List<AuditAuthorizeDO> retAuditAuthorizeDOList = new ArrayList<>();
    //
    //     List<AuditAuthorizeDO> authorizeDOList = new ArrayList<>();
    //
    //     // 查询要更新政策专员和审核人的列表
    //     if (id != null && !id.isEmpty()) {
    //         AuditAuthorizeDO authorizeDO = auditAuthorizeService.getById(id.get("id"));
    //         authorizeDOList.add(authorizeDO);
    //     } else {
    //         QmQueryWrapper<AuditAuthorizeDO> queryWrapper = new QmQueryWrapper<>();
    //         LambdaQueryWrapper<AuditAuthorizeDO> lambdaWrapper = queryWrapper.lambda();
    //
    //         lambdaWrapper.isNull(AuditAuthorizeDO::getFirstAuditName).or()
    //                 .eq(AuditAuthorizeDO::getFirstAuditName, "").or()
    //                 .isNull(AuditAuthorizeDO::getSecondAuditName).or()
    //                 .eq(AuditAuthorizeDO::getSecondAuditName, "");
    //
    //         authorizeDOList = auditAuthorizeService.list(queryWrapper);
    //     }
    //
    //     List<IWorkUserVO> iWorkUserVOS = auditAuthorizeService.findUserByOrganization(new IWorkDepartmentDTO());
    //
    //     for (AuditAuthorizeDO authorizeDO : authorizeDOList) {
    //         boolean updFlag = false;
    //         // 从findUser接口取回对应的Employeeid的姓名
    //         List<IWorkUserVO> getFirstAuditNameList = iWorkUserVOS.stream().filter(it -> authorizeDO.getCommissioner().equals(it.getEmployeeId())).collect(Collectors.toList());
    //         if (!getFirstAuditNameList.isEmpty()) {
    //             // 设置 商务政策专员 姓名
    //             authorizeDO.setFirstAuditName(getFirstAuditNameList.get(0).getUserName());
    //         } else {
    //             authorizeDO.setFirstAuditName(authorizeDO.getCommissioner());
    //         }
    //
    //         List<IWorkUserVO> getSecondAuditNameList = iWorkUserVOS.stream().filter(it -> authorizeDO.getDirector().equals(it.getEmployeeId())).collect(Collectors.toList());
    //         if (!getSecondAuditNameList.isEmpty()) {
    //             // 设置 总监 姓名
    //             authorizeDO.setSecondAuditName(getSecondAuditNameList.get(0).getUserName());
    //         } else {
    //             authorizeDO.setSecondAuditName(authorizeDO.getDirector());
    //         }
    //
    //         retAuditAuthorizeDOList.add(authorizeDO);
    //     }
    //
    //     auditAuthorizeService.saveOrUpdateBatch(retAuditAuthorizeDOList);
    //     resultObj.setData(retAuditAuthorizeDOList);
    //     return resultObj;
    // }

    @Operation(summary = "根据角色查询用户-工作台", description = "[author: ********]")
    @PostMapping("/getUserByRole")
    public JsonResultVo<UserByRoleVO> getUserByRole() {
        // 设定角色查询商务政策管理经理
        List<UserByRoleVO> userList = platformUserService.getUserByMMRole();
        JsonResultVo<UserByRoleVO> resultVo = new JsonResultVo<>();
        resultVo.setDataList(userList);
        return resultVo;
    }
}
