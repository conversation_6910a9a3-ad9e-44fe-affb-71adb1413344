package com.qm.ep.rebate.domain.bean;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sale_archives")
@Schema(description = "数据:经销商维护车辆档案表")
public class SaleArchivesDO implements Serializable {
    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-经销商代码")
    @TableField("dealerCode")
    @Excel(name = "经销商代码", orderNum = "1")
    private String dealerCode;

    @Schema(description = "数据-客户代码")
    @TableField("customerCode")
    @Excel(name = "客户代码", orderNum = "1")
    private String customerCode;

    @Schema(description = "数据-客户名称")
    @TableField("customerName")
    @Excel(name = "客户名称", orderNum = "1")
    private String customerName;

    @Schema(description = "数据-客户类型")
    @TableField("customerType")
    @Excel(name = "客户类型", orderNum = "1")
    private String customerType;

    @Schema(description = "数据-移动电话")
    @TableField("mobile")
    @Excel(name = "移动电话", orderNum = "1")
    private String mobile;

    @Schema(description = "数据-职业")
    @TableField("occupation")
    @Excel(name = "职业", orderNum = "1")
    private String occupation;

    @Schema(description = "数据-车架号")
    @TableField("vin")
    @Excel(name = "车架号", orderNum = "1")
    private String vin;

    @Schema(description = "数据-车系")
    @TableField("series")
    @Excel(name = "车系", orderNum = "1")
    private String series;

    @Schema(description = "数据-车型")
    @TableField("model")
    @Excel(name = "车型", orderNum = "1")
    private String model;

    @Schema(description = "数据-EV标识")
    @TableField("evFlag")
    @Excel(name = "EV标识", orderNum = "1")
    private Integer evFlag;

    @Schema(description = "数据-采购日期")
    @TableField("purchaseDate")
    @Excel(name = "采购日期", orderNum = "1")
    private Date purchaseDate;

    @Schema(description = "数据-采购价格")
    @TableField("purchasePrice")
    @Excel(name = "采购价格", orderNum = "1")
    private Double purchasePrice;

    @Schema(description = "数据-入库日期")
    @TableField("inDate")
    @Excel(name = "入库日期", orderNum = "1")
    private Date inDate;

    @Schema(description = "数据-退库日期")
    @TableField("outDate")
    @Excel(name = "退库日期", orderNum = "1")
    private Date outDate;

    @Schema(description = "数据-零售日期")
    @TableField("saleDate")
    @Excel(name = "零售日期", orderNum = "1")
    private Date saleDate;

    @Schema(description = "数据-零售价格")
    @TableField("salePrice")
    @Excel(name = "零售价格", orderNum = "1")
    private Double salePrice;

    @Schema(description = "数据-开票日期")
    @TableField("invoiceDate")
    @Excel(name = "开票日期", orderNum = "1")
    private Date invoiceDate;

    @Schema(description = "数据-开票价格")
    @TableField("invoicePrice")
    @Excel(name = "开票价格", orderNum = "1")
    private Double invoicePrice;

    @Schema(description = "数据-车牌号")
    @TableField("license")
    @Excel(name = "车牌号", orderNum = "1")
    private String license;

    @Schema(description = "数据-上牌日期")
    @TableField("registrationDate")
    @Excel(name = "上牌日期", orderNum = "1")
    private Date registrationDate;

    @Schema(description = "数据-上牌省份")
    @TableField("registrationProvince")
    @Excel(name = "上牌省份", orderNum = "1")
    private String registrationProvince;

    @Schema(description = "数据-上牌城市")
    @TableField("registrationCity")
    @Excel(name = "上牌城市", orderNum = "1")
    private String registrationCity;

    @Schema(description = "数据-购车方式")
    @TableField("purchaseMethod")
    @Excel(name = "购车方式", orderNum = "1")
    private String purchaseMethod;

    @Schema(description = "数据-车辆用途")
    @TableField("vehicleUsage")
    @Excel(name = "车辆用途", orderNum = "1")
    private String vehicleUsage;

    @Schema(description = "数据-保险日期")
    @TableField("insuranceDate")
    @Excel(name = "保险日期", orderNum = "1")
    private Date insuranceDate;

    @Schema(description = "数据-保险年限")
    @TableField("insuranceMonth")
    @Excel(name = "保险年限", orderNum = "1")
    private Integer insuranceMonth;

    @Schema(description = "数据-贷款年限")
    @TableField("loanDate")
    @Excel(name = "贷款年限", orderNum = "1")
    private Date loanDate;

    @Schema(description = "数据-金融机构")
    @TableField("financialInstitution")
    @Excel(name = "金融机构", orderNum = "1")
    private String financialInstitution;

    @Schema(description = "数据-创建日期")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-创建人")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-修改日期")
    @TableField(value ="updateon", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-修改人")
    @TableField(value ="updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "数据-品牌")
    @TableField(value = "BRAND", exist = false)
    @Excel(name = "品牌", orderNum = "1")
    private String brand;

    @Schema(description = "数据-经销商名称")
    @TableField(value = "dealerName", exist = false)
    @Excel(name = "经销商名称", orderNum = "1")
    private String dealerName;

    @Schema(description = "数据-经销商简称")
    @TableField(value = "dealerAbbreviation", exist = false)
    @Excel(name = "经销商简称", orderNum = "1")
    private String dealerAbbreviation;

    @Schema(description = "数据-失败原因")
    @TableField(value = "reseaon", exist = false)
    private String reseaon;
}
