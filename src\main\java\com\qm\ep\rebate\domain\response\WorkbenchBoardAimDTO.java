package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "数据:角色工作台指标数据类")
public class WorkbenchBoardAimDTO {

    @Schema(description = "数据-战队编号")
    private String teamCode;

    @Schema(description = "数据-L3流程编码")
    private String l3Code;

    @Schema(description = "数据-L4流程编码")
    private String l4Code;

    @Schema(description = "数据-日期yyyy-MM-dd")
    private String indicatorDate;

    @Schema(description = "数据-指标列表")
    private List<IndicatorDTO> indicatorList;
}
