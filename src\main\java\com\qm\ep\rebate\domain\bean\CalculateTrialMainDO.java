package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 红旗伙伴试算配置主表
 *
 *
 * <AUTHOR>
 * @since 2022-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("calculateTrialmain")
@Schema(description = "红旗伙伴试算配置主表")
public class CalculateTrialMainDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策ID")
    @TableField("policyid")
    private String policyid;

    @Schema(description = "数据-红旗伙伴试算配置名称")
    @TableField("factorname")
    private String factorname;

    @Schema(description = "数据-创建")
    @TableField("createon")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createon;

    @Schema(description = "数据-创建者")
    @TableField("createby")
    private String createby;

    @Schema(description = "数据-更新")
    @TableField("updateon")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateon;

    @Schema(description = "数据-更新作者")
    @TableField("updateby")
    private String updateby;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createbyname;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updatebyname;

    @Schema(description = "数据-政策名称")
    @TableField(exist = false)
    private String policyName;
}
