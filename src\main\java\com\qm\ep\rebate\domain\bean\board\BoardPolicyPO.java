package com.qm.ep.rebate.domain.bean.board;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qm.ep.rebate.enumerate.BoardTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  
 * 政策看板数据表
 *  
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Schema(description = "数据:实体类-  政策看板数据表  ")
@TableName("board_policy")
@Data
public class BoardPolicyPO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "数据-id")
    private String id;

    /**
     * 封版日期
     */
    @Schema(description = "数据-封版日期")
    private String sealDate;

    /**
     * 政策逾期数
     */
    @Schema(description = "数据-政策逾期数")
    private Integer delayCount;

    /**
     * 政策总数
     */
    @Schema(description = "数据-政策总数")
    private Integer totalCount;

    /**
     * 新增数(可为负)
     */
    @Schema(description = "数据-新增数(可为负)")
    private String changedCount;

    /**
     * 0：政策逾期，1：兑付逾期
     */
    @Schema(description = "数据-0：政策逾期，1：兑付逾期")
    private Integer state;

    /**
     * 创建时间
     */
    @Schema(description = "数据-创建时间")
    private String createTime;

    /**
     * 政策获取率
     */
    @Schema(description = "数据-政策获取率")
    private String acquireRate;

    @Schema(description = "数据-董事会政策详情")
    @TableField(exist = false)
    List<BoardPolicyDetailPO> boardPolicyDetails;

    @Schema(description = "数据-月延迟计数")
    private Integer monthDelayCount;
    @Schema(description = "数据-年延迟计数")
    private Integer yearDelayCount;

    public static BoardPolicyPO initBoardPolicy(BoardTypeEnum boardType, int totalCount, String sealDate){
        BoardPolicyPO po = new BoardPolicyPO();
        po.setSealDate(sealDate);
        po.setState(boardType.getCode());
        po.setCreateTime(DateUtil.now());
        po.setTotalCount(totalCount);
        return po;
    }

}
