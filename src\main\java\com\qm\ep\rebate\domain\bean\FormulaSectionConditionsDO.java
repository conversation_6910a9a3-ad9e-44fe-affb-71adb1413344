package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 计算公式区间赋值表子表
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-计算公式区间赋值表子表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value ="formula_section_conditions")
public class FormulaSectionConditionsDO implements Serializable {
    /**
     *主键id
     */
    @Schema(description = "数据-主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-区间ID")
    @TableField(value = "sectionId")
    private String sectionId;

    /**
     * 区间赋值表id
     */
    @Schema(description = "数据-区间赋值表id")
    @TableField(value = "section_assignment_id")
    private String sectionAssignmentId;

    /**
     * 开始括号
     */
    @Schema(description = "数据-开始括号")
    @TableField(value = "frontBracket")
    private String frontBracket;

    /**
     * 筛选字段来源
     */
    @Schema(description = "数据-筛选字段来源")
    @TableField(value = "fieldNameFrom")
    private String fieldNameFrom;

    /**
     * 筛选字段
     */
    @Schema(description = "数据-筛选字段")
    @TableField(value = "fieldname")
    private String fieldName;

    /**
     * 运算
     */
    @Schema(description = "数据-运算")
    @TableField(value = "caltype")
    private String calType;

    /**
     * 筛选方式
     */
    @Schema(description = "数据-筛选方式")
    @TableField(value = "filtratetype")
    private String filtrateType;

    /**
     * 条件值 来源
     */
    @Schema(description = "数据-条件值 来源")
    @TableField(value = "filtrateFrom")
    private String filtrateFrom;

    /**
     * 条件值
     */
    @Schema(description = "数据-条件值")
    @TableField(value = "filtrate")
    private String filtrate;

    /**
     * 结束括号
     */
    @Schema(description = "数据-结束括号")
    @TableField(value = "backBracket")
    private String backBracket;

    /**
     * 逻辑
     */
    @Schema(description = "数据-逻辑")
    @TableField(value = "contype")
    private String conType;

    /**
     * 排序
     */
    @Schema(description = "数据-排序")
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @Schema(description = "数据-创建时间")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    private Date createOn;

    /**
     * 创建人
     */
    @Schema(description = "数据-创建人")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改时间
     */
    @Schema(description = "数据-修改时间")
    @TableField(value = "updateon", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    /**
     * 修改人
     */
    @Schema(description = "数据-修改人")
    @TableField(value = "updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 备注
     */
    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;


}
