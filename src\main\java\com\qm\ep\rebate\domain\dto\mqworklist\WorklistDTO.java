package com.qm.ep.rebate.domain.dto.mqworklist;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-工作清单 DTO")
@Data
public class WorklistDTO {

    @Schema(description = "数据-主类")
    private String mainClass;

    @Schema(description = "数据-内容")
    private JSONObject content;



}