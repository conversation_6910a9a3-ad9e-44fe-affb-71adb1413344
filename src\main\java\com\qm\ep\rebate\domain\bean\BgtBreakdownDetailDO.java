package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 预算分解详情-返利项目
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_breakdown_detail")
@Schema(description = "数据:预算分解详情-返利项目")
public class BgtBreakdownDetailDO extends Model<BgtBreakdownDetailDO> {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-预算分解id")
    @TableField("bgt_breakdown_main_id")
    private Integer bgtBreakdownMainId;

    @Schema(description = "数据-返利项目代码")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    @TableField("classItemName")
    private String classItemName;

    @Schema(description = "数据-兑付依据（00-aak，01-std）")
    @TableField("sale_type")
    private String saleType;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    @TableField("bgt_type")
    private String bgtType;

    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    @Schema(description = "数据-车系点数")
    @TableField("series_point")
    private BigDecimal seriesPoint;

    @Schema(description = "数据-返利项目预算总点数（常规）")
    @TableField("classItem_total_point")
    private BigDecimal classitemTotalPoint;

    @Schema(description = "数据-返利项目预算使用点数（常规）")
    @TableField("classItem_used_point")
    private BigDecimal classitemUsedPoint;

    @Schema(description = "数据-返利项目预算余额点数（常规）")
    @TableField("classItem_point_balance")
    private BigDecimal classitemPointBalance;

    @Schema(description = "数据-返利项目预算总金额（专项）")
    @TableField("classItem_total_amount")
    private BigDecimal classitemTotalAmount;

    @Schema(description = "数据-返利项目已使用金额（专项）")
    @TableField("classItem_used_amount")
    private BigDecimal classitemUsedAmount;

    @Schema(description = "数据-返利项目金额余额（专项）")
    @TableField("classItem_amount_balance")
    private BigDecimal classitemAmountBalance;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Schema(description = "数据-版本")
    @TableField(exist = false)
    private String version;

    @Schema(description = "数据-细分年份")
    @TableField(exist = false)
    private String breakdownYear;

    @Schema(description = "数据-细分季度")
    @TableField(exist = false)
    private String breakdownQuarter;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
