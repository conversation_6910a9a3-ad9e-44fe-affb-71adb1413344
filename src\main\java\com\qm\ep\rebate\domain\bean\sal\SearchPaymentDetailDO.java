package com.qm.ep.rebate.domain.bean.sal;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * 查询收款明细
 *
 *
 * <AUTHOR>
 * @since 2021-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "数据:查询收款明细")
public class SearchPaymentDetailDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID S_salb061.nextval")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据-系统单号 编号规则 CREDITNO")
    private String vsysbillno;

    @Schema(description = "数据-入账票据号")
    private String vbillno;

    @Schema(description = "数据-公司ID")
    private String ncompanyid;

    @Schema(description = "数据-单位标识：1客户/2经销商")
    private String vunitflag;

    @Schema(description = "数据-客户ID")
    private String ncustomerid;

    @Schema(description = "数据-信用账户")
    private String vcreditacct;

    @Schema(description = "数据-信用账户名称")
    private String vcreditaccttext;

    @Schema(description = "数据-账户类别")
    private String vacctclass;

    @Schema(description = "数据-账户类型")
    private String vaccttype;

    @Schema(description = "数据-收款方式")
    private String vgatheringmodedesc;

    @Schema(description = "数据-返利项目")
    private String vclassitem;

    @Schema(description = "数据-金额")
    private Double nbalance;

    @Schema(description = "数据-运营部审核日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dcoo;

    @Schema(description = "数据-操作员ID")
    private String noperatorid;

    @Schema(description = "数据-操作员编码")
    private String voperatorcode;

    @Schema(description = "数据-操作员名称")
    private String voperatorname;

    @Schema(description = "数据-操作日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date doperatedate;


    @Schema(description = "数据-确认标识")
    private String visconfirm;

    @Schema(description = "数据-入账日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dpostingdate;

    @Schema(description = "数据-备注")
    private String vremark;

    @Schema(description = "数据-银行")
    private String vbank;

    @Schema(description = "数据-银行名称")
    private String vbankname;

    @Schema(description = "数据-银行账号1")
    private String vmarkforbank;

    @Schema(description = "数据-银行账号")
    private String vbankzh;

    @Schema(description = "数据-银行流水号")
    private String vbanklsh;

    @Schema(description = "数据-融资申请单编号 salc107c")
    private String vfinanceno;

    @Schema(description = "数据-单位ID")
    private String customer;

    @Schema(description = "数据-信用账户")
    private String credit;

    @Schema(description = "数据-起始日期")
    private String begin;

    @Schema(description = "数据-截止日期")
    private String end;

    @Schema(description = "数据-经销商代码")
    private String vdealer;

    @Schema(description = "数据-经销商代码名称")
    private String vdealertext;

    @Schema(description = "数据-返利项目代码")
    private String vclassitemcode;

    @Schema(description = "数据-返利项目名称")
    private String vclassitemtext;

    @Schema(description = "数据-vin码")
    private String vvin;

    @Schema(description = "数据-系列")
    private String vproduct;

    @Schema(description = "数据-金额和")
    private String nbalanceSum;

    @Schema(description = "数据-总数")
    private Integer total = 0;

    @Schema(description = "数据-开始子票区间")
    private String vbr1;

    @Schema(description = "数据-截止子票区间")
    private String vbr2;
    @Schema(description = "数据-大区")
    private String vinsttext2;

}
