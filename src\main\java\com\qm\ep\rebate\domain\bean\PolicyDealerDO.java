package com.qm.ep.rebate.domain.bean;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 *
 * 政策关联经销商表
 *
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy_dealer")
@Schema(description = "数据:政策关联经销商表")
public class PolicyDealerDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-主表ID")
    @TableField("POLICY_ID")
    private String policyId;

    @Schema(description = "数据-经销商代码")
    @TableField("DEALER_CODE")
    @Excel(name = "经销商代码", orderNum = "1")
    private String dealerCode;

//    @Excel(name = "品牌", orderNum = "1")
    @Schema(description = "数据-品牌")
    @TableField("BRAND")
    private String brand;

    @Schema(description = "数据-年月")
    @TableField("yearMonth")
    private String yearMonth;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-经销商名称")
    @TableField(value = "DEALER_NAME", exist = false)
    @Excel(name = "经销商名称", orderNum = "1")
    private String dealerName;

    @Schema(description = "数据-失败原因")
    @TableField(value = "reseaon", exist = false)
    private String reseaon;
}
