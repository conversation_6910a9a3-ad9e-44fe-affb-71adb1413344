package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.ep.rebate.enumerate.AccountEntryMethodEnum;
import com.qm.ep.rebate.enumerate.PolicyCalculateStatusEnum;
import com.qm.ep.rebate.enumerate.YesOrNoEnum;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 *  
 * 政策基础信息表
 *  
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy")
@Schema(description = "数据:政策基础信息表")
public class PolicyDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策代码")
    @TableField("VPOLICYCODE")
    private String vpolicycode;

    @Schema(description = "数据-政策名称")
    @TableField("VPOLICYNAME")
    private String vpolicyname;

    @Schema(description = "数据-政策描述")
    @TableField("VPOLICYDESC")
    private String vpolicydesc;

    @Schema(description = "数据-政策主键")
    @TableField("publishedId")
    private String publishedId;

    @Schema(description = "数据-经销商备注")
    @TableField("dealerRemark")
    private String dealerRemark;

    @Schema(description = "数据-政策开始时间")
    @TableField("DBEGIN")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @Schema(description = "数据-政策结束时间")
    @TableField("DEND")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @Schema(description = "数据-关联车型")
    @TableField("VAPPLYPRDT")
    private String vapplyprdt;

    @Schema(description = "数据-关联经销商")
    @TableField("VAPPLYORG")
    private String vapplyorg;

    @Schema(description = "数据-特殊车辆设定")
    @TableField("VTSCARTYPE")
    private String vtscartype;

    /**
     * 完成状态：00-录入，01-配置完成，20-提交，25-经理审核，30-发布，99-弃用
     */
    @Schema(description = "数据-完成状态")
    @TableField("VFINISHSTATE")
    private String vfinishstate;

    @Schema(description = "数据-计算状态")
    @TableField("calculateStatus")
    private PolicyCalculateStatusEnum calculateStatus;

    /**
     * 红旗：1-普通，2-追溯，3-预结，4-预结扣回
     * 博行：01-普通返利，02-模糊返利，03-额外返利
     */
    @Schema(description = "数据-结算类型")
    @TableField("VSMTTYPE")
    private String vsmttype;

    @Schema(description = "数据-复制来源")
    @TableField("copyFrom")
    private String copyFrom;

    @Schema(description = "数据-返利项目代码，来源：维护返利项目")
    @TableField("classItemCode")
    private String classItemCode;

    @Schema(description = "数据-返利项目名称")
    @TableField(value = "classItemName", exist = false)
    private String classItemName;

    @Schema(description = "数据-返利项目代码,名称")
    @TableField(value = "classItemCodeName", exist = false)
    private String classItemCodeName;

    @Schema(description = "数据-返利入账方式，字典项：RBTPOLICYACCT")
    @TableField("accountEntryMethod")
    private AccountEntryMethodEnum accountEntryMethod;

    @Schema(description = "数据-业务类型")
    @TableField("businessType")
    private String businessType;

    @Schema(description = "数据-配置完成时间")
    @TableField(value = "configCompletedOn", updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date configCompletedOn;

    @Schema(description = "数据-是否是政策模板")
    @TableField("isTemplate")
    private YesOrNoEnum isTemplate;

    @Schema(description = "数据-组织代码")
    @TableField("orgCode")
    private String orgCode;

    @Schema(description = "数据-目标版本")
    @TableField("aimVersion")
    private String aimVersion;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新时间")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-提交人")
    @TableField(value = "submitBy", fill = FieldFill.INSERT)
    private String submitBy;

    @Schema(description = "数据-提交人代码")
    @TableField(value = "submitCode", fill = FieldFill.INSERT)
    private String submitCode;

    @Schema(description = "数据-创建人代码")
    @TableField(value = "CREATEBYCODE", exist = false)
    private String createByCode;

    @Schema(description = "数据-创建人姓名")
    @TableField(value = "CREATEBYNAME", exist = false)
    private String createByName;

    @Schema(description = "数据-修改人姓名")
    @TableField(value = "UPDATEBYNAME", exist = false)
    private String updateByName;

    /**
     * 通过该字段判断，当前用户是否可以在页面上对该政策编辑
     */
    @Schema(description = "数据-是否可编辑")
    @TableField("editable")
    private boolean editable;

    @Schema(description = "数据-审核类型")
    @TableField(exist = false)
    private String auditType;

    @Schema(description = "数据-政策创建")
    @TableField("policyCreate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date policyCreate;

    @Schema(description = "数据-政策文件发布")
    @TableField("filePublish")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date filePublish;

    @Schema(description = "数据-政策配置日期（配置完成）")
    @TableField("policyFinish")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date policyFinish;

    @Schema(description = "数据-政策配置发布")
    @TableField("policyPublish")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date policyPublish;

    @Schema(description = "数据-政策计算")
    @TableField("policyCalc")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date policyCalc;

    @Schema(description = "数据-兑付申请（申请入账）")
    @TableField("applyEntry")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date applyEntry;

    @Schema(description = "数据-财务兑付完成日期（待确认）")
    @TableField("enrtyFinish")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date enrtyFinish;


    @Schema(description = "数据-预算金额")
    @TableField(value = "budgetAmount")
    private BigDecimal budgetAmount;

    @Schema(description = "数据-实际结算金额")
    @TableField(value = "actualAmount")
    private BigDecimal actualAmount;


    @Schema(description = "数据-政策周期")
    @TableField(value = "policyCycle")
    private String policyCycle;

    @Schema(description = "数据-政策类型")
    @TableField(value = "policyType")
    private String policyType;

    @Schema(description = "数据-是否校验VIN码")
    @TableField(value = "checkVinFlag")
    private YesOrNoEnum checkVinFlag;

    @Schema(description = "数据-兑付周期")
    @TableField(value = "cashCycle")
    private String cashCycle;

    @Schema(description = "数据-兑付代码")
    @TableField(value = "cashCode")
    private String cashCode;

    @Schema(description = "数据-兑付方式")
    @TableField(value = "cashMode")
    private String cashMode;

    @Schema(description = "数据-一审备注")
    @TableField(value = "firstRemark")
    private String firstRemark;

    @Schema(description = "数据-二审备注")
    @TableField(value = "secondRemark")
    private String secondRemark;

    @Schema(description = "数据-政策代码（红旗新增）")
    @TableField(value = "vpolicycodehq")
    private String vpolicycodehq;

    @Schema(description = "数据-品牌")
    @TableField(value = "brand")
    private String brand;

    @Schema(description = "数据-预估月份")
    @TableField(value = "predictMonth")
    private String predictMonth;

    @Schema(description = "数据-税率-bx")
    @TableField(value = "taxRates")
    private String taxRates;

    @Schema(description = "数据-是否参与试算  1是，0否")
    @TableField(value = "canTrial")
    private Integer canTrial;

    @Schema(description = "数据-任务实例编码")
    @TableField(value = "taskinsanceCode")
    private String taskinsanceCode;

    @Schema(description = "数据-任务end实例编码")
    @TableField(value = "taskinsanceEndCode")
    private String taskinsanceEndCode;

    @Schema(description = "数据-任务流实例编码")
    @TableField(value = "taskFlowinstanceCode")
    private String taskFlowinstanceCode;

    @Schema(description = "数据-任务来源0 任务流 1新建")
    @TableField(value = "dataResource")
    private String dataResource;

    @Schema(description = "数据-审批流主键")
    @TableField(value = "processInstanceId")
    private String processInstanceId;

    @Schema(description = "数据-预算是否释放（0-不释放，1-释放）")
    @TableField(value = "bgt_release")
    private String bgtRelease;

    @Schema(description = "数据-政策发布时间")
    @TableField(value = "publish_time")
    private String publishTime;


    /**
     * 预算金额
     */
    @Schema(description = "数据-预算金额")
    @TableField(exist = false)
    private BigDecimal totalAmount;
    /**
     * 返利项目车系余额
     */
    @Schema(description = "数据-返利项目车系余额")
    @TableField(exist = false)
    private BigDecimal classItemAmountBalance;

    /**
     * 预算类型
     */
    @Schema(description = "数据-预算类型")
    @TableField(exist = false)
    private String bgtType;


    @Schema(description = "数据-存在应用")
    @TableField(exist = false)
    private String existApply;

    @Schema(description = "数据-政策主键")
    @TableField(exist = false)
    private String policyId;

    @Schema(description = "数据-员工主键")
    @TableField(exist = false)
    private String employeeId;

    @Schema(description = "数据-部门id")
    @TableField(value = "department")
    private String department;

    @Schema(description = "数据-部门名称")
    @TableField(value = "departmentName")
    private String departmentName;

    /**
     * 复制政策的路径（传1表示是从待办进行基础页的复制）
     */
    @Schema(description = "数据-复制政策的路径（传1表示是从待办进行基础页的复制）")
    @TableField(exist = false)
    private String copySourceFromTask;

    @Schema(description = "数据-策略发布 vPolicy 代码")
    @TableField(exist = false)
    private String policyPublishedVPolicyCode;
    @Schema(description = "数据-申请时间")
    @TableField(exist = false)
    private String applyTime;
    @Schema(description = "数据-造物主")
    @TableField(exist = false)
    private String creator;

    @Schema(description = "试算类型（aak-std-invoice）")
    @TableField(value = "trail_type")
    private String trailType;

    @Schema(description = "是否编辑")
    @TableField(exist = false)
    private String isCreate;


}
