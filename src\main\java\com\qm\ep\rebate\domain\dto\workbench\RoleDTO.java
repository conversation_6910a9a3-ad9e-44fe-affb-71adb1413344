package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "数据:实体类-角色 DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoleDTO {

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-登录用户 ID")
    private String loginUserId;

    @Schema(description = "数据-法典")
    private String code;

    @Schema(description = "数据-名字")
    private String name;

    @Schema(description = "数据-姓名 en")
    private String nameEn;

    @Schema(description = "数据-多语言描述")
    private String multilingualDesc;

    @Schema(description = "数据-Multilingual desc en")
    private String multilingualDescEn;

    @Schema(description = "数据-角色名称2")
    private String roleName2;

    @Schema(description = "数据-角色名称3")
    private String roleName3;

    @Schema(description = "数据-角色名称4")
    private String roleName4;

    @Schema(description = "数据-系统 ID")
    private String systemId;

    @Schema(description = "数据-博士")
    private String dr;

    @Schema(description = "数据-钥匙")
    private String key;

    @Schema(description = "数据-用户 ID")
    private String userId;

    @Schema(description = "数据-角色 ID")
    private String roleId;

    @Schema(description = "数据-角色")
    private String roles;

    @Schema(description = "数据-用户")
    private String users;

    @Schema(description = "数据-有效类型")
    private Integer validType;

    @Schema(description = "数据-开始时间")
    private String beginTime;

    @Schema(description = "数据-结束时间")
    private String endTime;

    @Schema(description = "数据-角色类型")
    private String roleType;

    @Schema(description = "数据-角色起源")
    private String roleOrigin;

    @Schema(description = "数据-父角色 ID")
    private String parentRoleId;

}
