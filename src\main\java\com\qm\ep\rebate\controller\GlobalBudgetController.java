package com.qm.ep.rebate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.GlobalBudgetDO;
import com.qm.ep.rebate.domain.dto.GlobalBudgetDTO;
import com.qm.ep.rebate.service.GlobalBudgetResultService;
import com.qm.ep.rebate.service.GlobalBudgetService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/globalBudget")
@Tag(name = "总体预算编制", description = "[author: 10200571]")
public class GlobalBudgetController extends BaseController {

    @Resource
    private GlobalBudgetService globalBudgetService;

    @Resource
    private GlobalBudgetResultService globalBudgetResultService;

    @Operation(summary = "查询总体预算编制列表", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<GlobalBudgetDO>> table(@RequestBody GlobalBudgetDTO globalBudgetDTO){
        JsonResultVo<QmPage<GlobalBudgetDO>> jsonResultVo = new JsonResultVo<>();

        QmQueryWrapper<GlobalBudgetDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<GlobalBudgetDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.like(StrUtil.isNotBlank(globalBudgetDTO.getBudgetModel()), GlobalBudgetDO::getBudgetModel, globalBudgetDTO.getBudgetModel());
        lambdaWrapper.eq(StrUtil.isNotBlank(globalBudgetDTO.getBudgetVersion()), GlobalBudgetDO::getBudgetVersion, globalBudgetDTO.getBudgetVersion());
        lambdaWrapper.in(CollUtil.isNotEmpty(globalBudgetDTO.getOrgCodes()), GlobalBudgetDO::getOrgCode, globalBudgetDTO.getOrgCodes());
        lambdaWrapper.in(CollUtil.isNotEmpty(globalBudgetDTO.getStatuses()), GlobalBudgetDO::getStatus, globalBudgetDTO.getStatuses());
        QmPage<GlobalBudgetDO> data = globalBudgetService.table(queryWrapper, globalBudgetDTO);
        jsonResultVo.setData(data);
        return jsonResultVo;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<GlobalBudgetDO> save(@RequestBody GlobalBudgetDTO globalBudgetDTO){
        JsonResultVo<GlobalBudgetDO> ret = new JsonResultVo<>();

        GlobalBudgetDO globalBudgetDO = new GlobalBudgetDO();
        if(StrUtil.isNotBlank(globalBudgetDTO.getId())) {
            globalBudgetDO = globalBudgetService.getById(globalBudgetDTO.getId());
        }

        BeanUtil.copyProperties(globalBudgetDTO, globalBudgetDO);

        globalBudgetService.saveOrUpdate(globalBudgetDO);
        ret.setData(globalBudgetDO);
        return ret;
    }


    @Operation(summary = "获取详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<GlobalBudgetDO> getDetail(@RequestBody GlobalBudgetDTO globalBudgetDTO){
        JsonResultVo<GlobalBudgetDO> ret = new JsonResultVo<>();
        GlobalBudgetDO globalBudgetDO = globalBudgetService.getById(globalBudgetDTO.getId());
        ret.setData(globalBudgetDO);
        return ret;
    }

    @Operation(summary = "删除", description = "[author: 10200571]")
    @PostMapping("/deleteByIds")
    public JsonResultVo<Boolean> deleteByIds(@RequestBody List<String> ids){
        JsonResultVo<Boolean> ret = new JsonResultVo<>();
        if(CollUtil.isEmpty(ids)) {
            ret.setMsgErr("删除失败！ID列表不能为空！");
        } else {
            LambdaQueryWrapper<GlobalBudgetDO> wrapper = new QmQueryWrapper<GlobalBudgetDO>().lambda();
            wrapper.in(GlobalBudgetDO::getId, ids);
            boolean remove = globalBudgetService.remove(wrapper);
            ret.setData(remove);
        }
        return ret;
    }

    @Operation(summary = "插入其他收益标识", description = "[author: 10200571]")
    @PostMapping("/updateOtherProfitStatus")
    public JsonResultVo<Boolean> updateOtherProfitStatus(@RequestBody GlobalBudgetDTO globalBudgetDTO){
        JsonResultVo<Boolean> ret = new JsonResultVo<>();
        GlobalBudgetDO budget = globalBudgetService.getById(globalBudgetDTO.getId());
        budget.setInsertOtherProfit(globalBudgetDTO.getInsertOtherProfit());
        boolean saveOk = globalBudgetService.saveOrUpdate(budget);
        ret.setData(saveOk);
        return ret;
    }

    @Operation(summary = "下达总体预算编制", description = "[author: 10200571]")
    @PostMapping("/send")
    public JsonResultVo<Boolean> send(@RequestBody GlobalBudgetDTO globalBudgetDTO){
        JsonResultVo<Boolean> ret = new JsonResultVo<>();
        GlobalBudgetDO budget = globalBudgetService.getById(globalBudgetDTO.getId());
        budget.setSendDate(new Date());
        budget.setStatus("02");
        boolean saveOk = globalBudgetService.saveOrUpdate(budget);
        globalBudgetDTO.setModelCalcVersion(budget.getModelCalcVersion());
        globalBudgetDTO.setInsertOtherProfit(budget.getInsertOtherProfit());
        saveOk = globalBudgetResultService.saveResult(globalBudgetDTO);
        ret.setData(saveOk);
        return ret;
    }
}
