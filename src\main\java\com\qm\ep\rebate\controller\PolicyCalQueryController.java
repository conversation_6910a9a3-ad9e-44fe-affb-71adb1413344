package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.JobDTO;
import com.qm.ep.rebate.domain.dto.PolicyCalQueryDTO;
import com.qm.ep.rebate.domain.dto.TriggerDTO;
import com.qm.ep.rebate.domain.vo.PolicyCalQueryVO;
import com.qm.ep.rebate.enumerate.BgtReleaseStatusEnum;
import com.qm.ep.rebate.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebate.mapper.PolicyBudgetMapper;
import com.qm.ep.rebate.mapper.PolicyCalQueryMapper;
import com.qm.ep.rebate.mapper.PolicyMapper;
import com.qm.ep.rebate.remote.feign.RebateSchedulerFeignClient;
import com.qm.ep.rebate.service.BusinessConstructionService;
import com.qm.ep.rebate.service.BusinessConstructionTwoService;
import com.qm.ep.rebate.service.SysPersonOrgService;
import com.qm.ep.rebate.service.SystemConfigService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.TableUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
*  
*Controller
* 计算方案主表JsonResultVo
*
* <AUTHOR>
* @since 2022-06-09
*/
@Slf4j
@Tag(name ="计算方案主表")
@RestController
@RequestMapping("/policyCalQuery")
public class PolicyCalQueryController extends BaseController {
    @Autowired
    private PolicyBudgetMapper policyBudgetMapper;

    @Autowired
    private ExecFormalCalcHistoryMapper execFormalCalcHistoryMapper;
    @Autowired
    private PolicyCalQueryMapper policyCalQueryMapper;
    @Autowired
    private PolicyMapper policyMapper;
    @Resource
    private RebateSchedulerFeignClient rebateSchedulerFeignClient;
    @Resource
    private BusinessConstructionService businessConstructionService;
    @Resource
    private BusinessConstructionTwoService businessConstructionTwoService;
    @Resource
    private SystemConfigService systemConfigService;
    @Autowired
    private SysPersonOrgService sysPersonOrgService;

    /**
     * 政策发布入账列表页查询
     */
    @Operation(summary = "政策发布入账列表页查询", description = "[author: ********]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<PolicyCalQueryVO>> table(@RequestBody PolicyCalQueryDTO request){

        JsonResultVo<QmPage<PolicyCalQueryVO>> ret = new JsonResultVo<>();
        QmQueryWrapper<PolicyCalQueryDO> queryWrapper = new QmQueryWrapper<>();
        TableUtils.appendTableAdditional(queryWrapper,request, PolicyCalQueryDO.class);
        IPage<PolicyCalQueryDO> queryPage = TableUtils.convertToIPage(request);

        // 权限控制
        if( "1".equals(request.getSearchSelf())){
            request.setCreateby(getUserInfo().getOperatorId());
        }

        IPage<PolicyCalQueryVO> list = policyCalQueryMapper.getList(queryPage,queryWrapper,request);

        // 从A04服务获取定时任务状态并填充
        this.fillScheduleState(list);

        // 填充政策预算释放状态
        this.fillBgtInfo(list.getRecords());

        ret.setData(TableUtils.convertQmPageFromMpPage(list));
        return ret;
    }

    private void fillBgtInfo(List<PolicyCalQueryVO> pageList) {

        for (PolicyCalQueryVO policyCalQueryVO : pageList) {
            // 获取政策释放状态
            String policyId = policyCalQueryVO.getPolicyid();
            PolicyDO policyDO = policyMapper.selectById(policyId);
            List<PolicyBudgetDO> pbs = policyBudgetMapper.selectListByPolicyId(policyDO.getId(), policyDO.getClassItemCode());
            BigDecimal sum = pbs.stream().map(PolicyBudgetDO::getAmountBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            String bgtRelease = policyDO.getBgtRelease();
            if ("3".equals(bgtRelease)) {
                policyCalQueryVO.setReleaseStatus(BgtReleaseStatusEnum.RELEASED.getCode());
                policyCalQueryVO.setBgtAmountBalance(BigDecimal.ZERO);
            }
            // null  不可释放
            if (StringUtils.isBlank(bgtRelease)) {
                policyCalQueryVO.setReleaseStatus(BgtReleaseStatusEnum.PROHIBIT_RELEASABLE.getCode());
                policyCalQueryVO.setBgtAmountBalance(sum);
            }
            List<ExecFormalCalcHistoryDO> histories = execFormalCalcHistoryMapper.selectApplyingEntry(policyId);


            //  “0” 未释放，余额为政策balance
            if ("0".equals(bgtRelease)){
                // 存在入账中
                if (CollUtil.isNotEmpty(histories)){
                    policyCalQueryVO.setReleaseStatus(BgtReleaseStatusEnum.PROHIBIT_RELEASABLE.getCode());
                }else{
                    policyCalQueryVO.setReleaseStatus(BgtReleaseStatusEnum.UNRELEASED.getCode());
                }
                policyCalQueryVO.setBgtAmountBalance(sum);
            }
            //  “1”  入账中-不可释放，余额为balance；入账完成-已释放，余额为0

            if ("1".equals(bgtRelease)){
                if (CollUtil.isNotEmpty(histories)){
                    policyCalQueryVO.setReleaseStatus(BgtReleaseStatusEnum.PROHIBIT_RELEASABLE.getCode());
                    policyCalQueryVO.setBgtAmountBalance(sum);
                }else{
                    policyCalQueryVO.setReleaseStatus(BgtReleaseStatusEnum.RELEASED.getCode());
                    policyCalQueryVO.setBgtAmountBalance(BigDecimal.ZERO);
                }
            }
            // 不存在完成的和申请中的入账，就展示前端取消发布的按钮
            List<ExecFormalCalcHistoryDO> rejects = execFormalCalcHistoryMapper.selectUnApplyAndReject(policyId);
            if (CollUtil.isEmpty(rejects)){
                policyCalQueryVO.setShowCancelPublish("1");
            }
        }
    }

    private void fillScheduleState(IPage<PolicyCalQueryVO> list) {
        List<JobDTO> jobs = list.getRecords().stream().filter(i -> BootAppUtil.isnotNullOrEmpty(i.getJobId())).map(i -> {
            JobDTO job = new JobDTO();
            job.setId(i.getJobId());
            return job;
        }).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(jobs)) {
            JsonResultVo<Map<String, List<TriggerDTO>>> jobsTriggers = rebateSchedulerFeignClient.jobsTriggers(jobs);
            Map<String, String> schedulerStateMap = new HashMap<>(6);
            schedulerStateMap.put("NONE", "无状态");
            schedulerStateMap.put("NORMAL", "运行中");
            schedulerStateMap.put("PAUSED", "暂停");
            schedulerStateMap.put("COMPLETE", "完成");
            schedulerStateMap.put("ERROR", "出错");
            schedulerStateMap.put("BLOCKED", "等待");
            list.getRecords().forEach(i->{
                if(Objects.isNull(jobsTriggers) || Objects.isNull(jobsTriggers.getData())) {
                    i.setSchedulerState("未配置");
                }else{
                    List<TriggerDTO> triggers = jobsTriggers.getData().get(i.getJobId());
                    if(null!=triggers) {
                        Map<String, Integer> countMap = triggers.stream().collect(Collectors.toMap(TriggerDTO::getState, k -> 1, Integer::sum));
                        List<String> countStr = new ArrayList<>();
                        countMap.forEach((k, v)-> countStr.add(schedulerStateMap.get(k) + "(" + v + ")"));
                        i.setSchedulerState(StringUtils.join(countStr, ";"));
                    } else {
                        i.setSchedulerState("未配置");
                    }
                }
            });
        } else {
            list.getRecords().forEach(i-> i.setSchedulerState("未配置"));
        }
    }

    @Operation(summary = "保存", description = "[author: ********]")
    @PostMapping("/saveSchedule")
    public JsonResultVo<PlanQuartzDO> saveSchedule(@RequestBody PlanQuartzDO tempDO){
        JsonResultVo<PlanQuartzDO> ret = new JsonResultVo<>();
        PlanQuartzDO list = policyCalQueryMapper.getPlanSchedule(tempDO);
        if(BootAppUtil.isNullOrEmpty(list)){
            // 新建
            policyCalQueryMapper.insertSchedule(tempDO);
        }
        ret.setData(tempDO);
        return ret;
    }

    @Operation(summary = "由删除任务feign调用，删除计算方案与任务的对应关系", description = "[author: ********]")
    @PostMapping("/deleteSchedule")
    public JsonResultVo deleteSchedule(@RequestBody String jobId){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        policyCalQueryMapper.deleteScheduleByJobId(jobId);
        return ret;
    }

    @Operation(summary = "验证所选底表与方案合并输出字段是否完全一致", description = "[author: ********]")
    @PostMapping("/verify")
    public JsonResultVo verify(@RequestBody PolicyCalQueryDTO tempDTO){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        QmQueryWrapper<BusinessConstructionDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<BusinessConstructionDO> wrapper = queryWrapper.lambda();
        wrapper.eq(BusinessConstructionDO::getTableName, tempDTO.getBusinessTableName());
        List<BusinessConstructionDO> businessConstructionDOS = businessConstructionService.list(wrapper);
        Set<String> businessTableFields =businessConstructionDOS.stream().map(BusinessConstructionDO::getFieldName).collect(Collectors.toSet());


        QmQueryWrapper<BusinessConstructionTwoDO> queryWrapper2 = new QmQueryWrapper<>();
        LambdaQueryWrapper<BusinessConstructionTwoDO> wrapper2 = queryWrapper2.lambda();
        wrapper2.eq(BusinessConstructionTwoDO::getTableName, tempDTO.getCombinationname());
        List<BusinessConstructionTwoDO> businessConstructionTwoDOS = businessConstructionTwoService.list(wrapper2);
        Set<String> combinationFields =businessConstructionTwoDOS.stream().map(BusinessConstructionTwoDO::getFieldName).collect(Collectors.toSet());

        List<String> disjunction = new ArrayList<>(CollUtil.disjunction(businessTableFields, combinationFields));
        if(disjunction.isEmpty()){
            ret.setMsg("所选底表需包含“年月”字段！");
        }else{
            if(disjunction.size() !=1 || !"年月".equals(disjunction.get(0))){
                // 方案合并输出字段 与 所选底表字段 仅差“年月”
                ret.setMsg("所选底表与方案合并输出字段不一致，请重新选择！");
            }
        }
        return ret;
    }
    @Operation(summary = "验证所选方案是否有申请入账成功的计算结果", description = "[author: ********]")
    @PostMapping("/verifyEnterAccount")
    public JsonResultVo verifyEnterAccount(@RequestBody PolicyCalQueryDTO tempDTO){
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        int count = policyCalQueryMapper.getEnterAccount(tempDTO.getObjectId());
        if(count==0){
            resultVo.setMsg("请先手动申请入账，成功后方可设置自动入账！");
        }
        return resultVo;
    }

    @Operation(summary = "更新JobData", description = "[author: ********]")
    @PostMapping("/updateScheduleJobData")
    public JsonResultVo<String> updateScheduleJobData(@RequestBody PlanQuartzDO tempDO){
        JsonResultVo<String> ret = new JsonResultVo<>();
        policyCalQueryMapper.updateScheduleJobData(tempDO);
        return ret;
    }

}
