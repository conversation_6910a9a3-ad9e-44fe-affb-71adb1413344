package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "政策节点跟踪查询")
@Data
public class TimeAxisDTO {

    @Schema(description = "政策代码")
    private String policyCode;

    @Schema(description = "政策名称")
    private String policyName;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "节点状态")
    private String stage;

    @Schema(description = "逾期时长")
    private String overdue;

    @Schema(description = "创建部门")
    private String department;

    @Schema(description = "创建人")
    private String createby;

    @Schema(description = "政策产品")
    private String series;

    @Schema(description = "预算金额")
    private BigDecimal budgetAmount;

    @Schema(description = "实际结算金额")
    private BigDecimal actualAmount;

    @Schema(description = "结算差额")
    private BigDecimal difference;

    @Schema(description = "执行开始时间")
    private LocalDateTime dbegin;

    @Schema(description = "执行结束时间")
    private LocalDateTime dend;

    @Schema(description = "政策发布")
    private LocalDateTime publishDate;

    @Schema(description = "政策配置发布")
    private LocalDateTime policyPublish;

    @Schema(description = "政策配置")
    private LocalDateTime policyFinish;

    @Schema(description = "财务应兑付完成日期")
    private LocalDateTime applyEntry;

    @Schema(description = "财务兑付完成")
    private LocalDateTime enrtyFinish;

}
