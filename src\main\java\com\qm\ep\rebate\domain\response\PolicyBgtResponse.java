package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "数据:策略 BGT 响应")
@Data
public class PolicyBgtResponse {
    /**
     * 政策预算申请总金额
     */
    @Schema(description = "数据-政策预算申请总金额")
    private BigDecimal totalApplyAmount;
    /**
     * 预算分配
     */
    @Schema(description = "数据-预算分配")
    private List<ClassItemBgtBalance> main;
    /**
     * 预算驳回原因
     */
    @Schema(description = "数据-预算驳回原因")
    private String applyRejectReason;

    /**
     * 政策是否已经分配了预算
     */
    @Schema(description = "政策是否已经分配了预算 其他-未分配 1-已分配")
    private String done;
}
