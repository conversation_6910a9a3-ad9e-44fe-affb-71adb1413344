package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 * 返利折让审核授权表
 *
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Getter
@Setter
@TableName("rebate_extract_approve_relation")
public class RebateExtractApproveRelationPO implements Serializable {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审批类型（A-区域审核配置，B-大区审核配置，C-渠道审核配置，D-销售计划部审核配置）
     */
    @TableField("approve_type")
    private String approveType;

    /**
     * 部门编码
     */
    @TableField("dept_code")
    private String deptCode;

    /**
     * 部门名称
     */
    @TableField("department")
    private String department;

    /**
     * 人员域账号
     */
    @TableField("submit_code")
    private String submitCode;

    /**
     * 人员名称
     */
    @TableField("submit_name")
    private String submitName;

    /**
     * 联系方式
     */
    @TableField("phone")
    private String phone;

    /**
     * 经销商代码
     */
    @TableField("dealer_code")
    private String dealerCode;

    /**
     * 是否停用（0-未停用，1-停用）
     */
    @TableField("can_stop")
    private String canStop;

    /**
     * 停用时间
     */
    @TableField(value = "stop_time", insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime stopTime;

    /**
     * 操作人
     */
    @TableField("operation_name")
    private String OperationName;

    /**
     * 创建者
     */
    @TableField("CREATEBY")
    private String createBy;

    /**
     * 创建日期
     */
    @TableField("CREATEON")
    private LocalDateTime createOn;

    /**
     * 更新者
     */
    @TableField("UPDATEBY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATEON")
    private LocalDateTime updateOn;

    /**
     * 时间戳
     */
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;


}
