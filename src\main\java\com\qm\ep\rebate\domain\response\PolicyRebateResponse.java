package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "返利看板响应对象")
@Getter
@Setter
public class PolicyRebateResponse {
    /**
     * 政策编码
     */
    @Schema(description = "政策编码")
    private String policyCode;

    /**
     * 政策名称
     */
    @Schema(description = "政策名称")
    private String policyName;

    /**
     * 政策执行开始日期
     */
    @Schema(description = "政策执行开始日期")
    private String dbegin;

    /**
     * 政策执行结束日期
     */
    @Schema(description = "政策执行结束日期")
    private String dend;

    /**
     * 政策部门
     */
    @Schema(description = "政策部门")
    private String department;

    /**
     * 政策创建人
     */
    @Schema(description = "政策创建人")
    private String policyCreator;

    /**
     * 政策产品（系列）
     */
    @Schema(description = "政策产品（系列）")
    private String policyProductSeries;

    /**
     * 政策状态
     */
    @Schema(description = "政策状态")
    private String policyStatus;

    /**
     * 入账时间
     */
    @Schema(description = "入账时间")
    private String applyTime;

    /**
     * 结算金额
     */
    @Schema(description = "结算金额")
    private BigDecimal settlementAmount;

    /**
     * 入账详情
     */
    @Schema(description = "入账详情")
    private String accountingDetails;

    /**
     * 结算说明
     */
    @Schema(description = "结算说明")
    private String settlementDescription;

    /**
     * 计算数据详情
     */
    @Schema(description = "计算数据详情")
    private String computationData;


    /**
     * 所处阶段（政策配置，维护信息配置）
     */
    @Schema(description = "所处阶段（政策配置，维护信息配置）")
    private String phaseType;

    /**
     * 计算状态
     */
    @Schema(description = "计算状态")
    private String finEntryState;


    /**
     * 政策id
     */
    @Schema(description = "政策id")
    private String policyId;

    /**
     * 域账号
     */
    @Schema(description = "域账号")
    private String submitCode;

    /**
     * 第几次申请入账
     */
    @Schema(description = "第几次申请入账")
    private String applyNumberTh;

    /**
     * 政策结算类型
     */
    @Schema(description = "政策结算类型")
    private String settleType;


}





