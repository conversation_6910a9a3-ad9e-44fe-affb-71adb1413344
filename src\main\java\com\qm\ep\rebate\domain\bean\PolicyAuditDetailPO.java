package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 审计结果详情
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Getter
@Setter
@TableName("policy_audit_detail")
public class PolicyAuditDetailPO implements Serializable {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 审计记录主键
     */
    @TableField(value = "policyAuditId")
    private String policyAuditId;

    /**
     * 政策id
     */
    @TableField(value = "policyId")
    private String policyId;



    /**
     * 经销商代码
     */
    @TableField("dealerCode")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @TableField("dealerName")
    private String dealerName;

    /**
     * 车系
     */
    @TableField("series")
    private String series;

    /**
     * vin
     */
    @TableField("vin")
    private String vin;

    /**
     * 返利金额
     */
    @TableField("rebateAmount")
    private String rebateAmount;

    /**
     * 特殊因子名称
     */
    @TableField("factorName")
    private String factorName;

    /**
     * 政策名称
     */
    @TableField("policyName")
    private String policyName;

    /**
     * 创建人
     */
    @TableField("createBy")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("createOn")
    private LocalDateTime createOn;

    /**
     * 更新人
     */
    @TableField("updateBy")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("updateOn")
    private LocalDateTime updateOn;

    /**
     * 时间戳
     */
    @TableField("dtstamp")
    private LocalDateTime dtstamp;


}
