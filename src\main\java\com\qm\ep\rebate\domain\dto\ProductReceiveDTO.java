package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.domain.bean.VproductDO;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:传输接收产品信息")
@Data
public class ProductReceiveDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-产品试图信息List")
    private List<VproductDO> productViewlst;


}