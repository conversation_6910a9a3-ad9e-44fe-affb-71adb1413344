package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *  
 * 车系销量预测表
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_series_sale_count")
@Schema(description = "车系销量表")
public class BgtSeriesSaleCountDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Schema(description = "年度")
    @TableField("sale_year")
    private String saleYear;

    @Schema(description = "月度")
    @TableField("sale_month")
    private String saleMonth;

    @Schema(description = "车系")
    @TableField("series")
    private String series;

    @Schema(description = "销量（aak/std）")
    @TableField("sale_count")
    private Long saleCount;

    @Schema(description = "销售类型（00-aak，01-std）")
    @TableField("sale_type")
    private String saleType;

    @Schema(description = "版本号")
    @TableField("version")
    private String version;

    @Schema(description = "使用状态0未使用，1已使用")
    @TableField("use_status")
    private String useStatus;

    @Schema(description = "创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;



}
