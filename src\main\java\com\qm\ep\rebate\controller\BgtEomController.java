package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.qm.ep.rebate.domain.bean.BgtEomDO;
import com.qm.ep.rebate.domain.request.BgtEomRequest;
import com.qm.ep.rebate.service.BgtEomService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 *
 * EOM年度预算点数与金额表 前端控制器
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Tag(name = "/bgtEom", description = "[author: 10200571]")
@RestController
@RequestMapping("/bgtEom")
public class BgtEomController {

    @Resource
    private BgtEomService bgtEomService;

    @Operation(summary = "接口EOM数据下发接口", description = "[author: 10200571]")
    @PostMapping("/receiveEomData")
    public JsonResultVo<List<BgtEomDO>> receiveEomData(@RequestBody BgtEomRequest bgtEomRequest) {
        JsonResultVo<List<BgtEomDO>> jsonResultVo = new JsonResultVo<>();

        List<BgtEomDO> data = null;
        try {
            if (CollectionUtil.isNotEmpty(bgtEomRequest.getItems())) {
                data = bgtEomService.saveEomData(bgtEomRequest.getItems());
            } else {
                jsonResultVo.setMsg("下发数据为空！");
            }

        } catch (Exception e) {
            throw new QmException("下发数据保存失败！");
        }
        jsonResultVo.setData(data);
        jsonResultVo.setMsg("下发数据保存成功");
        return jsonResultVo;
    }

    @Operation(summary = "接口EOM下发调整数据接口", description = "[author: 10200571]")
    @PostMapping("/receiveEomAdjustData")
    public JsonResultVo<List<BgtEomDO>> receiveEomAdjustData(@RequestBody BgtEomRequest bgtEomRequest) {
        JsonResultVo<List<BgtEomDO>> jsonResultVo = new JsonResultVo<>();
        List<BgtEomDO> bgtEomDOList = null;
        try {
            if (CollectionUtil.isNotEmpty(bgtEomRequest.getItems())) {
                bgtEomDOList = bgtEomService.receiveEomAdjustData(bgtEomRequest.getItems());
                jsonResultVo.setMsg("下发数据保存成功");
            } else {
                jsonResultVo.setMsg("下发数据为空！");
            }

        } catch (Exception e) {
            throw new QmException("下发数据保存失败！");
        }
        jsonResultVo.setData(bgtEomDOList);

        return jsonResultVo;
    }

    @Operation(summary = "主动查询EOM预算分解点数数据接口", description = "[author: 10200571]")
    @PostMapping("/getDimensionInfoByTargetCode")
    public JsonResultVo<List<BgtEomDO>> getDimensionInfoByTargetCode() {
        JsonResultVo<List<BgtEomDO>> jsonResultVo = new JsonResultVo<>();

        jsonResultVo.setData(bgtEomService.getDimensionInfoByTargetCode());

        return jsonResultVo;
    }
}
