package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *  
 * 
 *  
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Getter
@Setter
@TableName("class_item_log")
public class ClassItemLogPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @TableField("classitem_code")
    private String classitemCode;


    @TableField("classitem_name")
    private String classitemName;

    /**
     * 列名
     */
    @TableField("column_name")
    private String columnName;

    /**
     * 修改前值
     */
    @TableField("pre_value")
    private String preValue;

    /**
     * 修改后值
     */
    @TableField("modified_value")
    private String modifiedValue;

    /**
     * 操作员姓名
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 操作员时间
     */
    @TableField("operator_time")
    private String operatorTime;

}
