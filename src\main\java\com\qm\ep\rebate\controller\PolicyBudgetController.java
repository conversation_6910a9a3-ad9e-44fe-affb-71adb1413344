package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.common.aop.idempotent.IdempotentLock;
import com.qm.ep.rebate.domain.request.BgtAdjustQueryRequest;
import com.qm.ep.rebate.domain.request.BgtAdjustSaveRequest;
import com.qm.ep.rebate.domain.request.BgtConfigQueryRequest;
import com.qm.ep.rebate.domain.request.BgtConfigSaveRequest;
import com.qm.ep.rebate.domain.response.*;
import com.qm.ep.rebate.service.PolicyBudgetService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * 政策配置-预算分配表 前端控制器
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@RestController
@RequestMapping("/policyBudget")

@Slf4j
@Tag(name = "政策配置基础页和分配页")
public class PolicyBudgetController {

    @Resource
    private PolicyBudgetService policyBudgetService;

    @Operation(summary = "接口：分配页预算和常规返利项目余额显示", description = "[author: 10200571]")
    @PostMapping("/getClassItemBgtBalance")
    public JsonResultVo<PolicyBgtResponse> getClassItemBgtBalance(@RequestBody BgtConfigQueryRequest request) {
        // ValidationUtils.valid(request, BgtConfigQueryRequest.GetClassItemBgtBalance.class);
        JsonResultVo<PolicyBgtResponse> result = new JsonResultVo<>();
        result.setData(policyBudgetService.getClassItemBgtBalance(request));
        return result;
    }

    @Operation(summary = "接口：返利项目常规预算-保存", description = "[author: 10200571]")
    @PostMapping("/saveNormalBgt")
    @IdempotentLock(expression = "#request.policyId", duration = 5L)
    public JsonResultVo<String> saveNormalBgt(@Validated(BgtConfigSaveRequest.SaveNormalBgt.class) @RequestBody BgtConfigSaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(policyBudgetService.saveNormalBgt(request));
        return result;
    }

    @Operation(summary = "接口：重新分配", description = "[author: 10200571]")
    @PostMapping("/reAllocate")
    public JsonResultVo<String> reAllocate(@RequestBody BgtConfigSaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        // ValidationUtils.valid(request, BgtConfigSaveRequest.ReAllocate.class);
        result.setData(policyBudgetService.reAllocate(request));
        return result;
    }

    @Operation(summary = "接口：业务员发起预算调整任务——执行待办跳转到政策配置基础页", description = "[author: 10200571]")
    @PostMapping("/initiateBgtAdjust")
    @IdempotentLock(duration = 5L)
    public JsonResultVo<String> initiateBgtAdjust(@RequestBody BgtConfigSaveRequest request) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setData(policyBudgetService.initiateBgtAdjust(request));
        return result;
    }

    @Operation(summary = "接口：创建预算调整-复制季度预算分解主表和明细表", description = "[author: 10200571]")
    @PostMapping("/createBgtAdjustTask")
    @IdempotentLock(duration = 5L)
    public JsonResultVo<BgtAdjustQueryRequest> createBgtAdjustTask(@Validated @RequestBody BgtAdjustSaveRequest request) {
        JsonResultVo<BgtAdjustQueryRequest> result = new JsonResultVo<>();
        result.setData(policyBudgetService.createBgtAdjustTask(request));
        return result;
    }


    @Operation(summary = "接口：政策基础页下一步提示", description = "[author: 10200571]")
    @PostMapping("/nextStepCheck")
    public JsonResultVo<String> nextStepCheck(@RequestParam String policyId, @RequestParam String classItem) {
        JsonResultVo<String> res = new JsonResultVo<>();
        res.setData(policyBudgetService.nextStepCheck(policyId, classItem));
        return res;
    }

    // @Operation(summary = "接口：政策基础页提交校验", description = "[author: 10200571]")
    // @PostMapping("/commitCheck")
    // public JsonResultVo<String> commitCheck(@RequestBody BgtConfigQueryRequest request) {
    //
    //     JsonResultVo<String> res = new JsonResultVo<>();
    //     res.setData(policyBudgetService.commitCheck(request));
    //     return res;
    // }

    @Operation(summary = "接口：政策基础页查看预算金额和返利项目余额", description = "[author: 10200571]")
    @PostMapping("/getPolicyAmountAndBalance")
    public JsonResultVo<PolicyBaseBgtResponse> getPolicyAmountAndBalance(@RequestBody BgtConfigQueryRequest request) {
        JsonResultVo<PolicyBaseBgtResponse> result = new JsonResultVo<>();
        PolicyBaseBgtResponse response = policyBudgetService.getPolicyAmountAndBalance(request);
        result.setData(response);
        return result;
    }

    @Operation(summary = "接口：改变返利项目-清空预算", description = "[author: 10200571]")
    @PostMapping("/clearBgt")
    public JsonResultVo<String> clearBgt(@RequestBody BgtConfigQueryRequest request) {
        policyBudgetService.clearBgt(request);
        return new JsonResultVo<>();
    }


    @Operation(summary = "接口：预算分解调整-调整提示", description = "[author: 10200571]")
    @GetMapping("/clue")
    public JsonResultVo<BgtAdjustQueryResponse> clue(@RequestParam String applyMainId, @RequestParam String bgtType) {
        JsonResultVo<BgtAdjustQueryResponse> res = new JsonResultVo<>();
        res.setData(policyBudgetService.clue(applyMainId, bgtType));
        return res;
    }

    @Operation(summary = "接口：预算释放", description = "[author: 10200571]")
    @GetMapping("/release")
    public JsonResultVo<String> release(@RequestParam String policyId) {
        JsonResultVo<String> res = new JsonResultVo<>();
        res.setData(policyBudgetService.release(policyId));
        return res;
    }

    @Operation(summary = "接口：政策基础页查看计算结果-显示点数", description = "[author: 10200571]")
    @GetMapping("/getFormalCalResults")
    public JsonResultVo<List<ExecCalPointDTO>> getFormalCalResults(@RequestParam String uniqueKey) {

        JsonResultVo<List<ExecCalPointDTO>> result = new JsonResultVo<>();
        // 基础页查看计算结果
        result.setData(policyBudgetService.getFormalCalResults(uniqueKey));
        return result;
    }

    @Operation(summary = "接口：政策基础页查看计算结果-显示金额", description = "[author: 10200571]")
    @GetMapping("/getFormalCalAmountResults")
    public JsonResultVo<List<ExecCalAmountDTO>> getFormalCalAmountResults(@RequestParam String uniqueKey) {

        JsonResultVo<List<ExecCalAmountDTO>> result = new JsonResultVo<>();
        // 基础页查看计算结果
        result.setData(policyBudgetService.getFormalCalAmountResults(uniqueKey));
        return result;
    }

    @Operation(summary = "接口：定时任务轮训进行预算占用转使用&自动释放政策的余额", description = "[author: 10200571]")
    @GetMapping("/occupyToUsedSchedule")
    public JsonResultVo<String> occupyToUsedSchedule() {
        JsonResultVo<String> res = new JsonResultVo<>();
        policyBudgetService.occupyToUsedSchedule();
        policyBudgetService.autoReleaseBalance();
        return res;
    }

    @Operation(summary = "接口：定时任务轮训进行驳回处理", description = "[author: 10200571]")
    @GetMapping("/rejectToReturnSchedule")
    public JsonResultVo<String> rejectToReturnSchedule() {
        JsonResultVo<String> res = new JsonResultVo<>();
        res.setData(policyBudgetService.rejectToReturnSchedule());
        return res;
    }


    @Operation(summary = "接口：控制器接口", description = "[author: 10200571]")
    @GetMapping("/reduce")
    public JsonResultVo<String> reduce(String policyId, String endTime) {
        JsonResultVo<String> res = new JsonResultVo<>();
        res.setData(policyBudgetService.reduce(policyId, endTime));
        return res;
    }


}
