package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "数据:策略 CAL 响应")
@Data
public class ExecCalPointDTO {

    /**
     * 类项目名称
     */
    @Schema(description = "类项目名称")
    private String classItemName;
    /**
     * 车系
     */
    @Schema(description = "车系")
    private String series;
    /**
     * 返利项目预算分解点数
     */
    @Schema(description = "返利项目预算分解点数")
    private BigDecimal classItemTotalPoint;
    /**
     * 返利项目使用点数
     */
    @Schema(description = "返利项目使用点数")
    private BigDecimal classItemUsedPoint;

    @Schema(description = "类项目")
    private String classItem;

    @Schema(description = "数据d 开始")
    private String dBegin;


}
