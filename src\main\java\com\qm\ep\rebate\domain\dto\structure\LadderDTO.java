package com.qm.ep.rebate.domain.dto.structure;

import com.qm.ep.rebate.domain.bean.FormulaDO;
import com.qm.ep.rebate.domain.bean.LadderConditionsDO;
import com.qm.ep.rebate.domain.bean.LadderMainDO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "阶梯对象")
public class LadderDTO {

    @Schema(description = "数据-主结构")
    private LadderMainDO main;

    @Schema(description = "数据-公式内容")
    private List<FormulaDO> contents;

    @Schema(description = "数据-筛选条件")
    private List<LadderConditionsDO> conditions;

}