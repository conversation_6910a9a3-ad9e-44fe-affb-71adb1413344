package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Schema(description = "数据:实体类-任务实例查询 DTO")
@Data
@Builder
public class TaskInstanceQueryDTO {

    /**
     * 能力中心ClientId, String(0-64),获取位置：工作流管理中心->注册能力中心->App key
     */
    @Schema(description = "数据-能力中心ClientId, String(0-64),获取位置：工作流管理中心->注册能力中心->App key")
    private String clientId;

    /**
     * 任务流实例编码
     */
    @Schema(description = "数据-任务流实例编码")
    private String taskFlowInstanceCode;

}
