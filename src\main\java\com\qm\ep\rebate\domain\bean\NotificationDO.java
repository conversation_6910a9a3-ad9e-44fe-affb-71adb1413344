package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("notification")
@Schema(description = "数据:通知对象")
public class NotificationDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-组织代码")
    @TableField("orgCode")
    private String orgCode;

    @Schema(description = "数据-标题")
    @TableField("title")
    private String title;

    @Schema(description = "数据-通知方式")
    @TableField("notifyMethod")
    private String notifyMethod;

    @Schema(description = "数据-状态")
    @TableField("status")
    private String status;

    @Schema(description = "数据-单号")
    @TableField("notifyNo")
    private String notifyNo;

    @Schema(description = "数据-下达人")
    @TableField(value = "sender")
    private String sender;

    @Schema(description = "数据-下达人姓名")
    @TableField(exist = false)
    private String senderName;

    @Schema(description = "数据-下达时间")
    @TableField(value = "sendDate")
    private Date sendDate;

    @Schema(description = "数据-形式背景")
    @TableField("formalBg")
    private String formalBg;

    @Schema(description = "数据-原则")
    @TableField("principle")
    private String principle;

    @Schema(description = "数据-直接接收人")
    @TableField(exist = false)
    private String receiverUsers;

    @Schema(description = "数据-通知由谁转发而来")
    @TableField(exist = false)
    private String deliverUserName;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
