package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@Schema(description = "数据:UserDTO对象")
public class UserDTO extends JsonParamDto {

    /**
     * 单一用户查询，对应接口：getUser、getUserOrgCodes
     */
    @Schema(description = "数据-单一用户查询，对应接口：getUser、getUserOrgCodes")
    private String userId;
    /**
     * 多用户查询，对应接口：getUserList
     */
    @Schema(description = "数据-多用户查询，对应接口：getUserList")
    private List<String> userIds;
    /**
     * 通过组织树ID查询下属人员，对应接口：searchUsers
     */
    @Schema(description = "数据-通过组织树ID查询下属人员，对应接口：searchUsers")
    private String treeId;
    @Schema(description = "数据-回显请求")
    private String echoRequest;
}
