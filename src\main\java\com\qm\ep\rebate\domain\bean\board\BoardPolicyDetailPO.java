package com.qm.ep.rebate.domain.bean.board;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * 政策看板中间表
 *
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Schema(description = "数据:实体类-  政策看板中间表  ")
@TableName("board_policy_detail")
@Data
public class BoardPolicyDetailPO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "数据-主键")
    private String id;

    /**
     * 政策看板id
     */
    @Schema(description = "数据-政策看板id")
    private String boardPolicyId;

    /**
     * 政策id
     */
    @Schema(description = "数据-政策id")
    private String policyId;

    /**
     * 政策所处节点
     */
    @Schema(description = "数据-政策所处节点")
    private String policyNode;

    /**
     * 政策逾期时长
     */
    @Schema(description = "数据-政策逾期时长")
    private Integer policyDelay;

    /**
     * 政策名称
     */
    @Schema(description = "数据-政策名称")
    private String policyName;

    /**
     * 创建人部门
     */
    @Schema(description = "数据-创建人部门")
    private String creatorPart;

    /**
     * 政策创建人
     */
    @Schema(description = "数据-政策创建人")
    private String creator;
    /**
     * 员工id
     */
    @Schema(description = "数据-员工id")
    private String employeeId;

    @Schema(description = "数据-计划完成时间")
    private String planFinishTime;

    @Schema(description = "数据-结算计数")
    private Integer settleCount;

    @Schema(description = "数据-实际结算时间")
    @TableField(exist = false)
    private String actualSettleTime;

}
