package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "数据:RedFlagTrialJoinOnDTO对象")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RedFlagTrialJoinOnDTO extends FilterCondition {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

}
