package com.qm.ep.rebate.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebate.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebate.domain.dto.PolicyCalQueryDTO;
import com.qm.ep.rebate.domain.dto.RebateCalculatorDTO;
import com.qm.ep.rebate.domain.vo.DealerSeriesVO;
import com.qm.ep.rebate.mapper.PolicyCalQueryMapper;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.TableUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;



/**
*
*Controller
* 计算方案主表JsonResultVo
*
* <AUTHOR>
* @since 2022-06-09
*/
@Tag(name ="返利计算器")
@RestController
@RequestMapping("/rebateCalculator")
public class RebateCalculatorController extends BaseController {

    @Autowired
    private PolicyCalQueryMapper policyCalQueryMapper;

    @Operation(summary = "获取阶梯计算结果", description = "[author: 10200571]")
    @PostMapping("/getCalcHistory")
    public JsonResultVo getCalcHistory(@RequestBody PolicyCalQueryDTO tempDTO){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        ret.setData(policyCalQueryMapper.getCalcHistory(tempDTO.getId()));
        return ret;
    }

    @Operation(summary = "根据阶梯计算结果 获取经销商对应车系零售信息", description = "[author: 10200571]")
    @PostMapping("/getSeriesAAk")
    public JsonResultVo<List<DealerSeriesVO>> getSeriesAAk(@RequestBody RebateCalculatorDTO tempDTO){
        JsonResultVo<List<DealerSeriesVO>> ret = new JsonResultVo<>();
        List<DealerSeriesVO> list = policyCalQueryMapper.getSeriesAAk(tempDTO);
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "根据经销商 查询阶梯配置计算结果列表", description = "[author: 10200571]")
    @PostMapping("/getLadderResult")
    public JsonResultVo<QmPage<ExecFormalCalcResultDO>> getLadderResult(@RequestBody RebateCalculatorDTO tempDTO) {
        JsonResultVo<QmPage<ExecFormalCalcResultDO>> ret = new JsonResultVo<>();
        QmQueryWrapper<ExecFormalCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        TableUtils.appendTableAdditional(queryWrapper,tempDTO, ExecFormalCalcResultDO.class);
        IPage<ExecFormalCalcResultDO> queryPage = TableUtils.convertToIPage(tempDTO);
        IPage<ExecFormalCalcResultDO> list =policyCalQueryMapper.getLadderResult(queryPage,queryWrapper,tempDTO);
        QmPage<ExecFormalCalcResultDO> qmPage = TableUtils.convertQmPageFromMpPage(list);
        ret.setData(qmPage);
        return ret;
    }

    @Operation(summary = "试算", description = "[author: 10200571]")
    @PostMapping("/trialCalc")
    public JsonResultVo<List<DealerSeriesVO>> trialCalc(@RequestBody RebateCalculatorDTO tempDTO){
        JsonResultVo<List<DealerSeriesVO>> ret = new JsonResultVo<>();
        QmQueryWrapper<ExecFormalCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        tempDTO.setPageSize(100000);
        TableUtils.appendTableAdditional(queryWrapper,tempDTO, ExecFormalCalcResultDO.class);
        IPage<ExecFormalCalcResultDO> queryPage = TableUtils.convertToIPage(tempDTO);
        IPage<ExecFormalCalcResultDO> ladderlist =policyCalQueryMapper.getLadderResult(queryPage,queryWrapper,tempDTO);
        tempDTO.getDealerSeriesList().forEach(item->{
            for(ExecFormalCalcResultDO calcResult:ladderlist.getRecords()){
                // 预计零售量大于等于下限（field6），小于上限（field5），预计金额 = 预计零售量*单台返利金额（field7）
                if(item.getSeries().equals(calcResult.getField4()) && item.getExpectedSum()>= Integer.parseInt(calcResult.getField6())
                        && item.getExpectedSum()<Integer.parseInt(calcResult.getField5())){
                    item.setExpectedNamt(item.getExpectedSum()*Integer.parseInt(calcResult.getField7()));
                }
            }
        });
        ret.setData(tempDTO.getDealerSeriesList());
        return ret;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo save(@RequestBody RebateCalculatorDTO tempDTO){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        String id = policyCalQueryMapper.getEstimateRebate(tempDTO);
        LoginKeyDO loginKeyDO = this.getUserInfo();
        BigDecimal sumAmout = BigDecimal.valueOf(0);
        for(DealerSeriesVO item:tempDTO.getDealerSeriesList()){
            sumAmout = sumAmout.add(BigDecimal.valueOf(item.getExpectedNamt()));
        }

        if(BootAppUtil.isNullOrEmpty(id)){
            // 新建
            boolean flag = policyCalQueryMapper.saveEstimateRebate(tempDTO,sumAmout,loginKeyDO);
            if(flag){
                ret.setMsg("保存成功");
            }
        }else{
            // 更新
            boolean flag = policyCalQueryMapper.updateEstimateRebate(sumAmout,id,loginKeyDO);
            if(flag){
                ret.setMsg("保存成功");
            }
        }
        return ret;
    }

}
