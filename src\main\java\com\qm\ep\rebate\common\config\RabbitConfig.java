package com.qm.ep.rebate.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class RabbitConfig {

    //接收ep车辆档案信息
    @Bean
    public Queue backlogInfoQueue() { return new Queue("todo.basic"); }
    @Bean
    public TopicExchange backlogInfoExchange() { return new TopicExchange("todo_center_exchange"); }
    @Bean
    Binding backlogInfoBinding() {  return BindingBuilder.bind(backlogInfoQueue()).to(backlogInfoExchange()).with("topic.todo_center.todo.basic");}

    @Bean(name = "backlogConnectionFactory")
    public ConnectionFactory secondConnectionFactory(
            @Value("${spring.rabbitmq.backlog.host}") String host,
            @Value("${spring.rabbitmq.backlog.port}") int port,
            @Value("${spring.rabbitmq.backlog.username}") String username,
            @Value("${spring.rabbitmq.backlog.password}") String password,
            @Value("${spring.rabbitmq.backlog.virtualHost}") String virtualHost) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);
        log.info("connectionFactory：{}", connectionFactory);

        return connectionFactory;
    }


    @Bean(name = "backlogRabbitTemplate")
    public RabbitTemplate secondRabbitTemplate(@Qualifier("backlogConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate backlogRabbitTemplate = new RabbitTemplate(connectionFactory);
        return backlogRabbitTemplate;
    }


    // 配置监听2
    @Bean(name = "backlogFactory")
    public SimpleRabbitListenerContainerFactory backlogFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("backlogConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }

}
