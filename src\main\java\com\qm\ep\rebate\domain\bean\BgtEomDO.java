package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * EOM年度预算点数与金额表
 *
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_eom")
@Schema(description = "数据:EOM年度预算点数与金额表")
public class BgtEomDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-财务预算下发年度")
    @TableField("eom_year")
    private String eomYear;

    @Schema(description = "数据-预算下发UUID")
    @TableField("eom_deliver_id")
    private String eomDeliverId;

    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    @Schema(description = "数据-返利项目")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-预算总点数")
    @TableField("eom_total_point")
    private BigDecimal eomTotalPoint;

    @Schema(description = "数据-预算使用点数")
    @TableField("eom_used_point")
    private BigDecimal eomUsedPoint;

    @Schema(description = "数据-预算点数余额")
    @TableField("eom_point_balance")
    private BigDecimal eomPointBalance;

    @Schema(description = "数据-预算金额")
    @TableField("eom_total_amount")
    private BigDecimal eomTotalAmount;

    @Schema(description = "数据-预算使用金额")
    @TableField("eom_used_amount")
    private BigDecimal eomUsedAmount;

    @Schema(description = "数据-预算金额余额")
    @TableField("eom_amount_balance")
    private BigDecimal eomAmountBalance;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    @TableField("bgt_type")
    private String bgtType;

    @Schema(description = "数据-版本号（v开头。eg.V1）")
    @TableField("version")
    private String version;

    @Schema(description = "数据-是否发布（0-未发布，1-发布）")
    @TableField("eom_status")
    private String eomStatus;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;
}
