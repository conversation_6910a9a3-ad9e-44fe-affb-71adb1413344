package com.qm.ep.rebate.controller;

import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.EntryAccountDataDO;
import com.qm.ep.rebate.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.dto.EntryAccountDataDTO;
import com.qm.ep.rebate.domain.request.EntryAccountDataRequest;
import com.qm.ep.rebate.service.EntryAccountDataService;
import com.qm.ep.rebate.service.ExecFormalCalcHistoryService;
import com.qm.ep.rebate.service.PolicyService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.base.service.IMultiLanguageTextService;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/entryAccount")
@Tag(name = "入账申请信息表", description = "[author: ********]")
@Slf4j
public class EntryAccountDataController extends BaseController {


    @Autowired
    @Lazy
    private EntryAccountDataService entryAccountDataService;

    @Autowired
    private IMultiLanguageTextService multiTextService;

    @Autowired
    private ExecFormalCalcHistoryService execFormalCalcHistoryService;

    @Autowired
    private PolicyService policyService;


    /**
     * 查询详情
     * @param id
     * @return
     */
    @Operation(summary = "查询详情", description = "[author: ********]")
    @GetMapping("/byId")
    public JsonResultVo<EntryAccountDataDO> byId(String id) {

        EntryAccountDataDO entryAccountDataDO = entryAccountDataService.getById(id);

        if (Objects.isNull(entryAccountDataDO)) {
            throw new QmException("没有查询到这条数据");
        }
        JsonResultVo<EntryAccountDataDO> resultVo = new JsonResultVo<>();
        resultVo.setData(entryAccountDataDO);
        return resultVo;
    }

    /**
     * 新申请入账V2
     * @param tempDO
     * @return
     */
    @Operation(summary = "新申请入账V2", description = "[author: ********]")
    @PostMapping("/apply")
    public JsonResultVo<String> applyIdV2(@RequestBody EntryAccountDataRequest tempDO) {
        JsonResultVo<String> resultVo = new JsonResultVo<>();
        entryAccountDataService.applyEntryAccountV2(tempDO.getUniqueKey(), tempDO.getBgtRelease());
        return resultVo;
    }


    /**
     * 取消申请入账
     * @param tempDO
     * @return
     * @throws Throwable
     */
    @Operation(summary = "取消申请入账", description = "[author: ********]")
    @PostMapping("/cancel")
    public JsonResultVo<Void> cancelEntryAccount(@RequestBody EntryAccountDataDO tempDO) throws Throwable {
        JsonResultVo<Void> resultVo = new JsonResultVo<>();
        entryAccountDataService.cancelEntryAccount(tempDO.getUniqueKey());
        return resultVo;
    }


    /**
     * 使用系统默认的保存/修改 方法
     */
    @Operation(summary = "保存", description = "[author: ********]")
    @PostMapping("/save")
    public JsonResultVo<EntryAccountDataDO> save(@org.springframework.web.bind.annotation.RequestBody EntryAccountDataDO tempDO) {
        LoginKeyDO loginKey = this.getUserInfo();
        // 保存公司id(数据表中没有公司id请自行删除)


        JsonResultVo<EntryAccountDataDO> resultObj = new JsonResultVo<>();
        boolean flag = entryAccountDataService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);

        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: ********]")
    @PostMapping("/deleteById")
    public JsonResultVo<EntryAccountDataDO> deleteById(@org.springframework.web.bind.annotation.RequestBody EntryAccountDataDO tempDO) {
        JsonResultVo<EntryAccountDataDO> resultObj = new JsonResultVo<>();
        boolean flag = entryAccountDataService.removeById(tempDO.getId());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }

    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: ********]")
    @PostMapping("/tables")
    @DS(DataSourceType.W)
    public JsonResultVo<Map<String, Object>> table(@org.springframework.web.bind.annotation.RequestBody EntryAccountDataDTO tempDTO) {
        // 定义查询构造器
        QmQueryWrapper<EntryAccountDataDO> queryWrapper = new QmQueryWrapper<>();
        // 拼装实体属性查询条件
        LambdaQueryWrapper<EntryAccountDataDO> lambdaWrapper = queryWrapper.lambda();

        // 主键
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getId()), EntryAccountDataDO::getId, tempDTO.getId());

        // 每次入账唯一主键（正式计算历史表ID）
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getUniqueKey()), EntryAccountDataDO::getUniqueKey, tempDTO.getUniqueKey());

        // 返利项目代码，取自销售模块维护返利项目程序
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getClassItem()), EntryAccountDataDO::getClassItem, tempDTO.getClassItem());

        // 入账方式代码，取自字典项：RBTPOLICYACCT
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getAuditType()), EntryAccountDataDO::getAuditType, tempDTO.getAuditType());

        // 每条数据唯一代码（正式计算历史表ID + 正式计算结果表ID）
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getBillNo()), EntryAccountDataDO::getBillNo, tempDTO.getBillNo());

        // 系列
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getSeries()), EntryAccountDataDO::getSeries, tempDTO.getSeries());

        // 经销商代码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getDealerCode()), EntryAccountDataDO::getDealerCode, tempDTO.getDealerCode());

        // 经销商名称
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getDealerName()), EntryAccountDataDO::getDealerName, tempDTO.getDealerName());

        // 返利金额
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getRebateAmount()), EntryAccountDataDO::getRebateAmount, tempDTO.getRebateAmount());

        // 备注（商务政策名称 + 经销商备注（如有） + VIN码）
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getRemark()), EntryAccountDataDO::getRemark, tempDTO.getRemark());

        // 操作员ID
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getOperatorId()), EntryAccountDataDO::getOperatorId, tempDTO.getOperatorId());

        // 状态码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getStateCode()), EntryAccountDataDO::getStateCode, tempDTO.getStateCode());

        // 政策主键
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getPolicyId()), EntryAccountDataDO::getPolicyId, tempDTO.getPolicyId());

        // 政策编码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getPolicyCode()), EntryAccountDataDO::getPolicyCode, tempDTO.getPolicyCode());

        // 政策名臣
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getPolicyName()), EntryAccountDataDO::getPolicyName, tempDTO.getPolicyName());

        // 任务实例编码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getTaskinsanceCode()), EntryAccountDataDO::getTaskinsanceCode, tempDTO.getTaskinsanceCode());

        // 任务流实例编码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getTaskFlowinstanceCode()), EntryAccountDataDO::getTaskFlowinstanceCode, tempDTO.getTaskFlowinstanceCode());

        // 审批流主键
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getProcessInstanceId()), EntryAccountDataDO::getProcessInstanceId, tempDTO.getProcessInstanceId());

        // 创建者
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getCreateby()), EntryAccountDataDO::getCreateby, tempDTO.getCreateby());

        // 创建日期
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getCreateon()), EntryAccountDataDO::getCreateon, tempDTO.getCreateon());

        // 更新者
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getUpdateby()), EntryAccountDataDO::getUpdateby, tempDTO.getUpdateby());

        // 更新日期
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getUpdateon()), EntryAccountDataDO::getUpdateon, tempDTO.getUpdateon());

        // 提交人
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getSubmitBy()), EntryAccountDataDO::getSubmitBy, tempDTO.getSubmitBy());

        // 提交人代码
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getSubmitCode()), EntryAccountDataDO::getSubmitCode, tempDTO.getSubmitCode());

        // 查询数据，使用table函数。
        QmPage<EntryAccountDataDO> list = entryAccountDataService.table(queryWrapper, tempDTO);
        // 过滤掉入账数据为0的结果
        list.getItems().removeIf(ele -> new BigDecimal(ele.getRebateAmount()).compareTo(BigDecimal.ZERO) == 0);
        JsonResultVo<Map<String, Object>> ret = new JsonResultVo<>();

        String uniqueKey = tempDTO.getUniqueKey();
        ExecFormalCalcHistoryDO historyDO = execFormalCalcHistoryService.getById(uniqueKey);
        String policyId = historyDO.getPolicyId();
        PolicyDO policyDO = policyService.getById(policyId);
        Map<String, Object> map = Maps.newHashMap();
        map.put("policyDO", policyDO);
        map.put("historyDO", historyDO);
        map.put("list", list);
        ret.setData(map);
        return ret;
    }

    @Operation(summary = "形成入账数据，自动入账使用", description = "[author: ********]")
    @PostMapping("/autoApply")
    public JsonResultVo autoApply(@RequestBody ExecFormalCalcHistoryDO tempDO) throws Throwable {
        // 打印入参日志
        log.info("入参===========================================：{}", tempDO);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        // 添加自动入账逻辑
        String autoFlag = entryAccountDataService.getAutoApplyFlag(tempDO.getPolicyId());
        if("1".equals(autoFlag)) {
            log.info("===============自动入账开启===============");
            // 校验逻辑
            entryAccountDataService.autoApplyEntryAccount(tempDO);
            //入账逻辑
            //entryAccountDataService.createEntryAccountData(tempDO);
        }
        return resultVo;
    }

    @Operation(summary = "批量保存", description = "[author: ********]")
    @PostMapping("/saveBatch")
    public JsonResultVo saveBatch(@RequestBody List<EntryAccountDataDO> tempDO) throws Throwable {
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        entryAccountDataService.saveBatch(tempDO);
        return resultVo;
    }

    @Operation(summary = "根据正式计算历史id形成入账数据——弥补入账数据缺失的问题", description = "[author: ********]")
    @PostMapping("/generateAccountData")
    public JsonResultVo<String> generateAccountData(@RequestParam("historyId") String historyId) throws Throwable {
        JsonResultVo<String> resultVo = new JsonResultVo<>();
        entryAccountDataService.generateAccountData(historyId);
        return resultVo;
    }


    @Operation(summary = "入账审批增加提醒注释", description = "[author: ********]")
    @PostMapping("/remind")
    public JsonResultVo<String> remind(@RequestBody EntryAccountDataRequest tempDO) throws Throwable {
        JsonResultVo<String> resultVo = new JsonResultVo<>();
        resultVo.setData(entryAccountDataService.remind(tempDO.getUniqueKey()));
        return resultVo;
    }
}
