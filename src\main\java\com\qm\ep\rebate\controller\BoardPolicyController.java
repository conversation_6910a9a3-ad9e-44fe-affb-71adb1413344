package com.qm.ep.rebate.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.qm.ep.rebate.domain.bean.board.BoardPolicyPO;
import com.qm.ep.rebate.domain.request.BoardPolicyRequest;
import com.qm.ep.rebate.domain.response.BoardHome;
import com.qm.ep.rebate.domain.response.BoardPolicyDetailResponse;
import com.qm.ep.rebate.service.BoardPolicyService;
import com.qm.ep.rebate.service.DingDingService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "政策日报看板", description = "[author: 10200571]")
@RequestMapping("/board/policy")
@Slf4j

public class BoardPolicyController {

    private DingDingService dingDingService;

    private BoardPolicyService boardPolicyService;


    @Operation(summary = "政策日报数据总览", description = "[author: 10200571]")
    @GetMapping("/home")
    public JsonResultVo<BoardHome> getHomeData(String createdDate, String sign) {
        JsonResultVo<BoardHome> ret = new JsonResultVo<>();
        ret.setData(boardPolicyService.getHomeData(BoardPolicyRequest.builder()
                .createdDate(createdDate)
                .sign(sign)
                .build()));
        return ret;
    }


    @Operation(summary = "政策逾期详情", description = "[author: 10200571]")
    @PostMapping("/detail")
    public JsonResultVo<List<BoardPolicyDetailResponse>> getDetailData(@Validated @RequestBody BoardPolicyRequest request) {
        JsonResultVo<List<BoardPolicyDetailResponse>> ret = new JsonResultVo<>();
        ret.setData(boardPolicyService.getDetailData(request));
        return ret;
    }

    @Operation(summary = "政策灯塔日报每日触发", description = "[author: 10200571]")
    @GetMapping("/schedule")
    public JsonResultVo<String> schedule() {
        JsonResultVo<String> ret = new JsonResultVo<>();
        boardPolicyService.runSchedule(DateUtil.today());
        ret.setData("政策灯塔日报每日触发成功！！");
        return ret;
    }

    @Operation(summary = "手动触发政策灯塔日报", description = "[author: 10200571]")
    @GetMapping("/scheduleManually")
    public JsonResultVo<String> scheduleManually(String sealDate) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        boardPolicyService.scheduleManually(sealDate);
        ret.setData("scheduleManually ok！");
        return ret;
    }


    @Operation(summary = "手动推送灯塔指标数据到工作台-ok", description = "[author: 10200571]")
    @GetMapping("/pushLightAimToWorkbench")
    public String pushLightAimToWorkbench(String sealDate) {
        boardPolicyService.pushLightAimToWorkbench(sealDate);
        return "ok";
    }


    @Operation(summary = "手动推送部门绩效数据到工作台-timeType:WEEK或者MONTH", description = "[author: 10200571]")
    @GetMapping("/pushDeptPerformanceToWorkbench")
    public String pushDeptPerformanceToWorkbench(@RequestParam("sealDate") String sealDate, @RequestParam("timeType") String timeType) {
        boardPolicyService.pushDeptPerformanceToWorkbench(sealDate, timeType);
        return "ok";
    }

    @Operation(summary = "手动推送异常指标和亮点数据推送到DMS-ok", description = "[author: 10200571]")
    @GetMapping("/pushExceptionAndLightToDMS")
    public String pushExceptionAndLightToDms(String sealDate) {
        boardPolicyService.pushExceptionAndLightToDMS(sealDate);
        return "ok";
    }

    @Operation(summary = "灯塔日报-检测机制", description = "[author: 10200571]")
    @GetMapping("/monitorScheduleRun")
    public JsonResultVo<String> monitorScheduleRun() {
        JsonResultVo<String> ret = new JsonResultVo<>();
        List<BoardPolicyPO> list = boardPolicyService.getBySealDate(DateUtil.today());
        if (CollUtil.isEmpty(list)) {
            dingDingService.pushMsgToPolicyBoardRobotToAll("政策日报数据为空，请检查数据状态");
        }
        ret.setData("定时任务已跑");
        return ret;
    }


}
