package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.ep.rebate.enumerate.FunctionEnum;
import com.qm.ep.rebate.enumerate.NullValueEnum;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ladder_content")
@Schema(description = "公式内容表")
public class LadderContentDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策ID")
    @TableField("POLICY_ID")
    private String policyId;

    @Schema(description = "数据-阶梯ID")
    @TableField("LADDER_ID")
    private String ladderId;

    @Schema(description = "数据-别名（上限、下限、单台返利金额）")
    @TableField("NAME")
    private String name;

    @Schema(description = "数据-公式内容")
    @TableField("CONTENT")
    private String content;

    @Schema(description = "数据-保留小数位")
    @TableField("`DECIMAL`")
    private Integer decimal;

    @Schema(description = "数据-分母为零时计算规则")
    @TableField("RULE")
    private NullValueEnum rule;

    @Schema(description = "数据-取值类型")
    @TableField("FETCH_TYPE")
    private FunctionEnum fetchType;

    @Schema(description = "数据-创建时间")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-更新")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

}
