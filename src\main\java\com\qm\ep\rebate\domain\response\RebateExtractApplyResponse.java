package com.qm.ep.rebate.domain.response;

import com.qm.ep.rebate.domain.bean.RebateExtractInvoicePO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 返利折让申请单与处理单合并表
 *
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Schema(description = "返利折让申请单与处理单合并表  ")
@Getter
@Setter
public class RebateExtractApplyResponse implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 申请单号
     */
    @Schema(description = "申请单号")
    private String applyNumber;
    /**
     * 状态（'00-提交','005-店总审核' '01-区域审核中', '02-大区审核中', '03-渠道审核中', '04-已审核', '10-处理中', '11-已处理', '12-已提报', '13-已确认',  '14-提交财务', '20-驳回', '21-终止', '22-撤回'）
     */
    @Schema(description = "状态（'00-提交','005-店总审核' '01-区域审核中', '02-大区审核中', '03-渠道审核中', '04-已审核', '10-处理中', '11-已处理', '12-已提报', '13-已确认',  '14-提交财务', '20-驳回', '21-终止', '22-撤回'）")
    private String applyStatus;
    /**
     * 经销商代码
     */
    @Schema(description = "经销商代码")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @Schema(description = "经销商名称")
    private String dealerName;

    /**
     * 重点关注店(0-不是，1-是)
     */
    @Schema(description = "重点关注店(0-不是，1-是)")
    private String canFocusStore;

    /**
     * 上月STD达成率
     */
    @Schema(description = "上月STD达成率")
    private BigDecimal stdAchievementRate;

    /**
     * 申请折让金额（万元）
     */
    @Schema(description = "申请折让金额（万元）")
    private BigDecimal appliedExtractAmount;

    /**
     * 申请时返利账户可用金额（万元）
     */
    @Schema(description = "申请时返利账户可用金额（万元）")
    private BigDecimal availableRebateAccount;

    /**
     * 申请原因
     */
    @Schema(description = "申请原因")
    private String applyReason;

    /**
     * 承诺事项
     */
    @Schema(description = "承诺事项")
    private String commitment;

    /**
     * 是否同意（0-不同意，1-同意）
     */
    @Schema(description = "是否同意（0-不同意，1-同意）")
    private String canAgree;

    /**
     * 经销商店总审批意见
     */
    @Schema(description = "经销商店总审批意见")
    private String approveOpinion;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String contactInfo;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    private LocalDateTime createOn;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 操作人员
     */
    @Schema(description = "操作人员")
    private String operator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateOn;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private LocalDateTime dtstamp;

    /**
     * 重点关注级别
     */
    @Schema(description = "重点关注级别")
    private String focusLevel;

    /**
     * 本月std目标
     */
    @Schema(description = "本月std目标")
    private Integer stdAim;

    /**
     * 周转系数
     */
    @Schema(description = "周转系数")
    private BigDecimal turnoverCoefficient;

    /**
     * 预留金额
     */
    @Schema(description = "预留金额")
    private BigDecimal reserveAmount;

    /**
     * 测算兑现金额（万元）
     */
    @Schema(description = "测算兑现金额（万元）")
    private BigDecimal calExtractAmount;

    /**
     * 测算时返利账户可用金额（万元）
     */
    @Schema(description = "测算时返利账户可用金额（万元）")
    private BigDecimal calAvailableRebateAccount;

    /**
     * 决策兑现金额（万元）
     */
    @Schema(description = "决策兑现金额（万元）")
    private BigDecimal decisionExtractAmount;

    /**
     * 实际兑现金额（万元）
     */
    @Schema(description = "实际兑现金额（万元）")
    private BigDecimal actualExtractAmount;

    /**
     * 提交时间
     */
    @Schema(description = "提交时间")
    private String submitApplyTime;

    /**
     * 审核通过时间
     */
    @Schema(description = "审核通过时间")
    private String passTime;

    /**
     * 最后一次测算时间
     */
    @Schema(description = "最后一次测算时间")
    private String calTime;

    /**
     * 申请单处理完成时间
     */
    @Schema(description = "申请单处理完成时间")
    private String handleTime;

    /**
     * 提报材料时间
     */
    @Schema(description = "提报材料时间")
    private String submitFileTime;

    /**
     * 申请单确认时间
     */
    @Schema(description = "申请单确认时间")
    private String confirmTime;

    /**
     * 申请单提交财务时间
     */
    @Schema(description = "申请单提交财务时间")
    private String submitFinanceTime;

    /**
     * 财务兑付完成时间（转账时间）
     */
    @Schema(description = "财务兑付完成时间（转账时间）")
    private String financeCashTime;

    /**
     * 终止原因
     */
    @Schema(description = "终止原因")
    private String terminateReason;

    /**
     * 发票信息编号集合
     */
    @Schema(description = "发票信息编号集合")
    private List<RebateExtractInvoicePO> invoiceList;


    /**
     * 文件列表
     */
    @Schema(description = "文件列表")
    private List<RebateExtractFileResponse> fileList;
}
