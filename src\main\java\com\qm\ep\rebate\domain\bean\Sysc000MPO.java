package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;


@Schema(description = "数据:实体类-SYSC000 MPO系列")
@TableName("sysc000_m")
public class Sysc000MPO implements Serializable {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    /**
     * 代码ID
     */
    @Schema(description = "数据-代码ID")
    private String nmainid;

    /**
     * 语言代码
     */
    @Schema(description = "数据-语言代码")
    private String vlanguagecode;

    /**
     * 文本
     */
    @Schema(description = "数据-文本")
    private String vtext;

    /**
     * 长文本
     */
    @Schema(description = "数据-长文本")
    private String vlongtext;

    @Schema(description = "数据-DTSTAMP")
    private LocalDateTime dtstamp;

    /**
     * 代码
     */
    @Schema(description = "数据-代码")
    private String vcode;

    /**
     * 公司id
     */
    @Schema(description = "数据-公司id")
    private String ncompanyid;

    /**
     * 表名
     */
    @Schema(description = "数据-表名")
    private String vtablename;

    /**
     * 描述文本
     */
    @Schema(description = "数据-描述文本")
    private String vdescription;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getNmainid() {
        return nmainid;
    }

    public void setNmainid(String nmainid) {
        this.nmainid = nmainid;
    }
    public String getVlanguagecode() {
        return vlanguagecode;
    }

    public void setVlanguagecode(String vlanguagecode) {
        this.vlanguagecode = vlanguagecode;
    }
    public String getVtext() {
        return vtext;
    }

    public void setVtext(String vtext) {
        this.vtext = vtext;
    }
    public String getVlongtext() {
        return vlongtext;
    }

    public void setVlongtext(String vlongtext) {
        this.vlongtext = vlongtext;
    }
    public LocalDateTime getDtstamp() {
        return dtstamp;
    }

    public void setDtstamp(LocalDateTime dtstamp) {
        this.dtstamp = dtstamp;
    }
    public String getVcode() {
        return vcode;
    }

    public void setVcode(String vcode) {
        this.vcode = vcode;
    }
    public String getNcompanyid() {
        return ncompanyid;
    }

    public void setNcompanyid(String ncompanyid) {
        this.ncompanyid = ncompanyid;
    }
    public String getVtablename() {
        return vtablename;
    }

    public void setVtablename(String vtablename) {
        this.vtablename = vtablename;
    }
    public String getVdescription() {
        return vdescription;
    }

    public void setVdescription(String vdescription) {
        this.vdescription = vdescription;
    }

    @Override
    public String toString() {
        return "Sysc000M{" +
            "id=" + id +
            ", nmainid=" + nmainid +
            ", vlanguagecode=" + vlanguagecode +
            ", vtext=" + vtext +
            ", vlongtext=" + vlongtext +
            ", dtstamp=" + dtstamp +
            ", vcode=" + vcode +
            ", ncompanyid=" + ncompanyid +
            ", vtablename=" + vtablename +
            ", vdescription=" + vdescription +
        "}";
    }
}
