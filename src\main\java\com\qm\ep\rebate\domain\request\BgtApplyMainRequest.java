package com.qm.ep.rebate.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  
 * 预算申请单
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Schema(description = "数据:  预算申请单  ")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BgtApplyMainRequest  {


    @Schema(description = "数据-预算申请单名")
    private String applyName;

    @Schema(description = "数据-申请状态（10-初始、11-提交、12-通过、13驳回）")
    private String applyStatus;


    @Schema(description = "数据-预算申请类型（0-常规调整，1-专项调整）")
    private String applyType;


    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-调整方式（0-点数，1-金额）")
    private String modifyWay;


    @Schema(description = "数据-申请来源（01-业务专员，02-运营专员）")
    private String applySource;

    @Schema(description = "数据-政策id")
    private String policyId;


    @Schema(description = "数据-文件路径（逗号拼接）")
    private String filePath;

    @Schema(description = "数据-待办实例编码")
    private String taskInstanceCode;

    @Schema(description = "数据-年")
    private String breakdownYear;

    @Schema(description = "数据-季度")
    private String breakdownQuarter;

}
