package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 *  
 * 
 *
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audit_authorize")
@Schema(description = "数据实体")
public class AuditAuthorizeDO implements Serializable {

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据-部门")
    @TableField("department")
    private String department;

    @Schema(description = "数据-商务政策专员")
    @TableField("commissionerId")
    private String commissionerId;

    @Schema(description = "数据-商务政策专员")
    @TableField("commissioner")
    private String commissioner;

    @Schema(description = "数据-总监")
    @TableField("director")
    private String director;

    @Schema(description = "数据-商务政策专员手机号")
    @TableField("commissionerPhone")
    private String commissionerPhone;

    @Schema(description = "数据-总监手机号")
    @TableField("directorPhone")
    private String directorPhone;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-按名称创建")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-创建时间")
    @TableField(value ="createOn" ,fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-按名称更新")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-更新")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-数据DTSTAMP")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    private Timestamp dtstamp;

    @Schema(description = "数据-ids")
    @TableField(exist = false)
    private List<String> ids;

    @Schema(description = "数据-公司")
    @TableField("company")
    private String company;

    @Schema(description = "数据-一审人id")
    @TableField("firstAuditId")
    private String firstAuditId;

    @Schema(description = "数据-一审人域账户")
    @TableField("firstLoginAccount")
    private String firstLoginAccount;

    @Schema(description = "数据-一审人姓名")
    @TableField("firstAuditName")
    private String firstAuditName;

    @Schema(description = "数据-一审自动审核")
    @TableField("autoFirstAudit")
    private Integer autoFirstAudit;

    @Schema(description = "数据-二审人id")
    @TableField("secondAuditId")
    private String secondAuditId;

    @Schema(description = "数据-二审人域账户")
    @TableField("secondLoginAccount")
    private String secondLoginAccount;

    @Schema(description = "数据-二审人姓名")
    @TableField("secondAuditName")
    private String secondAuditName;

    @Schema(description = "数据-二审自动审核")
    @TableField("autoSecondAudit")
    private Integer autoSecondAudit;

    @Schema(description = "数据-三审人id")
    @TableField("thirdAuditId")
    private String thirdAuditId;

    @Schema(description = "数据-三审人域账户")
    @TableField("thirdLoginAccount")
    private String thirdLoginAccount;

    @Schema(description = "数据-三审人姓名")
    @TableField("thirdAuditName")
    private String thirdAuditName;

    @Schema(description = "数据-三审自动审核")
    @TableField("autoThirdAudit")
    private Integer autoThirdAudit;

    @Schema(description = "数据-部门Id（hq）")
    @TableField("departmentId")
    private String departmentId;
}
