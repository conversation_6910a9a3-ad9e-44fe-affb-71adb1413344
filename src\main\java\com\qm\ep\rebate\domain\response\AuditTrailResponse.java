package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */

@Schema(description = "数据:审计跟踪响应")
@Data
public class AuditTrailResponse implements Serializable {

    @Schema(description = "序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-处理时间")
    private Date time;

    @Schema(description = "数据-处理人名称：userName")
    private String approver;

    @Schema(description = "数据-处理层级")
    private int level;

    @Schema(description = "数据-审批状态：typeDescName")
    private String state;

    @Schema(description = "数据-处理意见:fullMessage")
    private String remark;

    @Schema(description = "数据-节点Title")
    private String title;
}