package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.domain.bean.CalFactorMainDO;
import com.qm.ep.rebate.domain.bean.CalculateTrialMainDO;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.bean.RedFlagTrialMainDO;
import com.qm.ep.rebate.domain.dto.*;
import com.qm.ep.rebate.domain.vo.RedFlagTrialVO;
import com.qm.ep.rebate.enumerate.PolicyStatusEnum;
import com.qm.ep.rebate.enumerate.YesOrNoEnum;
import com.qm.ep.rebate.mapper.CalculateTrialMainMapper;
import com.qm.ep.rebate.mapper.RedFlagTrialConditionsMapper;
import com.qm.ep.rebate.mapper.RedFlagTrialJoinMapper;
import com.qm.ep.rebate.mapper.RedFlagTrialJoinOnMapper;
import com.qm.ep.rebate.service.*;
import com.qm.ep.rebate.infrastructure.util.DateUtil;
import com.qm.ep.rebate.infrastructure.util.RegexUtils;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
@RestController
@Slf4j
@RequestMapping("/redFlagTrialMain")
@Tag(name = "红旗伙伴试算")
public class RedFlagTrialMainController extends BaseController {


    @Resource
    private RedFlagTrialMainService redFlagTrialService;
    @Autowired
    private CalculateTrialMainService calculateTrialMainService;
    @Autowired
    private PolicyService policyService;
    @Resource
    private SystemConfigService systemConfigService;
    @Autowired
    private SysPersonOrgService sysPersonOrgService;

    @Autowired
    private CalculateTrialMainMapper calculateTrialMainMapper;

    @Autowired
    private RedFlagTrialConditionsMapper redFlagTrialConditionsMapper;

    @Autowired
    private RedFlagTrialJoinMapper redFlagTrialJoinMapper;

    @Autowired
    private RedFlagTrialJoinOnMapper redFlagTrialJoinOnMapper;

    @Autowired
    private BusinessConstructionService businessConstructionService;

    @Autowired
    private CalFactorMainService calFactorMainService;


    @Operation(summary = "获取数据源", description = "[author: 10200571]")
    @PostMapping("/getDataSource")
    public JsonResultVo<String> getDataSource(@RequestBody CalculateTrialMainDO tempDO) {

        if(Objects.isNull(tempDO) || Objects.isNull(tempDO.getPolicyid()) || ObjectUtils.isEmpty(tempDO.getPolicyid())){

            throw new QmException("政策id不能为空");
        }

        //数据源来源于  本政策名称中“计算因子”所使用的原始数据因子
        QmQueryWrapper<CalFactorMainDO> calFactorMainWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<CalFactorMainDO> mainWrapper = calFactorMainWrapper.lambda();
        mainWrapper.eq(CalFactorMainDO::getPolicyId, tempDO.getPolicyid());
        List<CalFactorMainDO> calFactorMainDOList = calFactorMainService.list(mainWrapper);

        List mainTableList = new ArrayList();

        calFactorMainDOList.forEach(item ->{

            String mainTable = item.getMainTable();
            String[] mainTableItem = mainTable.split(",");

            for(int i =0; i<mainTableItem.length; i++){
                if(ObjectUtils.isNotEmpty(mainTableItem[i])){
                    mainTableList.add(mainTableItem[i]);
                }
            }

        });

        log.info(JSONUtils.beanToJson(mainTableList));
        
        JsonResultVo<String> result = new JsonResultVo<>();
        LoginKeyDO loginKeyDO = this.getUserInfo();

        List<String> resultList = businessConstructionService.getDataSource(loginKeyDO);

        resultList = resultList.stream().filter(item -> mainTableList.contains(item)).collect(Collectors.toList());

        result.setDataList(resultList);

        return result;
    }


    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<CalculateTrialMainDO> deleteById(@RequestBody CalculateTrialMainDO tempDO){
        JsonResultVo<CalculateTrialMainDO> resultObj = new JsonResultVo<>();

        QmQueryWrapper<RedFlagTrialMainDO> wrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<RedFlagTrialMainDO> wrapperLambda = wrapper.lambda();
        wrapperLambda.eq(RedFlagTrialMainDO::getId, tempDO.getId());
        RedFlagTrialMainDO redFlagTrialMainDO = redFlagTrialService.getOne(wrapper);

        //已发布政策 红旗伙伴配置不可删除
        QmQueryWrapper<PolicyDO> wrapper1 = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyDO> wrapperLambda1 = wrapper1.lambda();
        wrapperLambda1.eq(PolicyDO::getId, redFlagTrialMainDO.getPolicyId());
        PolicyDO policyDO = policyService.getOne(wrapper1);
        if(Objects.isNull(policyDO) || policyDO.getVfinishstate().equals(PolicyStatusEnum.PUBLISH.getCode())){
            resultObj.setMsgErr("当前政策已发布，配置信息不可删除！");
            return resultObj;
        }

        boolean flag = redFlagTrialService.deleteMain(tempDO.getId());
        if (flag) {
            // 删除关联内容
            redFlagTrialJoinMapper.deleteJoin(tempDO.getId());
            redFlagTrialJoinOnMapper.deleteJoinOn(tempDO.getId());
            redFlagTrialConditionsMapper.deleteConditions(tempDO.getId());
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }

    @Operation(summary = "查询红旗伙伴试算详情", description = "[author: 10200571]")
    @PostMapping("/info")
    public JsonResultVo<RedFlagTrialVO> info(@RequestBody RedFlagTrialDTO redFlagTrialDTO){
        JsonResultVo<RedFlagTrialVO> jsonResultVo = new JsonResultVo<>();
        // 校验主键
        if(BootAppUtil.isNullOrEmpty(redFlagTrialDTO.getId())){
            jsonResultVo.setMsgErr("查询失败");
            return jsonResultVo;
        }
        jsonResultVo.setData(redFlagTrialService.info(redFlagTrialDTO.getId()));
        return jsonResultVo;
    }

    /**
     * 红旗伙伴试算 主表查询
     */
    @Operation(summary = "红旗伙伴试算 主表查询", description = "[author: 10200571]")
    @PostMapping("/getRedFlagTrialsMain")
    @DS(DataSourceType.W)
    public JsonResultVo<QmPage<CalculateTrialMainDO>> getRedFlagTrialsMain(@RequestBody CalculateTrialMainDTO tempDTO){
        //定义查询构造器
        QmQueryWrapper<CalculateTrialMainDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<CalculateTrialMainDO> lambdaWrapper = queryWrapper.lambda();
        //查询数据，使用table函数。
        lambdaWrapper.eq(CalculateTrialMainDO::getPolicyid, tempDTO.getPolicyid());
        QmPage<CalculateTrialMainDO> list = calculateTrialMainService.table(queryWrapper,tempDTO);
        JsonResultVo<QmPage<CalculateTrialMainDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询红旗伙伴试算模板列表", description = "[author: 10200571]")
    @PostMapping("/getFactorTemplate")
    public JsonResultVo<QmPage<CalculateTrialMainDO>> getFactorTemplate(@RequestBody CalculateTrialMainDTO tempDTO){
        String company = systemConfigService.getValueByCode(RebateConstants.COMPANY);
        LoginKeyDO userInfo = getUserInfo();

        QmQueryWrapper<PolicyDO> policyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<PolicyDO> lambda = policyWrapper.lambda();
        lambda.eq(PolicyDO::getIsTemplate, YesOrNoEnum.YES)
                .like(StringUtils.isNotEmpty(tempDTO.getPolicyName()), PolicyDO::getVpolicyname, tempDTO.getPolicyName());
        if("bx".equals(company)){
            List<String> createByList = sysPersonOrgService.queryPersonIdAtRelatedLevel(userInfo.getOperatorId());
            lambda.in(PolicyDO::getCreateBy, createByList);
        }
        List<PolicyDO> policyDOList = policyService.list(policyWrapper);

        JsonResultVo<QmPage<CalculateTrialMainDO>> ret = new JsonResultVo<>();
        if(CollUtil.isEmpty(policyDOList)){
            QmPage<CalculateTrialMainDO> page = new QmPage<>();
            ret.setData(page);
        }else{
            List<String> policyIdList = policyDOList.stream().map(PolicyDO::getId).collect(Collectors.toList());
            QmQueryWrapper<CalculateTrialMainDO> queryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<CalculateTrialMainDO> lambdaWrapper = queryWrapper.lambda();
            lambdaWrapper.in(CalculateTrialMainDO::getPolicyid, policyIdList)
                    .like(StringUtils.isNotEmpty(tempDTO.getFactorname()), CalculateTrialMainDO::getFactorname, tempDTO.getFactorname());
            QmPage<CalculateTrialMainDO> page = calculateTrialMainService.table(queryWrapper,tempDTO);
            ret.setData(page);
        }
        return ret;
    }
    @Operation(summary = "查询红旗伙伴试算模板列表", description = "[author: 10200571]")
    @PostMapping("/getTemplateList")
    public JsonResultVo<Page<RedFlagTrialDTO>> getTemplateList(@RequestBody RedFlagTrialDTO redFlagTrialDTO){

        Page<RedFlagTrialDTO> list = redFlagTrialService.selectAll(redFlagTrialDTO);

        JsonResultVo<Page<RedFlagTrialDTO>> ret = new JsonResultVo<>();

        ret.setData(list);

        return ret;
    }

    @Operation(summary = "保存红旗伙伴试算", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<String> save(@RequestBody RedFlagTrialDTO redFlagTrialDTO){
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        String msg = validateRedFlagTrialDTO(redFlagTrialDTO);
        if(BootAppUtil.isnotNullOrEmpty(msg)){
            jsonResultVo.setMsgErr(msg);
            return jsonResultVo;
        }
        String factorId = redFlagTrialService.save(redFlagTrialDTO);
        jsonResultVo.setData(factorId);
        return jsonResultVo;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateRedFlagTrialDTO(RedFlagTrialDTO redFlagTrialDTO){
        // 校验是否为空
        if(BootAppUtil.isNullOrEmpty(redFlagTrialDTO)){
            return "保存失败";
        }
        return validateId(redFlagTrialDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateId(RedFlagTrialDTO redFlagTrialDTO){
        // 校验主键是否合法
        if(BootAppUtil.isnotNullOrEmpty(redFlagTrialDTO.getId()) && redFlagTrialDTO.getId().length() > RebateConstants.ID_LENGTH_36){
            return "主键长度过长";
        }
        // 校验政策主键是否合法
        if(BootAppUtil.isNullOrEmpty(redFlagTrialDTO.getPolicyId())){
            return "政策主键不能为空";
        }
        if(redFlagTrialDTO.getPolicyId().length() > RebateConstants.ID_LENGTH_36){
            return "政策主键长度过长";
        }
        return validateFactor(redFlagTrialDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFactor(RedFlagTrialDTO redFlagTrialDTO){
        // 校验红旗伙伴试算名称是否合法
        if(BootAppUtil.isNullOrEmpty(redFlagTrialDTO.getFactorName())){
            return "红旗伙伴试算名称不能为空";
        }
        if(redFlagTrialDTO.getFactorName().length() > RebateConstants.FIELD_LENGTH_255){
            return "红旗伙伴试算名称长度过长";
        }
        if(!RegexUtils.validateName(redFlagTrialDTO.getFactorName())){
            return "名称只能包含数字、字母或汉字";
        }

        return validateFactorField(redFlagTrialDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFactorField(RedFlagTrialDTO redFlagTrialDTO){
        // 校验数据源取值是否合法
        if(redFlagTrialDTO.getDataSource() == null || redFlagTrialDTO.getDataSource().isEmpty()){
            return "数据源取值不能为空";
        }
        if(String.join(RebateConstants.COMMA_SEPARATOR, redFlagTrialDTO.getDataSource()).length() > RebateConstants.FIELD_LENGTH_255){
            return "数据源取值过多";
        }
        return validateFilterCondition(redFlagTrialDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateFilterCondition(RedFlagTrialDTO redFlagTrialDTO){
        List<RedFlagTrialConditionsDTO> filterConditionList = redFlagTrialDTO.getFilterCondition();
        if(filterConditionList != null && !filterConditionList.isEmpty()){
            for (RedFlagTrialConditionsDTO condition : filterConditionList) {
                try{
                    validateFieldName(condition);
                    validateOperation(condition);
                    validateFilterMethod(condition);
                    validateCondition(condition);
                }catch(QmException e){
                    return e.getMessage();
                }
            }
        }
        return validateDependentSource(redFlagTrialDTO);
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFieldName(FilterCondition condition){
        // 校验字段名
        if(BootAppUtil.isNullOrEmpty(condition.getFieldName())){
            throw new QmException("字段名不能为空");
        }
        if(condition.getFieldName().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("字段名长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateOperation(FilterCondition condition){
        // 校验前括号
        if(condition.getFrontBracket() != null && condition.getFrontBracket().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("括号长度过长");
        }
        // 校验运算符
        if(BootAppUtil.isNullOrEmpty(condition.getOperation())){
            throw new QmException("运算符不能为空");
        }
        if(condition.getOperation().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("运算符长度过长");
        }
        // 校验后括号
        if(condition.getBackBracket() != null && condition.getBackBracket().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("括号长度过长");
        }
        // 校验关联逻辑
        if(condition.getLogic() != null && condition.getLogic().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("关联逻辑长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateFilterMethod(FilterCondition condition){
        // 校验筛选方式
        if(BootAppUtil.isNullOrEmpty(condition.getFilterMethod())){
            throw new QmException("筛选方式不能为空");
        }
        if(condition.getFilterMethod().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("筛选方式长度过长");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateCondition(FilterCondition condition){
        // 校验条件值
        if(!RebateConstants.VALUE.equals(condition.getFilterMethod()) && BootAppUtil.isNullOrEmpty(condition.getCondition())){
            // 当筛选方式不是字符串而且为空，则报错
            throw new QmException("条件值不能为空");
        }
        if(BootAppUtil.isnotNullOrEmpty(condition.getCondition()) && condition.getCondition().length() > RebateConstants.FIELD_LENGTH_255){
            throw new QmException("条件值长度过长");
        }
        // 校验条件值格式
        if(RebateConstants.NUMBER.equals(condition.getFilterMethod()) && !NumberUtil.isNumber(condition.getCondition())){
            throw new QmException("条件值格式有误");
        }
        if(RebateConstants.DATE.equals(condition.getFilterMethod()) && !DateUtil.validateDateTime(condition.getCondition(), RebateConstants.DATE_FORMAT_PATTERN)){
            throw new QmException("条件值格式有误");
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private String validateDependentSource(RedFlagTrialDTO redFlagTrialDTO){
        List<String> dataSource = redFlagTrialDTO.getDataSource();
        List<RedFlagTrialJoinDTO> dependents = redFlagTrialDTO.getDependents();
        if(dependents != null && !dependents.isEmpty()){
            if(dataSource.size() > 1){
                return "主数据源为两个或两个以上时，从数据源应该为空";
            }
            for(RedFlagTrialJoinDTO dependent : dependents){
                if(dependent.getJoinType() == null){
                    return "关联类型不能为空";
                }
                if(BootAppUtil.isNullOrEmpty(dependent.getTableName())){
                    return "从数据源不能为空";
                }
                if(dependent.getTableName().length() > RebateConstants.FIELD_LENGTH_255){
                    return "从数据源长度过长";
                }
                List<RedFlagTrialJoinOnDTO> conditions = dependent.getConditions();
                if(conditions != null && !conditions.isEmpty()){
                    try {
                        validateJoinCondition(conditions);
                    } catch(QmException e){
                        return e.getMessage();
                    }
                }else{
                    return "关联条件不能为空";
                }
            }
        }
        return "";
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    private void validateJoinCondition(List<RedFlagTrialJoinOnDTO> conditions){
        for(RedFlagTrialJoinOnDTO condition : conditions){
            validateFieldName(condition);
            validateOperation(condition);
            validateFilterMethod(condition);
            validateCondition(condition);
        }
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @PostMapping("/getRedFlagMainById")
    public JsonResultVo<RedFlagTrialMainDO> getRedFlagMainById(@RequestParam String id){
        JsonResultVo<RedFlagTrialMainDO> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(redFlagTrialService.getRedFlagMainById(id));
        return jsonResultVo;
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @PostMapping("/getRedFlagMainByPolicyId")
    public JsonResultVo<RedFlagTrialMainDO> getRedFlagMainByPolicyId(@RequestParam String policyId){
        JsonResultVo<RedFlagTrialMainDO> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setDataList(redFlagTrialService.getRedFlagMainByPolicyId(policyId));
        return jsonResultVo;
    }

}
