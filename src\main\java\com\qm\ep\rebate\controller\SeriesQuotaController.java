package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.SeriesQuotaDO;
import com.qm.ep.rebate.service.SeriesQuotaService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/seriesQuota")
@Tag(name = "车系查询")
public class SeriesQuotaController {
    @Resource
    private SeriesQuotaService seriesQuotaService;

    @Operation(summary = "车系查询", description = "[author: 10200571]")
    @GetMapping("/seriesList")
    public JsonResultVo<List<SeriesQuotaDO>> seriesList(){
        JsonResultVo<List<SeriesQuotaDO>> resultVo = new JsonResultVo<>();
        resultVo.setData(seriesQuotaService.getSeriesIfStop0());
        return resultVo;
    }
}
