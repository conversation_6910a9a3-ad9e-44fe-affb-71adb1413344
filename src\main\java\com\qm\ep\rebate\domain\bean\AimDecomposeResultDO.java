package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *  
 * 
 *
 *
 * <AUTHOR>
 * @since 2023-01-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("aim_decompose_result")
@Schema(description = "数据实体")
public class AimDecomposeResultDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据-目标详情ID")
    @TableField("historyId")
    private String historyId;

    @Schema(description = "数据-组织代码")
    @TableField("orgCode")
    private String orgCode;

    @Schema(description = "数据-国家")
    @TableField("country")
    private String country;

    @Schema(description = "数据-渠道")
    @TableField("channel")
    private String channel;

    @Schema(description = "数据-区域")
    @TableField("region")
    private String region;

    @Schema(description = "数据-车型")
    @TableField("model")
    private String model;

    @Schema(description = "数据-车型mix")
    @TableField("modelMix")
    private String modelMix;

    @Schema(description = "数据-是否新能源")
    @TableField("ev")
    private String ev;

    @Schema(description = "数据-一月销量")
    @TableField("jan")
    private Double jan;

    @Schema(description = "数据-二月销量")
    @TableField("feb")
    private Double feb;

    @Schema(description = "数据-三月销量")
    @TableField("mar")
    private Double mar;

    @Schema(description = "数据-四月销量")
    @TableField("apr")
    private Double apr;

    @Schema(description = "数据-五月销量")
    @TableField("may")
    private Double may;

    @Schema(description = "数据-六月销量")
    @TableField("jun")
    private Double jun;

    @Schema(description = "数据-七月销量")
    @TableField("jul")
    private Double jul;

    @Schema(description = "数据-八月销量")
    @TableField("aug")
    private Double aug;

    @Schema(description = "数据-九月销量")
    @TableField("sep")
    private Double sep;

    @Schema(description = "数据-十月销量")
    @TableField("oct")
    private Double oct;

    @Schema(description = "数据-十一月销量")
    @TableField("nov")
    private Double nov;

    @Schema(description = "数据-十二月销量")
    @TableField("dece")
    private Double dece;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;


}
