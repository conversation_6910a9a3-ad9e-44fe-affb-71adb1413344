package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:实体类-任务模板信息响应 DTO")
@Data
public class TaskTemplateInfoResponseDTO {

    @Schema(description = "数据-业务单元编码")
    private String bizUnitCode;

    @Schema(description = "数据-任务名称/业务单元名称")
    private String bizUnitName;

    @Schema(description = "数据-业务单元角色编码")
    private String bizUnitRoleCode;

    @Schema(description = "数据-位置")
    private String position;

    @Schema(description = "数据-触发器类型（0：手动，1：时间定时，2：时间计划，3：事件）")
    private Integer triggerType;

    @Schema(description = "数据-事件编码,对应唯一业务单元编码")
    private String eventCode;

    @Schema(description = "数据-当前事件类型触发器配置的可下发用户信息列表")
    private List<UserInfoResponseDTO> userInfoList;

    @Schema(description = "数据-实例化的任务信息列表")
    private List<TaskInstanceInfoResponseDTO> taskInstanceInfoList;

}
