package com.qm.ep.rebate.domain.dto.oa;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:oa回调请求")
@Data
public class BPMRequest {
    @Schema(description = "数据-oa回调接口参数")
    private BPMCallBackInterfaceParam interfaceParam;

    /**
     * 消息类型（中间节点-message_send，流程自动调用-service_callback，流程结束-process_listener）
     */
    @Schema(description = "数据-消息类型（中间节点-message_send，流程自动调用-service_callback，流程结束-process_listener）")
    private String type;


}