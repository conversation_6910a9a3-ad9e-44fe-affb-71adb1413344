package com.qm.ep.rebate.domain.dto.agent.in;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "数据:报账单列表")
public class Sald002ManageListInDTO extends JsonParamDto {

    @Schema(description = "数据-报账开始时间")
    private String reimburseBegin;

    @Schema(description = "数据-报账结束时间")
    private String reimburseEnd;

    @Schema(description = "数据-发票号")
    private String invoiceNo;

    @Schema(description = "数据-报账状态,0录入、1提交、2驳回、3完成、4取消")
    private String reimburseStatus;

    @Schema(description = "数据-报账单号")
    private String reimburseNo;

    @Schema(description = "数据-报销单名称")
    private String reimburseName;

    @Schema(description = "数据-代理商代码")
    private List<String> vdealerCode;

}
