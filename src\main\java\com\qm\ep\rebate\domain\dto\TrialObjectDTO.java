package com.qm.ep.rebate.domain.dto;

import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.PolicyStatusEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:TrialObjectDTO对象")
@Data
@ToString(callSuper = true)
public class TrialObjectDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-计算对象ID")
    private String objectId;

    @Schema(description = "数据-计算对象类型")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "数据-计算对象名称")
    private String objectName;

    @Schema(description = "数据-政策主键")
    private String policyId;

    @Schema(description = "数据-商务政策编码")
    private String policyCode;

    @Schema(description = "数据-商务政策名称")
    private String policyName;

    @Schema(description = "数据-字段列表")
    private List<String> fields;

    @Schema(description = "数据-所属公司")
    private String companyId;

    @Schema(description = "数据-经销商代码")
    private String dealerCode;

    @Schema(description = "数据-政策状态")
    private List<PolicyStatusEnum> policyStatus;

    @Schema(description = "数据-计算对象类型列表")
    private List<CalcObjectTypeEnum> objectTypes;

    @Schema(description = "数据-是否使用临时表")
    private Boolean useTemporaryTable;

    @Schema(description = "数据-是否使用缩略别名")
    private Boolean abbreviated;

    @Schema(description = "数据-已经被选中得对象")
    private List<TrialObjectDTO> selectedObjects;
}
