package com.qm.ep.rebate.domain.dto.structure;

import com.qm.ep.rebate.domain.bean.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-因子 DTO")
@Data
public class FactorDTO {

    @Schema(description = "数据-主要")
    private CalFactorMainDO main;
    @Schema(description = "数据-详")
    private List<CalFactorDetailDO> details;
    @Schema(description = "数据-加入")
    private List<CalFactorJoinDO> joins;
    @Schema(description = "数据-条件")
    private List<CalFactorConditionsDO> conditions;
    @Schema(description = "数据-行列")
    private List<RankDO> ranks;

}