package com.qm.ep.rebate.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 *
 * 维护已发布政策信息
 *  
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("policy_published")
@Schema(description = "数据:维护已发布政策信息")
public class PolicyPublishedDO implements Serializable {


    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策编号")
    @TableField("VPOLICYCODE")
    private String vpolicycode;

    @Schema(description = "数据-政策名称")
    @TableField("VPOLICYNAME")
    private String vpolicyname;

    @Schema(description = "数据-是否调整 0/1")
    @TableField("ISADJUST")
    private String isadjust;

    @Schema(description = "数据-调整后编号")
    @TableField("ADJUSTVPOLICYCODE")
    private String adjustvpolicycode;

    @Schema(description = "经办人部门id")
    @TableField("DEPARTMENT")
    private String department;

    @Schema(description = "数据-经办人 Hrid")
    @TableField("AGENT")
    private String agent;

    @Schema(description = "数据-发布日期")
    @TableField("PUBLISHDATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date publishdate;

    @Schema(description = "数据-备注")
    @TableField("REMARK")
    private String remark;

    @Schema(description = "数据-暂不需配置")
    @TableField("NOREQUIRED")
    private String norequired;

    @Schema(description = "数据-停用")
    @TableField("STOP")
    private String stop;

    @Schema(description = "数据-停用时间")
    @TableField("STOPDATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date stopdate;

    @Schema(description = "数据-创建")
    @TableField("CREATEON")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createon;

    @Schema(description = "数据-创建者")
    @TableField("CREATEBY")
    private String createby;

    @Schema(description = "数据-更新")
    @TableField("UPDATEON")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateon;

    @Schema(description = "数据-更新作者")
    @TableField("UPDATEBY")
    private String updateby;

    @Schema(description = "数据-数据DTSTAMP")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-部门名称  逗号分割 冗余存储")
    @TableField("DEPARTMENTNAME")
    private String departmentname;

    @Schema(description = "数据-经办人名称 ，逗号分割 冗余存储")
    @TableField("AGENTNAME")
    private String agentname;


    // @Schema(description = "数据-部门 存id，逗号分割")
    // @TableField(exist = false)
    // private List<String> departments;

    // @Schema(description = "数据-经办人 存id，逗号分割")
    // @TableField(exist = false)
    // private List<String> agents;

    @Schema(description = "数据-创建人")
    @TableField(exist = false)
    private String createbyname;

    @Schema(description = "数据-钉钉号")
    @TableField("DINGDINGNO")
    private String dingdingno;

    @Schema(description = "数据-任务实例编码")
    @TableField("TASKINSTANCECODE")
    private String taskinstancecode;

    @Schema(description = "数据-任务流实例编码")
    @TableField("TASKFLOWINSTANCECODE")
    private String taskflowinstancecode;

    @Schema(description = "数据-政策阶段")
    @TableField("STAGE")
    private String stage;

    @Schema(description = "数据-政策周期")
    @TableField("POLICYCYCLE")
    private String policycycle;

    /**
     * 预算功能 - 新增字段
     */
    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    @TableField("budget_type")
    private String budgetType;

    @Schema(description = "数据-返利项目代码")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    @TableField("classItemName")
    private String classItemName;

    @Schema(description = "数据-预算释放状态（0-不可释放，1-未释放，2-已释放）")
    @TableField("bgt_release_status")
    private String bgtReleaseStatus;

    @Schema(description = "数据-业务类型")
    @TableField(exist = false)
    private String businessType;


    @Schema(description = "政策目标")
    @TableField("policy_target")
    private String policyTarget;

    @Schema(description = "其他目标名称")
    @TableField("other_target_name")
    private String otherTargetName;

    @Schema(description = "其他目标值")
    @TableField("other_target_value")
    private String otherTargetValue;

    @Schema(description = "政策目标车系")
    @TableField("policy_target_series")
    private String policyTargetSeries;

    @Schema(description = "政策目标车系数组")
    @TableField(exist = false)
    private List<String> series;

    @Schema(description = "执行开始时间")
    @TableField("dbegin")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @Schema(description = "执行结束时间")
    @TableField("dend")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @Schema(description = "确认时间")
    @TableField("confirm_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date confirmDate;

    @Schema(description = "分配时间")
    @TableField("allocation_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date allocationDate;

    @Schema(description = "该编码是否需要参与审计 0-返利标准，1-返利包")
    @TableField("rebateType")
    private String rebateType;

    @Schema(description = "数据-经办人名称 ，逗号分割 冗余存储")
    @TableField("AGENTCODE")
    private String agentcode;



}
