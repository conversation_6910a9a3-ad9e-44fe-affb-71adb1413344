package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import com.qm.ep.rebate.common.constant.RebateConstants;
import com.qm.ep.rebate.domain.bean.ParamSetDO;
import com.qm.ep.rebate.domain.dto.ParamBusinessDataDTO;
import com.qm.ep.rebate.domain.vo.ParamBusinessDataVO;
import com.qm.ep.rebate.service.ParamBusinessDataService;
import com.qm.ep.rebate.service.ParamSetService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/paramBusinessData")
@Tag(name = "列表参数关联业务底表数据")
public class ParamBusinessDataController extends BaseController {

    @Autowired
    private ParamBusinessDataService paramBusinessDataService;
    @Autowired
    private ParamSetService paramSetService;

    @Operation(summary = "查询被关联的底表数据", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ParamBusinessDataVO>> getParamBusinessDataTable(@RequestBody ParamBusinessDataDTO tempDTO){
        if (tempDTO == null) {
            throw new QmException("查询已指定数据失败");
        }
        JsonResultVo<QmPage<ParamBusinessDataVO>> ret = new JsonResultVo<>();
        if(StringUtils.isBlank(tempDTO.getParamId()) || StringUtils.isBlank(tempDTO.getSelectedObj())){
            return ret;
        }
        QmPage<ParamBusinessDataVO> list = paramBusinessDataService.table(tempDTO);
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询未被关联的底表数据", description = "[author: 10200571]")
    @PostMapping("/unSelectedTable")
    public JsonResultVo<QmPage<ParamBusinessDataVO>> getNoSelectedBusinessDataTable(@RequestBody ParamBusinessDataDTO tempDTO){
        if (tempDTO == null) {
            throw new QmException("查询未指定数据失败");
        }
        JsonResultVo<QmPage<ParamBusinessDataVO>> ret = new JsonResultVo<>();
        if(StringUtils.isBlank(tempDTO.getParamId()) || StringUtils.isBlank(tempDTO.getSelectedObj())){
            return ret;
        }
        QmPage<ParamBusinessDataVO> list = paramBusinessDataService.noSelectedTable(tempDTO);
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "保存关联数据", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo save(@RequestBody ParamBusinessDataDTO tempDTO){
        if(tempDTO == null || StringUtils.isBlank(tempDTO.getParamId())
                || StringUtils.isBlank(tempDTO.getSelectedObj()) || CollUtil.isEmpty(tempDTO.getBusinessDataIds())){
            throw new QmException("保存失败");
        }
        paramBusinessDataService.save(tempDTO);
        return new JsonResultVo<>();
    }

    @Operation(summary = "删除关联数据", description = "[author: 10200571]")
    @PostMapping("/remove")
    public JsonResultVo remove(@RequestBody String id){
        if(StringUtils.isBlank(id)){
            throw new QmException("删除失败");
        }
        paramBusinessDataService.removeById(id);
        return new JsonResultVo<>();
    }

    @Operation(summary = "提交关联数据", description = "[author: 10200571]")
    @PostMapping("/commit")
    public JsonResultVo<String> commit(@RequestParam("file") MultipartFile file, @RequestParam(value = "paramId", required = false) String paramId, @RequestParam(value = "selectedObj", required = false) String selectedObj){
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        if(file == null){
            jsonResultVo.setData("请上传文件");
            return jsonResultVo;
        }
        String fileName = file.getOriginalFilename();
        boolean isExcel = fileName == null ||
                (!fileName.endsWith(RebateConstants.SUFFIX_2003) && !fileName.endsWith(RebateConstants.SUFFIX_2007));
        if(isExcel){
            jsonResultVo.setData("当前文件不是.xls或.xlsx类型文件");
            return jsonResultVo;
        }
        if(StringUtils.isBlank(paramId) || StringUtils.isBlank(selectedObj)){
            jsonResultVo.setData("导入失败");
            return jsonResultVo;
        }
        ParamSetDO paramSetDO = paramSetService.getById(paramId);
        if(paramSetDO == null || paramSetDO.getId() == null){
            jsonResultVo.setData("当前参数不存在，导入失败");
            return jsonResultVo;
        }
        //return null;
        return paramBusinessDataService.commit(file, paramId, selectedObj);
    }
}
