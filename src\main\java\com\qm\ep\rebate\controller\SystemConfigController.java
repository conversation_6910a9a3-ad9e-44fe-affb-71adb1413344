package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.SystemConfigDO;
import com.qm.ep.rebate.service.SystemConfigService;
import com.qm.ep.rebate.service.workbench.WorkbenchService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/systemConfig")
@Tag(name = "系统参数")
public class SystemConfigController extends BaseController {

    @Resource
    private SystemConfigService systemConfigService;



    @Operation(summary = "通过参数code获取参数值", description = "[author: 10200571]")
    @PostMapping("/getValueByCode")
    public JsonResultVo<String> getValueByCode(@RequestBody String code) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(systemConfigService.getValueByCode(code));
        return jsonResultVo;
    }

    @Operation(summary = "获取全部系统参数", description = "[author: 10200571]")
    @PostMapping("/list")
    public JsonResultVo<List<SystemConfigDO>> list() {
        JsonResultVo<List<SystemConfigDO>> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(systemConfigService.list());
        return jsonResultVo;
    }

    @Operation(summary = "获取全部系统参数", description = "[author: 10200571]")
    @PostMapping("/getDataVersion")
    public JsonResultVo<String> getDataVersion() {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(systemConfigService.getDataVersion());
        return jsonResultVo;
    }

    @Operation(summary = "初始化待办任务实例编码", description = "[author: 10200571]")
    @GetMapping("/initTaskCode")
    public JsonResultVo<String> initTaskCode(@RequestParam(required = true) String userCode) {
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        jsonResultVo.setData(systemConfigService.initTaskCode(userCode));
        return jsonResultVo;
    }


}
