package com.qm.ep.rebate.domain.dto.budget.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "预算申请列表")
@Data
public class BgtApplyListOutDTO {

    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-预算申请单名")
    private String applyName;

    @Schema(description = "数据-预算申请类型（0-常规调整，1-专项调整）")
    private String applyType;

    @Schema(description = "数据-申请状态（10-初始、11-提交、12-通过、13驳回）")
    private String applyStatus;

    @Schema(description = "数据-申请数")
    private BigDecimal applyValue;

    @Schema(description = "数据-申请日期")
    private String applyTime;

    @Schema(description = "数据-姓名")
    private String submitName;

}
