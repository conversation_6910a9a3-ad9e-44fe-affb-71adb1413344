package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 政策兑付审计
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Schema(description = "政策兑付审计")
@Getter
@Setter
@TableName("policy_audit")
public class PolicyAuditPO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 政策编码
     */
    @Schema(description = "政策编码")
    @TableField("policyCode")
    private String policyCode;

    /**
     * 触发审计时的政策id
     */
    @Schema(description = "触发审计时的政策id")
    @TableField("policyIds")
    private String policyIds;

    /**
     * 发起部门
     */
    @Schema(description = "发起部门")
    @TableField("department")
    private String department;

    /**
     * 发起人
     */
    @Schema(description = "发起人")
    @TableField("creator")
    private String creator;

    /**
     * 发起审计时间
     */
    @Schema(description = "发起审计时间")
    @TableField("auditTime")
    private String auditTime;

    /**
     * 确认结果
     */
    @Schema(description = "确认结果")
    @TableField("confirmResult")
    private String confirmResult;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建时间")
    @TableField(value ="createOn" ,fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateOn;

    @Schema(description = "数据-数据DTSTAMP")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    private Timestamp dtstamp;


    /**
     * 一键审计触发的政策配置待办Code
     */
    @Schema(description = "一键审计触发的政策配置待办Code")
    @TableField(value = "taskInstanceCode")
    private String taskInstanceCode;
}
