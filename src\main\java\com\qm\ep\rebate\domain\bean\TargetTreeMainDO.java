package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.ep.rebate.enumerate.YesOrNoEnum;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("TARGET_TREE_MAIN")
@Schema(description = "指标树主表")
public class TargetTreeMainDO implements Serializable {
    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-父级主键")
    @TableField("parentId")
    private String parentId;

    @Schema(description = "数据-踪迹：记录所有上级ID并用逗号分隔")
    @TableField("trace")
    private String trace;

    @Schema(description = "数据-组织")
    @TableField("org")
    private String org;

    @Schema(description = "数据-指标名称")
    @TableField("targetName")
    private String targetName;

    @Schema(description = "数据-指标编码")
    @TableField("targetCode")
    private String targetCode;

    @Schema(description = "数据-唯一别名")
    @TableField("uniqueName")
    private String uniqueName;

    @Schema(description = "数据-层级")
    @TableField("level")
    private Integer level;

    @Schema(description = "数据-指标值")
    @TableField("targetValue")
    private String targetValue;

    @Schema(description = "数据-单位")
    @TableField("unit")
    private String unit;

    @Schema(description = "数据-行业数据")
    @TableField("industryData")
    private String industryData;

    @Schema(description = "数据-多维度")
    @TableField("multiDimensional")
    private YesOrNoEnum multiDimensional;

    @Schema(description = "数据-是否停用")
    @TableField("stopStatus")
    private YesOrNoEnum stopStatus;

    @Schema(description = "数据-停用时间")
    @TableField("stopDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date stopDate;

    @Schema(description = "数据-描述")
    @TableField("remark")
    private String remark;

    @Schema(description = "数据-创建者")
    @TableField(value = "createBy", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "createOn", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新者")
    @TableField(value = "updateBy", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

    @Schema(description = "数据-维度")
    @TableField(exist = false)
    private List<TargetTreeDimensionDO> dimensionList;

}
