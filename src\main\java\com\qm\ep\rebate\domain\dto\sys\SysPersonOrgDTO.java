package com.qm.ep.rebate.domain.dto.sys;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据:SysPersonOrgDTO")
@ToString(callSuper = true)
public class SysPersonOrgDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-个人 ID")
    private String personId;

}