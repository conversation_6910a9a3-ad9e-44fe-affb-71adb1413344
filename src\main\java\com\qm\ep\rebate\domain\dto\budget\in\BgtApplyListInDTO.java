package com.qm.ep.rebate.domain.dto.budget.in;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "数据:预算申请列表")
public class BgtApplyListInDTO extends JsonParamDto {

    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-预算申请单名")
    private String applyName;

    @Schema(description = "数据-预算申请类型（0-常规调整，1-专项调整）")
    private String applyType;

    @Schema(description = "数据-申请状态（10-初始、11-提交、12-通过、13驳回）")
    private String applyStatus;

    @Schema(description = "数据-申请日期")
    private String applyBeginTime;

    @Schema(description = "数据-申请日期")
    private String applyEndTime;

}
