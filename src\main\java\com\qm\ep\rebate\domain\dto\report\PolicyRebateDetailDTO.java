package com.qm.ep.rebate.domain.dto.report;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目名称：yiqi-project
 * 类 名 称：PolicyRebateDetailDTO
 * 类 描 述：TODO
 * 创建时间：2024/2/22 下午12:57
 * 创 建 人：cuihaochuan
 */
@Data
@Schema(description = "数据:政策返利明细")
public class PolicyRebateDetailDTO {
    @Schema(description = "数据-名称")
    private String name;

    @Schema(description = "数据-金额")
    private BigDecimal amount;
}
