package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:返利响应")
@Data
public class RebateResponse {
    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "系列名称")
    private String seriesName;

    @Schema(description = "量")
    private Long amount;

    @Schema(description = "唯一键")
    private String uniqueKey;

    @Schema(description = "结算时间")
    private String  settleTime;
}
