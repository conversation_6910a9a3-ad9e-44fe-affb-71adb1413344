package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "数据:数据BGT 调整查询响应")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BgtAdjustQueryResponse {

    /**
     * 预算类型
     */
    @Schema(description = "预算类型")
    private String bgtType;
    /**
     * 返利项目代码
     */
    @Schema(description = "返利项目代码")
    private String classItem;

    /**
     * 返利项目名称
     */
    @Schema(description = "返利项目名称")
    private String classItemName;


    /**
     * 需要增加的点数提示
     */
    @Schema(description = "需要增加的点数提示")
    private List<Clue> clues;

    @Schema(description = "数据:线索")
    @Data
    public static  class Clue {
        /**
         * 车系
         */
        @Schema(description = "车系")
        private String series;
        /**
         * 调整的点数或金额
         * 如果bgtType为0，pointOrAmount为点数(******前端不需要乘100，直接加%显示即可*************)
         * 如果bgtType为1，pointOrAmount为金额
         */
        @Schema(description = "调整的点数或金额 如果bgtType为0，pointOrAmount为点数(******前端不需要乘100，直接加%显示即可*************) 如果bgtType为1，pointOrAmount为金额")
        private BigDecimal pointOrAmount;
    }



}
