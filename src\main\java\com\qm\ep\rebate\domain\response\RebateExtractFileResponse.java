package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 返利折让提报材料表
 *  
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Schema(description = "返利折让提报材料表  ")
@Getter
@Setter
public class RebateExtractFileResponse implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer fileId;

    /**
     * 文件用途（F01-业务上传，F02-经销商上传）
     */
    @Schema(description = "文件用途（F01-业务上传，F02-经销商上传）")
    private String fileUse;

    /**
     * 文件id
     */
    @Schema(description = "文件id")
    private String objectKey;

    /**
     * 返利折让申请单id
     */
    @Schema(description = "返利折让申请单id")
    private Long applyId;

    /**
     * 附件类型
     */
    @Schema(description = "附件类型")
    private String fileType;

    /**
     * 附件名称
     */
    @Schema(description = "附件名称")
    private String fileName;

    /**
     * 是否ai审核
     */
    @Schema(description = "是否ai审核")
    private String canAi;

    /**
     * ai审核结果
     */
    @Schema(description = "数据ai审核结果")
    private String canAiResult;

    /**
     * ai审核备注
     */
    @Schema(description = "数据ai审核备注")
    private String aiRemark;

    /**
     * 是否人工审核
     */
    @Schema(description = "是否人工审核")
    private String canManual;

    /**
     * 人工审核备注
     */
    @Schema(description = "人工审核备注")
    private String manualRemark;

    /**
     * 附件审核通过结果
     */
    @Schema(description = "附件审核通过结果")
    private String canPass;

    /**
     * 人工审核结果
     */
    @Schema(description = "人工审核结果")
    private String canManualResult;
    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    private LocalDateTime createOn;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateOn;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private LocalDateTime dtstamp;

    /**
     * 上传人
     */
    @Schema(description = "上传人")
    private String operator;


}
