package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 计算公式明细表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value ="formuladetail")
@Schema(description = "数据:计算公式明细表对象")
public class FormulaDetailDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-计算公式主表id")
    @TableField("CALID")
    private String calid;

    @Schema(description = "数据-计算因子名")
    @TableField("TABLEID")
    private String tableid;

    @Schema(description = "数据-字段名")
    @TableField("FIELDNAME")
    private String fieldname;

    @Schema(description = "数据-别名")
    @TableField("ALIASNAME")
    private String aliasname;

    @Schema(description = "数据-字段类型（输出字段）")
    @TableField("TYPE")
    private String type;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createby;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createon;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateby;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateon;

}