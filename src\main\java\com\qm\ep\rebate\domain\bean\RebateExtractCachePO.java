package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *  
 * 
 *  
 *
 * <AUTHOR>
 * @since 2024-08-24
 */
@Getter
@Setter
@TableName("rebate_extract_cache")
public class RebateExtractCachePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型
     */
    @TableField("cache_type")
    private String cacheType;

    /**
     * 备注
     */
    @TableField("cache_remark")
    private String cacheRemark;

    /**
     * 年
     */
    @TableField("cache_year")
    private String cacheYear;

    /**
     * 月
     */
    @TableField("cache_month")
    private String cacheMonth;

    /**
     * 数据1
     */
    @TableField("field1")
    private String field1;

    /**
     * 数据2
     */
    @TableField("field2")
    private String field2;

    /**
     * 数据3
     */
    @TableField("field3")
    private String field3;


}
