package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("common_rank")
@Schema(description = "通用排序对象")
public class RankDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策id")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "数据-对象主键")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "数据-对象类型")
    @TableField("objectType")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "数据-别名")
    @TableField("alias")
    private String alias;

    @Schema(description = "数据-排名字段")
    @TableField("orderField")
    private String orderField;

    @Schema(description = "数据-分组字段")
    @TableField("groupField")
    private String groupField;

    @Schema(description = "数据-排序规则")
    @TableField("sort")
    private String sort;

    @Schema(description = "数据-排名规则")
    @TableField("logic")
    private String logic;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-数据DTSTAMP")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;
}
