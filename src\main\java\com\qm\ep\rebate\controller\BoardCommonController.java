package com.qm.ep.rebate.controller;


import com.qm.ep.rebate.service.BoardCommonService;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 *  前端控制器
 *  
 *
 * <AUTHOR>
 * @since 2024-04-13
 */
@Tag(name = "/board-common", description = "[author: 10200571]")
@RestController
@Slf4j
@RequestMapping("/board-common")
public class BoardCommonController {

    @Autowired
    private BoardCommonService boardCommonService;

    @Operation(summary = "接口：政策人员效能星级日报", description = "[author: 10200571]")
    
    @GetMapping("/runPersonalEfficiencySchedule")
    public JsonResultVo<String> runPersonalEfficiencySchedule(String sealDate) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        boardCommonService.runPersonalEfficiencySchedule(sealDate);
        ret.setData("schedule ok");
        return ret;
    }


}
