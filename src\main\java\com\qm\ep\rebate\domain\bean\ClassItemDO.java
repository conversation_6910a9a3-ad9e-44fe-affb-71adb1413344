package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.ep.rebate.common.LogColumnAnnotation;
import com.qm.ep.rebate.domain.dto.ClassItemDeptDTO;
import com.qm.ep.rebate.enumerate.AccountEntryMethodEnum;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CLASS_ITEM")
@Schema(description = "数据:返利项目")
public class ClassItemDO implements Serializable {



    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-公式代码")
    @TableField("companyId")
    private String companyId;

    @LogColumnAnnotation("返利项目代码")
    @Schema(description = "数据-返利项目代码")
    @TableField("classItem")
    private String classItem;

    @LogColumnAnnotation("返利项目名称")
    @Schema(description = "数据-返利项目名称")
    @TableField("classItemName")
    private String classItemName;

    @LogColumnAnnotation("入账方式")
    @Schema(description = "数据-入账方式")
    @TableField("accountMode")
    private AccountEntryMethodEnum accountMode;

    @Schema(description = "数据-对应信用账户")
    @TableField("creditAcct")
    private String creditAcct;

    @LogColumnAnnotation("是否停用")
    @Schema(description = "数据-对应信用账户")
    @TableField("stop")
    private String stop;

    @Schema(description = "数据-性质")
    @TableField("property")
    private String property;

    @LogColumnAnnotation("预算类型")
    @Schema(description = "数据-预算类型:0-常规，1-专项")
    @TableField("bgt_type")
    private String bgtType;

    @LogColumnAnnotation("业务类型")
    @Schema(description = "数据-对应账号（0- 返利，1-代理制保证金折让，2-代理制佣金）")
    @TableField("rebate_type")
    private String rebateType;

    @Schema(description = "数据-停用日期")
    @TableField("stopDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date stopDate;

    @Schema(description = "数据-创建者")
    @TableField(value = "createBy", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "createOn", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "updateBy", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;


    @LogColumnAnnotation("税率")
    @Schema(description = "数据-税")
    @TableField("tax")
    private String tax;

    @Schema(description = "数据-类项目代码")
    @TableField(exist = false)
    private String classItemCode;
    @Schema(description = "数据-类项代码名称")
    @TableField(exist = false)
    private String classItemCodeName;

    @Schema(description = "数据-关联部门集合")
    @TableField(exist = false)
    private List<ClassItemDeptDTO> deptList;


}
