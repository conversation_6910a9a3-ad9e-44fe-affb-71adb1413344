package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.service.AimDetailService;
import com.qm.ep.rebate.service.AimService;
import com.qm.ep.rebate.service.AimUserService;
import com.qm.tds.api.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/aim")
@Tag(name = "业务目标", description = "[author: 10200571]")
public class AimController extends BaseController {

    @Resource
    private AimService aimService;
    @Resource
    private AimDetailService aimDetailService;
    @Resource
    private AimUserService aimUserService;

    // @Operation(summary = "查询总体预算编制列表", description = "[author: 10200571]")
    // @PostMapping("/getAimList")
    // public JsonResultVo<List<AimDO>> getAimList(@RequestBody AimDTO aimDTO){
    //     JsonResultVo<List<AimDO>> jsonResultVo = new JsonResultVo<>();
    //     QmQueryWrapper<AimDO> queryWrapper = new QmQueryWrapper<>();
    //     LambdaQueryWrapper<AimDO> lambdaWrapper = queryWrapper.lambda();
    //     lambdaWrapper.eq(StrUtil.isNotBlank(aimDTO.getOrgCode()), AimDO::getOrgCode, aimDTO.getOrgCode())
    //             .eq(StrUtil.isNotBlank(aimDTO.getAimVersion()), AimDO::getAimVersion, aimDTO.getAimVersion())
    //             .eq(StrUtil.isNotBlank(aimDTO.getStatus()),AimDO::getStatus,aimDTO.getStatus())
    //             .eq(StrUtil.isNotBlank(aimDTO.getNotifyNo()),AimDO::getNotifyNo,aimDTO.getNotifyNo())
    //             .ge(StrUtil.isNotBlank(aimDTO.getStartTime()),AimDO::getCreateOn,aimDTO.getStartTime())
    //             .le(StrUtil.isNotBlank(aimDTO.getEndTime()),AimDO::getCreateOn,aimDTO.getEndTime()+" 23:59:59");
    //     List<AimDO> data = aimService.list(queryWrapper);
    //     for(AimDO aimDO : data){
    //         aimDO.setUserNames(aimService.getUserNames(aimDO.getId()));
    //     }
    //     jsonResultVo.setData(data);
    //     return jsonResultVo;
    // }

    // @Operation(summary = "查询总体预算编制详情列表", description = "[author: 10200571]")
    // @PostMapping("/getAimDetailList")
    // public JsonResultVo<List<AimDetailDO>> getAimDetailList(@RequestBody AimDTO aimDTO){
    //     JsonResultVo<List<AimDetailDO>> jsonResultVo = new JsonResultVo<>();
    //     QmQueryWrapper<AimDO> queryWrapper = new QmQueryWrapper<>();
    //     LambdaQueryWrapper<AimDO> lambdaWrapper = queryWrapper.lambda();
    //     lambdaWrapper.eq(AimDO::getOrgCode, aimDTO.getOrgCode())
    //             .eq(AimDO::getAimVersion, aimDTO.getAimVersion());
    //     AimDO one = aimService.getOne(queryWrapper);
    //     jsonResultVo.setData(one.getDetails());
    //     return jsonResultVo;
    // }
    //
    //
    // @Operation(summary = "获取组织下可用的业务目标版本号列表", description = "[author: 10200571]")
    // @PostMapping("/getAimVersionList")
    // public JsonResultVo<List<String>> getAimVersionList(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<List<String>> ret = new JsonResultVo<>();
    //     List<String> aimVersionList = aimService.getAimVersionList(aimDTO);
    //     ret.setData(aimVersionList);
    //     return ret;
    // }
    // @Operation(summary = "获取详细信息", description = "[author: 10200571]")
    // @GetMapping("/getAimById")
    // public JsonResultVo<AimDO> getAimById(@RequestParam("id") String id) {
    //     JsonResultVo<AimDO> ret = new JsonResultVo<>();
    //     AimDO aimDO = aimService.getById(id);
    //     QmQueryWrapper<AimDetailDO> queryWrapper = new QmQueryWrapper<>();
    //     LambdaQueryWrapper<AimDetailDO> lambdaWrapper = queryWrapper.lambda();
    //     lambdaWrapper.eq(AimDetailDO::getAimId,id);
    //     List<AimDetailDO> detailDOS = aimDetailService.list(lambdaWrapper);
    //     aimDO.setDetails(detailDOS);
    //     List<AimUserDO> userDOS = aimService.getAimUserList(aimDO.getId());
    //     aimDO.setUsers(userDOS);
    //     ret.setData(aimDO);
    //     return ret;
    // }
    // @Operation(summary = "保存下达", description = "[author: 10200571]")
    // @PostMapping("/saveAim")
    // public JsonResultVo<AimDO> saveAim(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<AimDO> ret = new JsonResultVo<>();
    //     AimDO aimDO = new AimDO();
    //     if(StrUtil.isNotBlank(aimDTO.getId()) && StrUtil.isNotEmpty(aimDTO.getId())){
    //         aimDO = aimService.getById(aimDTO.getId());
    //         aimService.deleteDetail(aimDTO.getId());
    //     }
    //     BeanUtils.copyProperties(aimDTO,aimDO);
    //     // 下达
    //     if("02".equals(aimDTO.getStatus())){
    //         aimDO.setSendDate(new Date());
    //     }
    //     aimDO.setDtstamp(new Date());
    //     aimService.saveOrUpdate(aimDO);
    //     List<AimDetailDO> detailDOS = aimDTO.getDetails();
    //     List<AimUserDO> userDOS = aimDTO.getUsers();
    //     if(!detailDOS.isEmpty()){
    //         detailDOS.forEach(item->{
    //             item.setId(null);
    //             item.setAimId(aimDTO.getId());
    //         });
    //         aimDetailService.saveBatch(detailDOS);
    //     }
    //     if(!userDOS.isEmpty()) {
    //         userDOS.forEach(item->{
    //             item.setId(null);
    //             item.setAimId(aimDTO.getId());
    //         });
    //         aimUserService.saveBatch(userDOS);
    //     }
    //     ret.setData(aimDO);
    //     return ret;
    // }
    // @Operation(summary = "下达", description = "[author: 10200571]")
    // @PostMapping("/send")
    // public JsonResultVo<AimDO> send(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<AimDO> jsonResultVo = new JsonResultVo<>();
    //     AimDO aimDO = new AimDO();
    //     if(StrUtil.isNotBlank(aimDTO.getId()) && StrUtil.isNotEmpty(aimDTO.getId())){
    //         aimDO = aimService.getById(aimDTO.getId());
    //         BeanUtils.copyProperties(aimDTO,aimDO);
    //         aimDO.setSendDate(new Date());
    //         aimService.saveOrUpdate(aimDO);
    //     }else{
    //         jsonResultVo.setMsgErr("未找到业务目标主键");
    //         return jsonResultVo;
    //     }
    //     return jsonResultVo;
    // }
    // @Operation(summary = "删除记录", description = "[author: 10200571]")
    // @PostMapping("/deleteByIds")
    // public JsonResultVo deleteByIds(@RequestBody List<String> deleteIds) {
    //     JsonResultVo<Object> jsonResultVo = new JsonResultVo<>();
    //     for(String id:deleteIds) {
    //         AimDO aimDO = aimService.getById(id);
    //         if("02".equals(aimDO.getStatus())){
    //             jsonResultVo.setMsgErr("下达状态不允许删除");
    //             return jsonResultVo;
    //         }
    //         aimService.deleteDetail(id);
    //         aimService.removeById(id);
    //     }
    //     return jsonResultVo;
    // }
}
