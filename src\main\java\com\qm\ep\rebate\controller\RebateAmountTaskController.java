package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.enumerate.RebateSyncAmountGroupTypeEnum;
import com.qm.ep.rebate.service.impl.RebateAmountTaskServiceFactory;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目名称：yiqi-project
 * 类 名 称：RebateAmountTaskController
 * 创建时间：2024/2/21 下午4:02
 * 创 建 人：cuihaochuan
 */
@Tag(name ="返利金额数据推送任务入口")
@RestController
@RequestMapping("/rebateAmount")
public class RebateAmountTaskController {

    // @Autowired
    // private IRebateAmountTaskService rebateAmountTaskService;

    @Autowired
    private RebateAmountTaskServiceFactory rebateAmountTaskServiceFactory;


    // @Operation(summary = "接口：任务触发", description = "[author: 10200571]")
    // @PostMapping("/pushTaskV2")
    // public JsonResultVo pushTaskV2(){
    //     JsonResultVo<Object> ret = new JsonResultVo<>();
    //     rebateAmountTaskServiceFactory.getIRebateAmountTaskServiceImplByType(RebateSyncAmountGroupTypeEnum.REGION_GROUP.getCode()).executeRebateAmountTask();
    //     return ret;
    // }

    @Operation(summary = "任务触发", description = "[author: 10200571]")
    @PostMapping("/pushTask")
    public JsonResultVo pushTask(){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        rebateAmountTaskServiceFactory.getIRebateAmountTaskServiceImplByType(RebateSyncAmountGroupTypeEnum.REGION_GROUP.getCode()).executeRebateAmountTask();
        return ret;
    }

    @Operation(summary = "每日经销商简报-维度任务触发", description = "[author: 10200571]")
    @PostMapping("/pushVdealerTask")
    public JsonResultVo pushVdealerTask(){
        JsonResultVo<Object> ret = new JsonResultVo<>();
        rebateAmountTaskServiceFactory.getIRebateAmountTaskServiceImplByType(RebateSyncAmountGroupTypeEnum.VDEALER_GROUP.getCode()).executeRebateAmountTask();
        return ret;
    }

}
