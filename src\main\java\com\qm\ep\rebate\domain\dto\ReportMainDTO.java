package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "报表明细主表对象")
public class ReportMainDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-报表名称")
    private String reportName;

    @Schema(description = "数据-兑付对象")
    private String cashObject;

    @Schema(description = "数据-经销商代码字段")
    private String dealerCodeField;

    @Schema(description = "数据-合计字段")
    private String sumField;

    @Schema(description = "数据-合计字段")
    private List<String> sumFieldList;

    @Schema(description = "数据-输出字段")
    private List<String> outputFieldList;

    @Schema(description = "数据-报表描述")
    private String description;

    @Schema(description = "数据-关联方案")
    private List<ReportJoinDTO> reportJoinDTOList;

    @Schema(description = "数据-公式内容")
    private List<ReportFormulaContentDTO> reportFormulaContentDTOList;

    @Schema(description = "数据-筛选条件")
    private List<ReportConditionDTO> reportConditionDTOList;

    @Schema(description = "数据-查询条件-开始时间")
    private Date startTime;

    @Schema(description = "数据-查询条件-结束时间")
    private Date endTime;

    @Schema(description = "数据-查询条件-日期类型")
    private String dateType;

    @Schema(description = "数据-经销商代码")
    private String dealerCode;
}