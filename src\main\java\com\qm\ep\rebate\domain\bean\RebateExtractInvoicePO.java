package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * 红字发票信息
 *
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Getter
@Setter
@TableName("rebate_extract_invoice")
public class RebateExtractInvoicePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 折让申请单id
     */
    @TableField("apply_id")
    private Integer applyId;

    /**
     * 红色发票信息编号
     */
    @TableField("invoice_number")
    private String invoiceNumber;


}
