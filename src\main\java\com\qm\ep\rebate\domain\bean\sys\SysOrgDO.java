package com.qm.ep.rebate.domain.bean.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYSC060")
@Schema(description = "数据:系统组织机构表")
public class SysOrgDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "数据-机构代码")
    @TableField("VINSTCODE")
    private String vinstCode;

    @Schema(description = "数据-机构名称")
    @TableField("VINSTTEXT")
    private String vinstText;

    @Schema(description = "数据-机构类型")
    @TableField("VINSTTYPE")
    private String vinstType;

    @Schema(description = "数据-父级机构ID")
    @TableField("NPARENTINSTID")
    private String nparentInstId;

    @Schema(description = "数据-公司ID")
    @TableField("NCOMPANYID")
    private String ncompanyId;

    @Schema(description = "数据-负责人")
    @TableField("VPRINCIPAL")
    private String vprincipal;

    @Schema(description = "数据-联系人")
    @TableField("VLINKMAN")
    private String vlinkMan;

    @Schema(description = "数据-固定电话")
    @TableField("VTEL")
    private String vtel;

    @Schema(description = "数据-移动电话")
    @TableField("VMOBILE")
    private String vmobile;

    @Schema(description = "数据-停用标识")
    @TableField("VSTOP")
    private String vstop;

    @Schema(description = "数据-停用日期")
    @TableField("DSTOP")
    private Date dstop;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

}
