package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.qm.ep.rebate.enumerate.AuditObjectTypeEnum;
import com.qm.ep.rebate.enumerate.AuditTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("AUDIT_TRAIL")
@Schema(description = "数据:审核轨迹表对象")
public class AuditTrailDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-审核类型")
    @TableField("auditType")
    private AuditTypeEnum auditType;

    @Schema(description = "数据-审核对象ID")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "数据-审核对象类型")
    @TableField("objectType")
    private AuditObjectTypeEnum objectType;

    @Schema(description = "数据-操作前缀：cancel：取消")
    @TableField("prefix")
    private String prefix;

    /**
     * 节点状态
     */
    @Schema(description = "数据-审核类型描述")
    @TableField("typeDesc")
    private String typeDesc;

    @Schema(description = "数据-处理时间")
    @TableField(value = "time")
    private Date time;

    @Schema(description = "数据-处理人ID")
    @TableField(value = "userId")
    private String userId;

    @Schema(description = "数据-处理人名称")
    @TableField(value = "userName")
    private String userName;

    @Schema(description = "数据-处理意见")
    @TableField(value = "fullMessage")
    private String fullMessage;

    @Schema(description = "数据-预留状态")
    @TableField(value = "status")
    private Integer status;
}
