package com.qm.ep.rebate.domain.dto.budget.in;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(description = "数据:实体类-DTO 中预算策略列表")
@Data
public class BudgetPolicyListInDTO extends JsonParamDto {

    @Schema(description = "数据-主键")
    private Integer id;

    private List<String> seriesList;

    private String year;

    private String timeType;

    private String classItem;

}
