package com.qm.ep.rebate.controller;

import com.alibaba.fastjson.JSONObject;
import com.qm.ep.rebate.service.approve.impl.BpmCallBackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "/bpmcallback", description = "[author: 10200571]")
@RestController
@RequestMapping("/bpmcallback")
public class BpmCallBackController {
    /**
     * 节点动作监听：一个应用只需要注册一个节点动作监听
     * 即使有多个分类，也使用一个监听（在工作流服务配置中每个分类也都配置这个监听），达到中转分发的效果
     *
     * @param entity json格式的参数 由BPM发送 需要转化成JSONObject处理
     * @return JSONObject格式 包含status msgCode msg
     * 成功示例{"status":"1","msgCode":"200","msg":"操作成功"}
     */
    @Operation(summary = "接口：}", description = "[author: 10200571]")
    @RequestMapping(value = "/messageSendListener", method = RequestMethod.POST)
    public JSONObject messageSendListener(@RequestBody String entity) {
        JSONObject entityJson = JSONObject.parseObject(entity);
        return BpmCallBackService.messageSendListener(entityJson);
    }

    /**
     * 流程监听：任务被创建时向当前任务参与者推送待办消息，根据报文消息判断流程是否为创建状态，同时设置业务对象为审批中状态
     * 第一种正常通过结束情况，如果流程监听事件为结束end，且结束原因为空，在此种情况下设置单据状态为通过，给流程提交人发送流程通过消息。
     * 第二种被驳回情况，如果流程监听事件为结束end，且结束原因为REJECTTOSTART，在此种情况下设置单据状态为驳回，给流程提交人发送流程通过消息。
     *
     * @param entity json格式的参数 由BPM发送 需要转化成JSONObject处理
     * @return JSONObject格式 包含status msgCode msg
     * 成功示例{"status":"1","msgCode":"200","msg":"操作成功"}
     */
    @Operation(summary = "接口：}", description = "[author: 10200571]")
    @RequestMapping(value = "/processListener", method = RequestMethod.POST)
    public JSONObject processListener(@RequestBody String entity) {
        JSONObject entityJson = JSONObject.parseObject(entity);
        return BpmCallBackService.processListener(entityJson);
    }

    /**
     * 调用服务监听：根据servicecode区分服务节点所调用的服务
     * 这里的servicecode就是在流程模板中服务节点上配置的服务code
     *
     * @param entity json格式的参数 由BPM发送 需要转化成JSONObject处理
     * @return JSONObject格式 包含status msgCode msg
     * 成功示例{"status":"1","msgCode":"200","msg":"操作成功"}
     */
    @Operation(summary = "接口：}", description = "[author: 10200571]")
    @RequestMapping(value = "/serviceCallbackListener", method = RequestMethod.POST)
    public JSONObject serviceCallbackListener(@RequestBody String entity) {
        JSONObject entityJson = JSONObject.parseObject(entity);
        return BpmCallBackService.serviceCallbackListener(entityJson);
    }
}
