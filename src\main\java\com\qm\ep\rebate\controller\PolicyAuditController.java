package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.common.aop.idempotent.IdempotentLock;
import com.qm.ep.rebate.domain.bean.PolicyAuditPO;
import com.qm.ep.rebate.domain.request.EntryAccountDataRequest;
import com.qm.ep.rebate.domain.request.PolicyAuditRequest;
import com.qm.ep.rebate.domain.response.PolicyAuditDetailResponse;
import com.qm.ep.rebate.domain.response.SpecialFactorResponse;
import com.qm.ep.rebate.service.EntryAccountDataService;
import com.qm.ep.rebate.service.PolicyAuditService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 政策一键审计控制器
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Tag(name = "政策一键审计")
@RestController
@RequestMapping("/policyAudit")

@Slf4j
public class PolicyAuditController extends BaseController {

    @Resource
    private PolicyAuditService policyAuditService;

    @Resource
    private EntryAccountDataService entryAccountDataService;



    @Operation(summary = "申请入账-特殊因子校验列表页", description = "[author: ********]")
    @PostMapping("/specialFactorValidateList")
    public JsonResultVo<SpecialFactorResponse> specialFactorValidateList(@Valid @RequestBody EntryAccountDataRequest request) {
        JsonResultVo<SpecialFactorResponse> resultVo = new JsonResultVo<>();
        resultVo.setData(entryAccountDataService.specialFactorValidateList(request.getUniqueKey()));
        return resultVo;
    }

    /**
     * 政策一键审计
     */
    @Operation(summary = "政策一键审计", description = "[author: ********]")
    @PostMapping("/startAuditByPolicyCode")
    @IdempotentLock(expression = "#policyCode", duration = 10L)
    public JsonResultVo<String> startAuditByPolicyCode(@Valid @RequestBody PolicyAuditRequest request) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setData(policyAuditService.startAuditByPolicyCode(request.getPolicyCode()));
        return ret;
    }

    /**
     * 政策审计结果跟踪列表
     */
    @Operation(summary = "政策审计结果跟踪列表", description = "[author: ********]")
    @PostMapping("/auditTrackList")
    public JsonResultVo<QmPage<PolicyAuditPO>> auditTrackList( @RequestBody PolicyAuditRequest request) {
        JsonResultVo<QmPage<PolicyAuditPO>> ret = new JsonResultVo<>();
        ret.setData(policyAuditService.auditTrackList(request));
        return ret;
    }

    /**
     * 审计结果详情
     */
    @Operation(summary = "审计结果详情", description = "[author: ********]")
    @PostMapping("/auditDetailById")
    public JsonResultVo<PolicyAuditDetailResponse> auditDetailById(@RequestBody PolicyAuditRequest request) {
        JsonResultVo<PolicyAuditDetailResponse> ret = new JsonResultVo<>();
        ret.setData(policyAuditService.auditDetailById(request));
        return ret;
    }

    /**
     * 审计结果详情-确认处理&无需处理
     */
    @IdempotentLock(duration = 10L)
    @Operation(summary = "审计结果详情-确认处理&无需处理", description = "[author: ********]")
    @PostMapping("/auditHandle")
    public JsonResultVo<String> auditHandle(@RequestBody PolicyAuditRequest request) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setData(policyAuditService.auditHandle(request));
        return ret;
    }

}
