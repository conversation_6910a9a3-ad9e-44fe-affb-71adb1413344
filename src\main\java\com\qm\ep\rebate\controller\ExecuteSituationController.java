package com.qm.ep.rebate.controller;


import com.qm.ep.rebate.domain.dto.ExecuteSituationDTO;
import com.qm.ep.rebate.service.ExecuteSituationService;
import com.qm.ep.rebate.service.PolicyProdService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/executeSituation")
@Tag(name = "返利政策执行情况", description = "[author: 10200571]")
public class ExecuteSituationController extends BaseController {

    @Autowired
    private ExecuteSituationService executeSituationService;
    @Autowired
    private PolicyProdService policyProdService;
    @Operation(summary = "获取返利项目执行情况", description = "[author: 10200571]")
    @PostMapping("/getList")
    public JsonResultVo<List<Map<String,Object>>> getDataSource(@RequestBody ExecuteSituationDTO executeSituationDTO) {
        JsonResultVo<List<Map<String,Object>>> result = new JsonResultVo<>();
        result.setData(executeSituationService.getList(executeSituationDTO));
        return result;
    }

    @Operation(summary = "获取返利项目对应的政策执行情况", description = "[author: 10200571]")
    @PostMapping("/getDetail")
    public JsonResultVo<List<Map<String,Object>>> getDetail(@RequestBody ExecuteSituationDTO executeSituationDTO) {
        JsonResultVo<List<Map<String,Object>>> result = new JsonResultVo<>();
        result.setData(executeSituationService.getDetail(executeSituationDTO));
        return result;
    }
}
