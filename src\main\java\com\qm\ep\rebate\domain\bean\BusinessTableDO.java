package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("BUSINESS_TABLE")
@Schema(description = "数据:数据对象")
public class BusinessTableDO implements Serializable {
    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-数据源表名")
    @TableField("TABLE_NAME")
    private String tableName;

    @Schema(description = "数据-数据源来源")
    @TableField("sourceFrom")
    private String sourceFrom;

    @Schema(description = "数据-数据源表类型")
    @TableField("TABLE_TYPE")
    private String tableType;

    @Schema(description = "数据-底表结构")
    @TableField(exist=false)
    private List<BusinessConstructionDO> businessConstructions;

    @Schema(description = "数据-停用标识")
    @TableField("STOP")
    private Integer stop;

    @Schema(description = "数据-停用时间")
    @TableField("STOP_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date stopDate;

    @Schema(description = "数据-停用原因")
    @TableField("STOP_REASON")
    private String stopReason;

    @Schema(description = "数据-完成状态")
    @TableField("FINISH_STATE")
    private Integer finishState;

    @Schema(description = "数据-开放状态")
    @TableField("OPEN_STATE")
    private Integer openState;

    @Schema(description = "数据-数据Owner")
    @TableField("dataOwner")
    private String dataOwner;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dtstamp;

    @Schema(description = "数据-最后使用时间")
    @TableField("lastusedate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date lastUseDate;

    @Schema(description = "数据-最后使用人账号")
    @TableField("lastloginaccount")
    private String lastLoginAccount;

    @Schema(description = "数据-最后使用人")
    @TableField("lastusername")
    private String lastUsername;

    @Schema(description = "数据-域账号")
    @TableField("loginaccount")
    private String loginAccount;

    @Schema(description = "数据-部门")
    @TableField("department")
    private String department;

    @Schema(description = "数据-部门id")
    @TableField("departmentid")
    private String departmentId;

    @Schema(description = "数据-特殊标识")
    @TableField("special")
    private String special;

    @Schema(description = "数据-再启动时间")
    @TableField("restartdate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date restartDate;

    @Schema(description = "数据-预警180")
    @TableField("warn1")
    private String warn1;

    @Schema(description = "数据-预警270")
    @TableField("warn2")
    private String warn2;

    @Schema(description = "数据-预警330")
    @TableField("warn3")
    private String warn3;

    @Schema(description = "数据-停用360")
    @TableField("deactivate1")
    private String deactivate1;

    @Schema(description = "数据-停用90")
    @TableField("deactivate2")
    private String deactivate2;

}
