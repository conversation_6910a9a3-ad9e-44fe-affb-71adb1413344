package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Schema(description = "数据:回扣 cal 响应")
@Data
public class RebateCalResponse {

    @Schema(description = "全系列返利 CAL 结果")
    private SeriesRebateCalResult allSeriesRebateCalResult;

    @Schema(description = "系列回扣 CAL 结果")
    private List<SeriesRebateCalResult> seriesRebateCalResults;



    /**
     * 车型返利计算结果(代表整个一行)
     *
     * <AUTHOR>
     * @date 2023/10/08
     */
    @Schema(description = "数据:车型返利计算结果(代表整个一行)")
    @Data
    public static class SeriesRebateCalResult {
        @Schema(description = "系列名称")
        private String seriesName;
        @Schema(description = "数据AAK")
        private int aak;
        @Schema(description = "预估回扣")
        private long estimatedRebate;
        @Schema(description = "平均回扣")
        private long averageRebate;
        @Schema(description = "额外回扣 CAL 结果")
        private List<ExtraRebateCalResult> extraRebateCalResults;

    }


}