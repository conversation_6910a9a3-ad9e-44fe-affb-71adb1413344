package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * 
 *
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Schema(description = "数据:实体类-    ")
@Getter
@Setter
public class Salb061PolicyDO implements Serializable {


    @Schema(description = "数据-SALB061 编号")
    private String salb061Id;

    /**
     * 政策编号
     */
    @Schema(description = "数据-政策编号")
    private String policyCode;

    /**
     * 政策名称
     */
    @Schema(description = "数据-政策名称")
    private String policyName;

    /**
     * 不含税金额
     */
    @Schema(description = "数据-不含税金额")
    private String notaxAmount;

    /**
     * 税率
     */
    @Schema(description = "数据-税率")
    private String taxRate;

    /**
     * vin
     */
    @Schema(description = "数据-vin")
    private String vin;

    /**
     * 返利金额
     */
    @Schema(description = "数据-返利金额")
    private String rebateAmount;

    /**
     * 业务类型
     */
    @Schema(description = "数据-业务类型")
    private String rebateType;


}
