package com.qm.ep.rebate.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "数据:实体类-红旗试验细节 DTO")
@Data
public class RedFlagTrialDetailDTO implements Serializable {


    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-红旗伙伴试算主表主键")
    private String calId;

    @Schema(description = "数据-字段来源表名")
    private String tableId;

    @Schema(description = "数据-字段名")
    private String fieldName;

    @Schema(description = "数据-别名")
    private String aliasName;

    @Schema(description = "数据-字段类型（输出字段 =》 关联取值对象，汇总字段 =》 汇总对象，取值字段 =》 取值字段")
    private String type;

    @Schema(description = "数据-创建者")
    private String createBy;

    @Schema(description = "数据-创建日期")
    private Date createOn;

    @Schema(description = "数据-更新者")
    private String updateBy;

    @Schema(description = "数据-更新日期")
    private Date updateOn;
}
