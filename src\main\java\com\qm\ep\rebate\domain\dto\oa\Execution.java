package com.qm.ep.rebate.domain.dto.oa;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:实体类-执行")
@Data
public class Execution{
	@Schema(description = "数据-过程定义主键")
    private String processDefinitionId;
	@Schema(description = "数据-过程实例主键")
    private String processInstanceId;
	@Schema(description = "数据-当前活动主键")
    private String currentActivityId;
	@Schema(description = "数据-租户代码")
    private String tenantCode;
	@Schema(description = "数据-数据ESN 文档")
    private boolean esnDocument;
	@Schema(description = "数据-开始用户主键")
    private String startUserId;
	@Schema(description = "数据-模板名称")
    private String templateName;
	@Schema(description = "数据-应用程序房客主键")
    private String appTenantId;
	@Schema(description = "数据-当前活动名称")
    private String currentActivityName;
	/**
	 * 业务主键
	 */
	@Schema(description = "数据-业务主键")
    private String businessKey;
	@Schema(description = "数据-名字")
    private String name;
	@Schema(description = "数据-房客主键")
    private String tenantId;
	/**
	 * 事件名称
	 */
	@Schema(description = "数据-事件名称")
    private String eventName;
	@Schema(description = "数据-主键")
    private String id;
	@Schema(description = "数据-应用源")
    private String appSource;
	@Schema(description = "数据-类别")
    private String category;
	@Schema(description = "数据-是发送到入门 IM")
    private boolean isSendToStarterIM;
	@Schema(description = "数据-接口参数")
    private String interfaceParam;


	/**
	 * 删除原因
	 */
	@Schema(description = "数据-删除原因")
    private String deleteReason;
}