package com.qm.ep.rebate.domain.bean.sal;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 * 信用单据凭证
 *  
 *
 * <AUTHOR>
 * @since 2021-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "信用单据凭证")
public class SearchRebateDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-经销商代码")
    private String vdealer;

    @Schema(description = "数据-经销商代码名称")
    private String vdealertext;

    @Schema(description = "数据-期初收款")
    private String n1;

    @Schema(description = "数据-收款")
    private String n2;

    @Schema(description = "数据-使用金额")
    private String n3;

    @Schema(description = "数据-账户余额")
    private String n4;

    @Schema(description = "数据-期初占用")
    private String n5;

    @Schema(description = "数据-占用金额")
    private String n6;

    @Schema(description = "数据-可用余额")
    private String n7;

    @Schema(description = "数据-信用额度")
    private String ncreditquota;

    @Schema(description = "数据-资金占用")
    private String noccupyamt;

    @Schema(description = "数据-期初占用")
    private String ninitoccupyamt;

    @Schema(description = "数据-公司ID")
    private String ncompanyid;

    @Schema(description = "数据-单位ID")
    private String customer;

    @Schema(description = "数据-客户ID")
    private String ncustomerid;

    @Schema(description = "数据-信用账户")
    private String credit;

    @Schema(description = "数据-信用账户名称")
    private String vcreditaccttext;

    @Schema(description = "数据-单位标识")
    private String vunitflag;

    @Schema(description = "数据-当前月的开始时间")
    private String beginInit;

    @Schema(description = "数据-起始日期")
    private String begin;

    @Schema(description = "数据-截止日期")
    private String end;

    @Schema(description = "数据-当前时间")
    private String currentDate;

    @Schema(description = "数据-是否显示合计")
    private String displayMode;

    @Schema(description = "数据-系列")
    private String vpdtseries;

    @Schema(description = "数据-返利类型， 0返利，1代理制保证金折让")
    private String rebateType;

    @Schema(description = "数据-是否代理制， 0否，1代理制")
    private String isAgent;


}
