package com.qm.ep.rebate.common.config;


import com.fasterxml.jackson.core.type.TypeReference;
import com.qm.ep.rebate.domain.dto.Result;
import com.qm.ep.rebate.infrastructure.util.HttpUtils;
import com.qm.ep.rebate.infrastructure.util.JsonUtil;
import com.qm.ep.rebate.remote.response.UcgToken;
import com.qm.tds.api.exception.QmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
@Slf4j
public class BaseApiGatewayService {

    @Autowired
    private RebateConfig rebateConfig;


    private static final String UCG_GET_TOKEN_URL = "/ucg/oauth/getToken";

    public String getToken() {

        Map<String, String> params = buildParamsMap(rebateConfig.getAppKey(), String.valueOf(System.currentTimeMillis()));

        String signature = sign(params, rebateConfig.getAppSecret());

        log.info("getToken.signature: {}", signature);

        String response = HttpUtils.sendGet(getGetUrl(params.get("appKey"), params.get("timestamp"), signature),
                null, null);
        log.info("getToken.response: {}", response);

        if (StringUtils.isBlank(response)) {
            throw new QmException("rpc failed");
        }
        Result<UcgToken> result = JsonUtil.jsonToObject(response,
                new TypeReference<>() {
                });

        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            throw new QmException("rpc failed");
        }

        return result.getData().getAccess_token();
    }

    private Map<String, String> buildParamsMap(String appKey, String timestamp) {
        Map<String, String> params = new LinkedHashMap<>();
        params.put("appKey", appKey);
        params.put("timestamp", timestamp);
        return params;
    }

    public String sign(Map<String, String> params, String secretKey) {

        log.info("sign.params: {}", JsonUtil.toJsonString(params));

        Map<String, String> treeMap;
        if (params instanceof TreeMap) {
            treeMap = params;
        } else {
            treeMap = new TreeMap<>(params);
        }

        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : treeMap.entrySet()) {
            stringBuilder.append(entry.getKey()).append(entry.getValue());
        }

        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringBuilder.toString().getBytes(StandardCharsets.UTF_8));
            String base64String = Base64.getEncoder().encodeToString(signData);
            return URLEncoder.encode(base64String, "UTF-8");
        } catch (Exception e) {
            throw new QmException("rpc failed");
        }
    }

    private String getGetUrl(String appKey, String timestamp, String signature) {
        return String.format("%s?appKey=%s&timestamp=%s&signature=%s",
                rebateConfig.getHost() + UCG_GET_TOKEN_URL, appKey, timestamp, signature);
    }

    public String buildApiRequestUrl(String url, String token) {
        return String.format("%s?access_token=%s", url, token);
    }

}
