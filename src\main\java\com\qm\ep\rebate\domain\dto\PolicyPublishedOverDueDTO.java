package com.qm.ep.rebate.domain.dto;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 *
 * 维护已发布政策信息
 *  
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Data
@Schema(description = "数据:维护已发布政策信息")
public class PolicyPublishedOverDueDTO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    private String id;

    @Schema(description = "数据-政策编号")
    private String vpolicycode;

    @Schema(description = "数据-政策名称")
    private String vpolicyname;

    @Schema(description = "数据-是否调整 0/1")
    private String isadjust;

    @Schema(description = "数据-调整后编号")
    private String adjustvpolicycode;

    @Schema(description = "经办人部门名称")
    private String department;

    @Schema(description = "数据-经办人 存id，逗号分割")
    private String agent;

    @Schema(description = "数据-发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date publishdate;

    @Schema(description = "数据-备注")
    private String remark;

    @Schema(description = "数据-暂不需配置")
    private String norequired;

    @Schema(description = "数据-停用")
    private String stop;

    @Schema(description = "数据-停用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date stopdate;

    @Schema(description = "数据-创建")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createon;

    @Schema(description = "数据-创建者")
    private String createby;

    @Schema(description = "数据-更新")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateon;

    @Schema(description = "数据-更新作者")
    private String updateby;

    @Schema(description = "数据-数据DTSTAMP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-部门名称  逗号分割 冗余存储")
    private String departmentname;

    @Schema(description = "数据-经办人名称 ，逗号分割 冗余存储")
    private String agentname;


    @Schema(description = "数据-部门 存id，逗号分割")
    @TableField(exist = false)
    private List<String> departments;

    @Schema(description = "数据-经办人 存id，逗号分割")
    @TableField(exist = false)
    private List<String> agents;

    @Schema(description = "数据-创建人")
    @TableField(exist = false)
    private String createbyname;

    @Schema(description = "数据-钉钉号")
    private String dingdingno;

    @Schema(description = "数据-任务实例编码")
    private String taskinstancecode;

    @Schema(description = "数据-任务流实例编码")
    private String taskflowinstancecode;

    @Schema(description = "数据-政策阶段")
    private String stage;

    @Schema(description = "数据-政策周期")
    private String policycycle;

    /**
     * 预算功能 - 新增字段
     */
    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    private String budgetType;

    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-预算释放状态（0-不可释放，1-未释放，2-已释放）")
    private String bgtReleaseStatus;

    @Schema(description = "数据-业务类型")
    @TableField(exist = false)
    private String businessType;


    @Schema(description = "政策目标")
    private String policyTarget;

    @Schema(description = "其他目标名称")
    private String otherTargetName;

    @Schema(description = "其他目标值")
    private String otherTargetValue;

    @Schema(description = "政策目标车系")
    private String policyTargetSeries;

    @Schema(description = "政策目标车系数组")
    @TableField(exist = false)
    private List<String> series;

    @Schema(description = "执行开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbegin;

    @Schema(description = "执行结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dend;

    @Schema(description = "确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date confirmDate;

    @Schema(description = "分配时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date allocationDate;

    @Schema(description = "该编码是否需要参与审计 0-返利标准，1-返利包")
    private String rebateType;

    @Schema(description = "数据-经办人名称 ，逗号分割 冗余存储")
    private String agentcode;

    @Schema(description = "完成配置的id")
    private String pid;

    @Schema(description = "经办人集合")
    private String agentls;

    @Schema(description = "政策状态")
    private String vfinishState;

    @Schema(description = "政策基本信息名称")
    private String vpolicySubName;

    @Schema(description = "政策域账号")
    private String submitCode;


}
