package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * 返利折让申请单与处理单合并表
 *
 *
 * <AUTHOR>
 * @since 2024-08-09
 */
@Getter
@Setter
@TableName("rebate_extract_apply")
public class RebateExtractApplyPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申请单号
     */
    @TableField("applyNumber")
    private String applyNumber;

    /**
     * 状态（'00-提交', '01-区域审核中', '02-大区审核中', '03-渠道审核中', '04-已审核', '10-处理中', '11-已处理', '12-已提报', '13-已确认',  '14-提交财务', '20-驳回', '21-终止', '22-撤回'）
     */
    @TableField("applyStatus")
    private String applyStatus;

    /**
     * 经销商代码
     */
    @TableField("dealerCode")
    private String dealerCode;

    /**
     * 经销商名称
     */
    @TableField("dealerName")
    private String dealerName;

    /**
     * 重点关注店(0-不是，1-是)
     */
    @TableField("canFocusStore")
    private String canFocusStore;

    /**
     * 上月STD达成率
     */
    @TableField("stdAchievementRate")
    private BigDecimal stdAchievementRate;

    /**
     * 申请折让金额（万元）
     */
    @TableField("appliedExtractAmount")
    private BigDecimal appliedExtractAmount;

    /**
     * 申请时返利账户可用金额（万元）
     */
    @TableField("availableRebateAccount")
    private BigDecimal availableRebateAccount;

    /**
     * 申请原因
     */
    @TableField("applyReason")
    private String applyReason;

    /**
     * 承诺事项
     */
    @TableField("commitment")
    private String commitment;

    /**
     * 是否同意（0-不同意，1-同意）
     */
    @TableField("canAgree")
    private String canAgree;

    /**
     * 经销商店总审批意见
     */
    @TableField("approveOpinion")
    private String approveOpinion;

    /**
     * 经销商店总审批时间
     */
    @TableField("approveTime")
    private String approveTime;

    /**
     * 联系方式
     */
    @TableField("contactInfo")
    private String contactInfo;

    /**
     * 创建者
     */
    @TableField("CREATEBY")
    private String createBy;

    /**
     * 创建日期
     */
    @TableField("CREATEON")
    private LocalDateTime createOn;

    /**
     * 更新者
     */
    @TableField("UPDATEBY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATEON")
    private LocalDateTime updateOn;

    /**
     * 时间戳
     */
    @TableField("DTSTAMP")
    private LocalDateTime dtstamp;

    /**
     * 操作人员
     */
    @TableField("operator")
    private String operator;

    /**
     * 重点关注级别
     */
    @TableField("focusLevel")
    private String focusLevel;

    /**
     * 本月std目标
     */
    @TableField("stdAim")
    private Integer stdAim;

    /**
     * 周转系数
     */
    @TableField("turnoverCoefficient")
    private BigDecimal turnoverCoefficient;

    /**
     * 预留金额
     */
    @TableField("reserveAmount")
    private BigDecimal reserveAmount;

    /**
     * 测算兑现金额（万元）
     */
    @TableField("calExtractAmount")
    private BigDecimal calExtractAmount;

    /**
     * 测算时返利账户可用金额（万元）
     */
    @TableField("calAvailableRebateAccount")
    private BigDecimal calAvailableRebateAccount;

    /**
     * 决策兑现金额（万元）
     */
    @TableField("decisionExtractAmount")
    private BigDecimal decisionExtractAmount;

    /**
     * 实际兑现金额（万元）
     */
    @TableField("actualExtractAmount")
    private BigDecimal actualExtractAmount;

    /**
     * 提交时间
     */
    @TableField("submitApplyTime")
    private String submitApplyTime;

    /**
     * 渠道部审核通过时间
     */
    @TableField("passTime")
    private String passTime;

    /**
     * 最后一次测算时间
     */
    @TableField("calTime")
    private String calTime;

    /**
     * 申请单处理完成时间
     */
    @TableField("handleTime")
    private String handleTime;

    /**
     * 提报材料时间
     */
    @TableField("submitFileTime")
    private String submitFileTime;

    /**
     * 申请单确认时间
     */
    @TableField("confirmTime")
    private String confirmTime;

    /**
     * 申请单提交财务时间
     */
    @TableField("submitFinanceTime")
    private String submitFinanceTime;

    /**
     * 财务兑付完成时间
     */
    @TableField("financeCashTime")
    private String financeCashTime;

    /**
     * 终止原因
     */
    @TableField("terminateReason")
    private String terminateReason;

    /**
     * OA审批流程id
     */
    @TableField("processInstanceId")
    private String processInstanceId;

    /**
     * batchId
     */
    @TableField("batchId")
    private String batchId;

    /**
     * 待办任务编码
     */
    @TableField("taskInstanceCode")
    private String taskInstanceCode;
    /**
     * 待办完成状态 0---------------未完成 1---------------已完成
     */
    @TableField("closeStatus")
    private String closeStatus;
    /**
     * 待办任务人员code
     */
    @TableField("taskUserCode")
    private String taskUserCode;

    /**
     * ai待办任务编码（zhinengshenhe）
     */
    @TableField("aiTaskInstanceCode")
    private String aiTaskInstanceCode;

    /**
     * 确认待办任务编码（ai审核失败）
     */
    @TableField("confirmTaskInstanceCode")
    private String confirmTaskInstanceCode;
}
