package com.qm.ep.rebate.common.config;

import cn.hutool.core.collection.CollUtil;
import com.qm.ep.rebate.domain.dto.IWorkDepartmentDTO;
import com.qm.ep.rebate.domain.dto.ReceiveHighlightRequest;
import com.qm.ep.rebate.domain.dto.StaffEfficiencyDTO;
import com.qm.ep.rebate.domain.dto.workbench.IndicatorValueRequest;
import com.qm.ep.rebate.domain.request.AbnormalAimRequest;
import com.qm.ep.rebate.domain.response.WorkbenchBoardAimDTO;
import com.qm.ep.rebate.domain.vo.IWorkResultVo;
import com.qm.ep.rebate.domain.vo.IWorkUserVO;
import com.qm.ep.rebate.remote.feign.IWorkRemote;
import com.qm.ep.rebate.remote.http.SendMessageHttpClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AimTask {

    @Autowired
    private IWorkRemote iWorkRemote;

    @Autowired
    private SendMessageHttpClient sendMessageHttpClient;


    ThreadPoolExecutor threadPool = new ThreadPoolExecutor(5, 10, 100, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));



    @Async("aimExecutor")
    public void reportAbnormalAim(AbnormalAimRequest request)  {
        sendMessageHttpClient.reportAbnormalAim(request);
    }


    @Async("aimExecutor")
    public void asyncSendLightAim(WorkbenchBoardAimDTO request) {
        sendMessageHttpClient.asynSendLightAim(request);
    }

    @Async("aimExecutor")
    public void asyncSendPerformanceToWorkbench(IndicatorValueRequest request) {
        sendMessageHttpClient.asyncSendPerformanceToWorkbench(request);
    }

    @Async("aimExecutor")
    @Transactional(isolation = Isolation.READ_UNCOMMITTED, propagation = Propagation.REQUIRES_NEW)
    @SneakyThrows
    public void asyncSendStaffEfficiencyToWorkbench(List<StaffEfficiencyDTO> pushDataList, Set<String> realExitingCodes) {
        Thread.sleep(5000);
        // 过滤掉总星级为0的数据
        // 过滤掉总星级为0或者为null的数据
        pushDataList = pushDataList.stream().filter(staffEfficiencyDTO ->
                        staffEfficiencyDTO.getOverallEvaluation() != null &&
                                BigDecimal.ZERO.compareTo(staffEfficiencyDTO.getOverallEvaluation()) != 0)
                .collect(Collectors.toList());

        pushDataList.removeIf(staffEfficiencyDTO -> !realExitingCodes.contains(staffEfficiencyDTO.getUserCode()));
        sendMessageHttpClient.asyncSendStaffEfficiencyToWorkbench(pushDataList);
    }



    @Async("commonAsync")
    public void doFindUserByOrganizationAsync(String orgName, CountDownLatch countDownLatch, Map<String,List<IWorkUserVO>> map) {
        try{
            IWorkDepartmentDTO iWorkDepartmentDTO = new IWorkDepartmentDTO();
            iWorkDepartmentDTO.setOrganizationNames(Collections.singletonList(orgName));
            IWorkResultVo<List<IWorkUserVO>> resultVo = iWorkRemote.findUserByOrganization(iWorkDepartmentDTO);
            if (resultVo==null){
                return;
            }
            if (CollUtil.isEmpty(resultVo.getData())){
                return;
            }
            if (CollUtil.isNotEmpty(resultVo.getData())){
                map.put(orgName,resultVo.getData());
            }
        }finally {
            countDownLatch.countDown();
        }

    }

    public void asyncSendHighlight(ReceiveHighlightRequest request) {
        sendMessageHttpClient.asyncSendHighlight(request);
    }
}
