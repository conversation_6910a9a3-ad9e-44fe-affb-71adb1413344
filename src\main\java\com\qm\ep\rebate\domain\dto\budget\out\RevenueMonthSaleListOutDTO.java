package com.qm.ep.rebate.domain.dto.budget.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "数据:销量明细")
@Data
public class RevenueMonthSaleListOutDTO {

    @Schema(description = "数据-id")
    private Integer id;

    @Schema(description = "数据-主键")
    private String uniqueKey;

    @Schema(description = "数据-年度")
    private String saleYear;

    @Schema(description = "数据-月度")
    private String saleMonth;

    @Schema(description = "数据-平均指导价")
    private BigDecimal averagePrice;

    @Schema(description = "数据-销量（aak/std）")
    private Long saleCount;

    @Schema(description = "数据-收入（平均指导价*销量）")
    private BigDecimal saleAmount;

    @Schema(description = "数据-销售类型（00-aak，01-std）")
    private String saleType;

    @Schema(description = "数据-排序")
    private int sort;

}
