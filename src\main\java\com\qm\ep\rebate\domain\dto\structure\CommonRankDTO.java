package com.qm.ep.rebate.domain.dto.structure;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-普通等级 DTO")
@Data
public class CommonRankDTO {
    @Schema(description = "数据-策略 ID")
    private String policyId;
    @Schema(description = "数据-对象 ID")
    private String objectId;
    @Schema(description = "数据-别名")
    private String alias;
    @Schema(description = "数据-订单字段")
    private String orderField;
    @Schema(description = "数据-组字段")
    private String groupField;
    @Schema(description = "数据-排序")
    private String sort;
    @Schema(description = "数据-逻辑")
    private String logic;
}