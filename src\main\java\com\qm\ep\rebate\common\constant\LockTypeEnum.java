package com.qm.ep.rebate.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分布式锁枚举
 *
 * <AUTHOR>
 * @date 2022-09-26 08:46
 */
@AllArgsConstructor
@Getter
public enum LockTypeEnum {

    /**
     * 预算占用Lock
     */
    BGT_OCCUPY_LOCK("bgt:occupy:lock:", 30),
    /**
     * 预算重新分配Lock
     */
    BGT_REALLOCATE_LOCK("bgt:reallocate:lock:", 30),
    BGT_ADJUST_CREATE_LOCK("bgt:adjustCreate:lock:", 30),
    REBATE_BOARD_SCHEDULE("rebate:board:schedule:", 30),
    REBATE_BOARD_COMMON_SCHEDULE("rebate:board:common:schedule:", 30),
    REBATE_BPMCallBack("rebate:BPMCallBack:lock:new:", 30);

    private final String prefix;
    private final long expireTime;

    public String getKey(String key) {
        return this.prefix + key;
    }
}
