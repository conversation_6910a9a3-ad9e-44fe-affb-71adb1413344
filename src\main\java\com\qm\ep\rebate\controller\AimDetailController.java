package com.qm.ep.rebate.controller;

import com.qm.tds.api.controller.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/aim")
@Tag(name = "业务目标", description = "[author: 10200571]")
public class AimDetailController extends BaseController {
    //
    // @Autowired
    // private AimDetailService aimDetailService;
    // /**
    //  * 查询aim主数据
    //  * [aim、aim_detail]
    //  * 2023-01-17 JJQ
    //  */
    // @Operation(summary = "查询aim主数据 [aim、aim_detail] 2023-01-17 JJQ", description = "[author: 10200571]")
    // @PostMapping("/getAimDetailsPage")
    // public JsonResultVo<QmPage<AimDetailVO>> getAimDetailsPage(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<QmPage<AimDetailVO>> result = new JsonResultVo<>();
    //     result.setData(this.aimDetailService.getAimDetailsPage(aimDTO));
    //     return result;
    // }
    //
    // /**
    //  * 查询aim分解历史
    //  * [aim_decompose_history]
    //  * 2023-01-17 JJQ
    //  */
    // @Operation(summary = "查询aim分解历史 [aim_decompose_history] 2023-01-17 JJQ", description = "[author: 10200571]")
    // @PostMapping("/getAimHistoryPage")
    // public JsonResultVo<QmPage<AimDecomposeHistoryVO>> getAimHistoryPage(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<QmPage<AimDecomposeHistoryVO>> result = new JsonResultVo<>();
    //     result.setData(this.aimDetailService.getAimHistoryPage(aimDTO));
    //     return result;
    // }
    //
    // /**
    //  * 查询aim分解历史明细（锁定）
    //  * [aim_decompose_result、aim_decompose_history.lock=1]
    //  * 2023-01-17 JJQ
    //  */
    // @Operation(summary = "查询aim分解历史明细（锁定） [aim_decompose_result、aim_decompose_history.lock=1] 2023-01-17 JJQ", description = "[author: 10200571]")
    // @PostMapping("/getAimResultLockPage")
    // public JsonResultVo<QmPage<AimDecomposeResultVO>> getAimResultLockPage(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<QmPage<AimDecomposeResultVO>> result = new JsonResultVo<>();
    //     result.setData(this.aimDetailService.getAimResultLockPage(aimDTO));
    //     return result;
    // }
    //
    // /**
    //  * 查询aim分解历史明细
    //  * [aim_decompose_result]
    //  * 2023-01-17 JJQ
    //  */
    // @Operation(summary = "查询aim分解历史明细 [aim_decompose_result] 2023-01-17 JJQ", description = "[author: 10200571]")
    // @PostMapping("/getAimResultPage")
    // public JsonResultVo<QmPage<AimDecomposeResultVO>> getAimResultPage(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<QmPage<AimDecomposeResultVO>> result = new JsonResultVo<>();
    //     result.setData(this.aimDetailService.getAimResultPage(aimDTO));
    //     return result;
    // }
    //
    // /**
    //  * 上传excel
    //  * 2023-01-30 JJQ
    //  */
    // @Operation(summary = "上传excel，获取里面的数据", description = "[author: 10200571]")
    // @PostMapping("/excel/commit")
    // public JsonResultVo<List<AimDecomposeResultVO>> commit(@RequestParam MultipartFile file) {
    //     JsonResultVo<List<AimDecomposeResultVO>> resultVo = new JsonResultVo<>();
    //     List<AimDecomposeResultVO> list = this.aimDetailService.commit(file);
    //     resultVo.setData(list);
    //     return resultVo;
    // }
    //
    // /**
    //  * 上传excel数据验证
    //  * 2023-01-30 JJQ
    //  */
    // @Operation(summary = "上传excel数据验证", description = "[author: 10200571]")
    // @PostMapping("/excel/commitAfter")
    // public JsonResultVo<List<AimDecomposeResultVO>> commitAfter(@RequestBody AimExcelDTO aimExcelDTO) {
    //     JsonResultVo<List<AimDecomposeResultVO>> resultVo = new JsonResultVo<>();
    //     List<AimDecomposeResultVO> list = this.aimDetailService.commitAfter(aimExcelDTO);
    //     if (list.isEmpty()) {
    //         resultVo.setMsg("excel导入成功!");
    //     } else {
    //         resultVo.setMsgErr("excel导入失败!");
    //     }
    //     resultVo.setData(list);
    //     return resultVo;
    // }
    //
    // /**
    //  * 删除分解历史
    //  * 2023-01-30 JJQ
    //  */
    // @Operation(summary = "删除分解历史 2023-01-30 JJQ", description = "[author: 10200571]")
    // @DeleteMapping("/removeHistory/{id}")
    // public JsonResultVo<Boolean> removeHistory(@PathVariable String id) {
    //     JsonResultVo<Boolean> result = new JsonResultVo<>();
    //     this.aimDetailService.removeHistory(id);
    //     result.setData(true);
    //     return result;
    // }
    //
    // /**
    //  * 锁定分解历史
    //  * 2023-01-30 JJQ
    //  */
    // @Operation(summary = "锁定分解历史 2023-01-30 JJQ", description = "[author: 10200571]")
    // @DeleteMapping("/lockHistory/{id}")
    // public JsonResultVo<Boolean> lockHistory(@PathVariable String id) {
    //     JsonResultVo<Boolean> result = new JsonResultVo<>();
    //     this.aimDetailService.lockHistory(id);
    //     result.setData(true);
    //     return result;
    // }
    //
    // /**
    //  * 通过aim detail id 获取锁定的版本号
    //  */
    // @Operation(summary = "通过aim detail id 获取锁定的版本号", description = "[author: 10200571]")
    // @PostMapping("/detail/getLockedHistory")
    // public JsonResultVo<AimDecomposeHistoryVO> getLockedHistory(@RequestBody AimDTO aimDTO) {
    //     JsonResultVo<AimDecomposeHistoryVO> result = new JsonResultVo<>();
    //     AimDecomposeHistoryVO historyLockVersion = aimDetailService.getLockedHistory(aimDTO.getDetailId());
    //     result.setData(historyLockVersion);
    //     return result;
    // }
}
