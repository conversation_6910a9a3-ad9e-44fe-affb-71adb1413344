package com.qm.ep.rebate.domain.dto.budget.out;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "数据:返利明细")
@Data
public class BgtBreakdownDetailOutDTO {

    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-返利项目代码")
    private String classItem;

    @Schema(description = "数据-返利项目名称")
    private String classItemName;

    @Schema(description = "数据-兑付依据（00-aak，01-std）")
    private String saleType;

    @Schema(description = "数据-时间类型")
    private String timeType;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    private String bgtType;

    @Schema(description = "数据-车系各项金额明细")
    private List<SeriesAmountOutDTO> seriesList;

}
