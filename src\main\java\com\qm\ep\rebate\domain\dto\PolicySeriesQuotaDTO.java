package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Schema(description = "数据:实体类-策略系列配额 DTO")
@Data
public class PolicySeriesQuotaDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-车系")
    private String series;
    @Schema(description = "数据-额度")
    private String quota;
    @Schema(description = "数据-是否停用")
    private String ifStop;

    @Schema(description = "数据-创建")
    private Date createon;
    @Schema(description = "数据-创建者")
    private String createby;
    @Schema(description = "数据-更新")
    private Date updateon;
    @Schema(description = "数据-更新作者")
    private String updateby;
    @Schema(description = "数据-DTSTAMP")
    private Timestamp dtstamp;
}