package com.qm.ep.rebate.domain.dto.oa;
import lombok.*;

/**
 * 代表业务流程实例的数据传输对象。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProcessInstanceDTO {

    /**
     * 系统代码。
     */
    private String sysCode;

    /**
     * 流程定义ID。
     */
    private String processDefinitionId;

    /**
     * 表单类型。
     */
    private String formType;

    /**
     * 流程代码。
     */
    private String procCode;

    /**
     * 源类别。
     */
    private String sourceCategory;

    /**
     * PC端URL。
     */
    private String pcUrl;

    /**
     * 任务表单URL。
     */
    private String taskformurl;

    /**
     * 应用程序URL。
     */
    private String appUrl;

    /**
     * 流程实例ID。
     */
    private String procInstId;

    /**
     * 标题。
     */
    private String title;

    /**
     * 用户ID。
     */
    private String userId;

    /**
     * 取消发送消息。
     */
    private String cancelSendingMessage;

    /**
     * 模板名称。
     */
    private String templateName;

    /**
     * 文件夹编号。
     */
    private String folio;

    /**
     * 类别。
     */
    private String category;

    /**
     * H5表单类型。
     */
    private String h5FormType;

    /**
     * 应用来源。
     */
    private String appSource;

    /**
     * 点击类型。
     */
    private String clickType;

    /**
     * 任务ID。
     */
    private String taskId;
}

