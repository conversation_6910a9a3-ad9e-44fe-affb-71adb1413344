package com.qm.ep.rebate.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:TriggerDTO对象")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TriggerDTO extends JsonParamDto {
    @Schema(description = "数据-州")
    private String state;
}

