package com.qm.ep.rebate.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.PolicyDO;
import com.qm.ep.rebate.domain.bean.PolicyDealerDO;
import com.qm.ep.rebate.domain.dto.PolicyDealerDTO;
import com.qm.ep.rebate.mapper.PolicyMapper;
import com.qm.ep.rebate.service.PolicyDealerService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 *
 * Controller
 * 政策关联经销商表JsonResultVo
 *  
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
@Tag(name = "政策关联经销商表")
@RestController
@RequestMapping("/policyDealer")
public class PolicyDealerController extends BaseController {

    @Autowired
    private PolicyDealerService policyDealerService;

    @Resource
    private PolicyMapper policyMapper;

    /**
     * 使用系统默认的保存/修改 方法
     */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<List<PolicyDealerDO>> save(@RequestBody List<PolicyDealerDO> request) {
        JsonResultVo<List<PolicyDealerDO>> resultObj = new JsonResultVo<>();
        if (CollUtil.isNotEmpty(request)) {
            PolicyDealerDO policyDealerDO = request.get(0);
            PolicyDO policyDO = policyMapper.selectById(policyDealerDO.getPolicyId());
            if (policyDO == null) {
                throw new QmException("政策不存在，id：" + policyDealerDO.getPolicyId());
            }
        }
        boolean flag = policyDealerService.saveOrUpdateBatch(request);
        if (flag) {
            resultObj.setData(request);
            resultObj.setMsg("保存成功！");
        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }

    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<PolicyDealerDO> deleteById(@RequestBody PolicyDealerDO tempDO) {
        JsonResultVo<PolicyDealerDO> resultObj = new JsonResultVo<>();
        boolean flag = policyDealerService.removeById(tempDO.getId());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }

    @Operation(summary = "根据传入的政策id批量删除", description = "[author: 10200571]")
    @PostMapping("/deleteBatchByPolicyId")
    public JsonResultVo<String> deleteBatchByPolicyId(@RequestBody PolicyDealerDO tempDO) {
        return policyDealerService.deleteBatchByPolicyId(tempDO.getPolicyId());
    }

    /**
     * 红旗伙伴列表查询
     */
    @Operation(summary = "红旗伙伴列表查询", description = "[author: 10200571]")
    @PostMapping("/getSubList2")
    public JsonResultVo<QmPage<PolicyDealerDO>> getSubList2(@RequestBody PolicyDealerDTO tempDTO) {
        JsonResultVo<QmPage<PolicyDealerDO>> ret = new JsonResultVo<>();
        // 定义查询构造器
        QmQueryWrapper<PolicyDealerDO> queryWrapper = new QmQueryWrapper<>();
        if (tempDTO.getTwhere() != null) {
            tempDTO.setTwhere(tempDTO.getTwhere().replace("dealerCode", "DEALER_CODE"));
            tempDTO.setTwhere(tempDTO.getTwhere().replace("dealerName", "DEALER_NAME"));
        }
        if (tempDTO.getTsortby() != null) {
            tempDTO.setTsortby(tempDTO.getTsortby().replace("dealerCode", "DEALER_CODE"));
            tempDTO.setTsortby(tempDTO.getTsortby().replace("dealerName", "DEALER_NAME"));
        }
        // 拼装实体属性查询条件
        LambdaQueryWrapper<PolicyDealerDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getNpolicyid()), PolicyDealerDO::getPolicyId, tempDTO.getNpolicyid());
        if (!BootAppUtil.isNullOrEmpty(tempDTO.getNpolicyid())) {
            lambdaWrapper.eq(PolicyDealerDO::getPolicyId, tempDTO.getNpolicyid());
        } else {
            ret.setMsgErr("政策主表ID不能为空！");
            return ret;
        }

        // 查询数据，使用table函数。
        QmPage<PolicyDealerDO> list = policyDealerService.getSubList2(queryWrapper, tempDTO);

        ret.setData(list);
        return ret;
    }

    @Operation(summary = "导入", description = "[author: 10200571]")
    @PostMapping("/commit")
    public JsonResultVo<List<PolicyDealerDO>> commit(@RequestParam MultipartFile file) {
        return policyDealerService.commit(file);
    }

    @Operation(summary = "导入后数据处理", description = "[author: 10200571]")
    @PostMapping("/commitAfter")
    public JsonResultVo<List<PolicyDealerDO>> commitAfter(@RequestBody PolicyDealerDTO tempDTO) {
        LoginKeyDO loginKey = getUserInfo();
        return policyDealerService.commitAfter(tempDTO, loginKey);
    }

    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/downTemplate")
    public void downTemplate(HttpServletResponse response) {
        policyDealerService.downTemplate(response);
    }

}
