package com.qm.ep.rebate.domain.dto.oa;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:实体类-数据BPMcall back 接口参数")
@Data
public class BPMCallBackInterfaceParam {
    /**
     * 回调执行参数
     */
    @Schema(description = "数据-回调执行参数")
    private Execution execution;
    @Schema(description = "数据-任务")
    private Task task;
    @Schema(description = "数据-发送类型")
    private String sendType;
    @Schema(description = "数据-登录用户主键")
    private String loginUserId;
    @Schema(description = "数据-语言")
    private String language;
    @Schema(description = "数据-用户主键")
    private String userId;
    @Schema(description = "数据-类型")
    private String type;

    private String appSource;
}
