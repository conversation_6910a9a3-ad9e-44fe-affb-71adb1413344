package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 预算分解主表
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_breakdown_main")
@Schema(description = "数据:预算分解主表")
public class BgtBreakdownMainDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-给工作台的bizId")
    @TableField("biz_id")
    private String bizId;

    @Schema(description = "数据-预算表常规数据下发id")
    @TableField("bgt_eom_re_id")
    private String bgtEomReId;

    @Schema(description = "数据-预算表专项数据下发id")
    @TableField("bgt_eom_sp_id")
    private String bgtEomSpId;

    @Schema(description = "数据-滚动销售额标识")
    @TableField("bgt_series_sale_uniqueKey")
    private String bgtSeriesSaleUniqueKey;

    @Schema(description = "数据-预算时间维度（0-年度，1-季度）")
    @TableField("time_type")
    private String timeType;

    @Schema(description = "数据-年")
    @TableField("breakdown_year")
    private String breakdownYear;

    @Schema(description = "数据-季度")
    @TableField("breakdown_quarter")
    private String breakdownQuarter;

    @Schema(description = "数据-使用状态")
    @TableField("use_status")
    private String useStatus;

    @Schema(description = "数据-触发类型（01-EOM下发新版本，02-运营自调）")
    @TableField("trigger_type")
    private String triggerType;

    @Schema(description = "数据-预算类型（0-常规，1-专项）")
    @TableField("bgt_type")
    private String bgtType;

    @Schema(description = "数据-版本号")
    @TableField("version")
    private String version;

    @Schema(description = "数据-维护信息待办任务实例编码")
    @TableField("taskInstanceCode")
    private String taskInstanceCode;

    @Schema(description = "数据-任务流实例编码")
    @TableField("taskFlowinstanceCode")
    private String taskFlowinstanceCode;

    @Schema(description = "数据-预算分解任务实例编码")
    @TableField("taskDecInstanceCode")
    private String taskDecInstanceCode;

    @Schema(description = "数据-发布状态（0-草稿，1-发布）")
    @TableField("published_status")
    private String publishedStatus;

    @Schema(description = "数据-创建人姓名")
    @TableField("submit_name")
    private String submitName;

    @Schema(description = "数据-创建人域账号")
    @TableField("submit_code")
    private String submitCode;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Schema(description = "数据-该分解预算总点数（常规）")
    @TableField("breakdown_total_point")
    private BigDecimal breakdownTotalPoint;

    @Schema(description = "数据-该分解使用的预算点数")
    @TableField("breakdown_used_point")
    private BigDecimal breakdownUsedPoint;

    @Schema(description = "数据-该分解预算总点数余额")
    @TableField("breakdown_point_balance")
    private BigDecimal breakdownPointBalance;

    @Schema(description = "数据-该分解预算总金额（专项）")
    @TableField("breakdown_total_amount")
    private BigDecimal breakdownTotalAmount;

    @Schema(description = "数据-该分解使用的预算金额")
    @TableField("breakdown_used_amount")
    private BigDecimal breakdownUsedAmount;

    @Schema(description = "数据-该分解预算金额余额")
    @TableField("breakdown_amount_balance")
    private BigDecimal breakdownAmountBalance;

    @Schema(description = "数据-申请单主表id（关联触发的分解）")
    @TableField("apply_main_id")
    private Integer applyMainId;

    @Schema(description = "数据-该分解预算金额余额")
    @TableField("breakdown_re_amount")
    private BigDecimal breakdownReAmount;

    @Schema(description = "数据-该分解预算金额余额")
    @TableField("breakdown_sp_amount")
    private BigDecimal breakdownSpAmount;
}
