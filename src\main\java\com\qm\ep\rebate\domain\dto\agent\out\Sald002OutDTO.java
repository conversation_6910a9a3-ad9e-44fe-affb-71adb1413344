package com.qm.ep.rebate.domain.dto.agent.out;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "数据:报账单对象")
public class Sald002OutDTO {

    @Schema(description = "数据-主键")
    private String id ;

    @Schema(description = "数据-代理商代码")
    private String vdealerCode;

    @Schema(description = "数据-代理商名称")
    private String vdealerName;

    @Schema(description = "数据-报账单号")
    private String reimburseNo;

    @Schema(description = "数据-报销单名称")
    private String reimburseName;

    @Schema(description = "数据-报账状态,0录入、1提交、2驳回、3完成、4取消")
    private String reimburseStatus;

    @Schema(description = "数据-报账金额")
    private BigDecimal reimburseAmount;

    @Schema(description = "数据-不含税金额")
    private BigDecimal excTaxAmount;

    @Schema(description = "数据-税额")
    private BigDecimal taxAmount;

    @Schema(description = "数据-税率")
    private String taxRate;

    @Schema(description = "数据-开票人")
    private String invoicBy;

    @Schema(description = "数据-报账时间")
    private Date reimburseTime;

    @Schema(description = "数据-提交时间")
    private Date submitTime;

    @Schema(description = "数据-审核时间")
    private Date auditTime;

    @Schema(description = "数据-时间戳")
    private Timestamp dtstamp;

    @Schema(description = "数据-审核状态")
    private String auditStatus;

    @Schema(description = "数据-审核意见")
    private String auditContent;

    @Schema(description = "数据-创建者")
    @JsonProperty("CREATEBY")
    private String CREATEBY;

    @Schema(description = "数据-创建日期")
    @JsonProperty("CREATEON")
    private String CREATEON;

    @Schema(description = "数据-更新者")
    @JsonProperty("UPDATEBY")
    private String UPDATEBY;

    @Schema(description = "数据-更新时间")
    @JsonProperty("UPDATEON")
    private String UPDATEON;

    @Schema(description = "数据-结算单集合")
    List<Sald001ListOutDTO> sdOutList;

    @Schema(description = "数据-发票明细集合")
    List<Sald002diListOutDTO> invoiceList;

    @Schema(description = "数据-附件集合")
    List<SaldFileListOutDTO> fileList;

}
