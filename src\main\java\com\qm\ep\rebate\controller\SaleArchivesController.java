package com.qm.ep.rebate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebate.domain.bean.SaleArchivesDO;
import com.qm.ep.rebate.domain.dto.SaleArchivesDTO;
import com.qm.ep.rebate.domain.vo.SaleArchivesVO;
import com.qm.ep.rebate.service.SaleArchivesService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.TableUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 经销商维护车辆档案
 * <AUTHOR>
 */
@Tag(name = "经销商维护车辆档案")
@RestController
@RequestMapping("/saleArchives")
public class SaleArchivesController extends BaseController {
    @Autowired
    private SaleArchivesService saleArchivesService;
    @Operation(summary = "查询浏览页经销商维护车辆档案数据", description = "[author: 10200571]")
    @PostMapping("/getDealerVehicleFilesList")
    public JsonResultVo<QmPage<SaleArchivesVO>> getDealerVehicleFilesList(@RequestBody SaleArchivesDTO tempDTO){
        JsonResultVo<QmPage<SaleArchivesVO>> ret=new JsonResultVo<>();
        QmQueryWrapper<SaleArchivesVO> queryWrapper=new  QmQueryWrapper<>();
        TableUtils.appendTableAdditional(queryWrapper,tempDTO, SaleArchivesVO.class);
        IPage<SaleArchivesVO> queryPage = TableUtils.convertToIPage(tempDTO);
        IPage<SaleArchivesVO> saleArchivesList=saleArchivesService.getSaleArchives(queryPage,tempDTO,queryWrapper);
        QmPage<SaleArchivesVO> qmPage = TableUtils.convertQmPageFromMpPage(saleArchivesList);
        ret.setData(qmPage);
        return ret;
    }

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/saveDealerVehicle")
    public JsonResultVo<List<SaleArchivesDO>> saveDealerVehicle(@RequestBody SaleArchivesDO saleArchivesDO){
        JsonResultVo<List<SaleArchivesDO>> resultObj=new JsonResultVo<>();
        boolean flag =saleArchivesService.saveOrUpdate(saleArchivesDO);
        if (flag) {
            resultObj.setMsg("保存成功！");
        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }
    @Operation(summary = "回显数据", description = "[author: 10200571]")
    @PostMapping("/getDealerVehicleFilesDate")
    public JsonResultVo<SaleArchivesDO> getDealerVehicleFilesDate(@RequestBody SaleArchivesDO saleArchivesDO) {
        JsonResultVo<SaleArchivesDO> resultObj = new JsonResultVo<>();
        if (BootAppUtil.isNullOrEmpty(saleArchivesDO.getId())) {
            resultObj.setMsgErr("id为空");
            return resultObj;
        }
        SaleArchivesDO saleArchives=saleArchivesService.getDealerVehicleFilesDate(saleArchivesDO.getId());
        resultObj.setData(saleArchives);
        return resultObj;
    }
    @Operation(summary = "删除", description = "[author: 10200571]")
    @PostMapping("/deleteDealerVehicleFiles")
    public JsonResultVo<SaleArchivesDO> deleteDealerVehicleFiles(@RequestBody SaleArchivesDO saleArchivesDO) {
        JsonResultVo<SaleArchivesDO> resultObj = new JsonResultVo<>();
        boolean flag = saleArchivesService.removeById(saleArchivesDO.getId());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }
    @Operation(summary = "导入", description = "[author: 10200571]")
    @PostMapping("/commit")
    public JsonResultVo<List<SaleArchivesDO>> commit(@RequestParam MultipartFile file) {
        return saleArchivesService.commit(file);
    }
    @Operation(summary = "导入后数据处理", description = "[author: 10200571]")
    @PostMapping("/commitAfter")
    public JsonResultVo<List<SaleArchivesDO>> commitAfter(@RequestBody SaleArchivesDTO tempDTO) {
        return saleArchivesService.commitAfter(tempDTO);
    }
    @Operation(summary = "批量删除", description = "[author: 10200571]")
    @PostMapping("/batchDeletion")
    public JsonResultVo<List<SaleArchivesDO>> batchDeletion(@RequestBody List<SaleArchivesDO> tempDOs) {
        JsonResultVo<List<SaleArchivesDO>> resultObj = new JsonResultVo<>();
        List<String> ids=new ArrayList<>();
        for(SaleArchivesDO saleArchivesDO:tempDOs){
            ids.add(saleArchivesDO.getId());
        }
        boolean flag =saleArchivesService.removeByIds(ids);
        if (flag) {
            resultObj.setData(tempDOs);
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
        return resultObj;
    }


}
