package com.qm.ep.rebate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebate.domain.bean.AuditApproveDO;
import com.qm.ep.rebate.domain.dto.AuditApproveDTO;
import com.qm.ep.rebate.service.AuditApproveService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
*
*Controller
* JsonResultVo
*
* <AUTHOR>
* @since 2023-09-19
*/
@Tag(description = "审核业务数据表",name ="审核业务数据表")
@RestController
@RequestMapping("/auditApprove")

public class AuditApproveController extends BaseController {


    @Resource
    private AuditApproveService  auditApproveService;

    /**
    *使用系统默认的保存/修改 方法
    */
    @Operation(summary = "使用系统默认的保存/修改 方法", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<AuditApproveDO> save(@RequestBody AuditApproveDO tempDO){

        JsonResultVo<AuditApproveDO> resultObj = new JsonResultVo<>();
        boolean flag = auditApproveService.saveOrUpdate(tempDO);
        if (flag) {
            resultObj.setData(tempDO);

        } else {
            resultObj.setMsgErr("保存失败！");
        }
        return resultObj;
    }
    /**
     * 根据传入的id删除数据
     */
    @Operation(summary = "根据传入的id删除数据", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<AuditApproveDO> deleteById(@RequestBody AuditApproveDO tempDO){
        JsonResultVo<AuditApproveDO> resultObj = new JsonResultVo<>();
        boolean flag = auditApproveService.removeById(tempDO.getId());
        if (flag) {
            resultObj.setMsg("删除成功！");
        } else {
            resultObj.setMsgErr("删除失败！");
        }
            return resultObj;
    }
    /**
     * 根据传入的实体信息进行查询
     */
    @Operation(summary = "根据传入的实体信息进行查询", description = "[author: 10200571]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<AuditApproveDO>> table(@RequestBody AuditApproveDTO tempDTO){
        //定义查询构造器
        QmQueryWrapper<AuditApproveDO> queryWrapper = new QmQueryWrapper<>();
        //拼装实体属性查询条件
        LambdaQueryWrapper<AuditApproveDO> lambdaWrapper = queryWrapper.lambda();

            //主键
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getId()), AuditApproveDO::getId, tempDTO.getId());

            //业务主键
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getBizId()), AuditApproveDO::getBizId, tempDTO.getBizId());

            //任务号
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getTaskFlowInstanceCode()), AuditApproveDO::getTaskFlowInstanceCode, tempDTO.getTaskFlowInstanceCode());

            //审批的实体
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getClassName()), AuditApproveDO::getClassName, tempDTO.getClassName());

            //完成状态（0：初始化，1：进行中，2已完成）
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVFinishStatus()), AuditApproveDO::getVFinishStatus, tempDTO.getVFinishStatus());

            //完成时间
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getVFinishTime()), AuditApproveDO::getVFinishTime, tempDTO.getVFinishTime());

            //提交人登录账户
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getSubmitCode()), AuditApproveDO::getSubmitCode, tempDTO.getSubmitCode());

            //提交人主键
        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getSubmitId()), AuditApproveDO::getSubmitId, tempDTO.getSubmitId());

        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getCreateBy()), AuditApproveDO::getCreateBy, tempDTO.getCreateBy());

        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getCreateOn()), AuditApproveDO::getCreateOn, tempDTO.getCreateOn());

        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getUpdateBy()), AuditApproveDO::getUpdateBy, tempDTO.getUpdateBy());

        lambdaWrapper.eq(!BootAppUtil.isNullOrEmpty(tempDTO.getUpdateOn()), AuditApproveDO::getUpdateOn, tempDTO.getUpdateOn());

        //查询数据，使用table函数。
        QmPage<AuditApproveDO> list = auditApproveService.table(queryWrapper,tempDTO);
        JsonResultVo<QmPage<AuditApproveDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }
}
