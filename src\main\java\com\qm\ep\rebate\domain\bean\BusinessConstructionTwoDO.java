package com.qm.ep.rebate.domain.bean;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("BUSINESSCONSTRUCTION2")
@Schema(description = "数据:数据对象")
public class BusinessConstructionTwoDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-政策主键")
    @TableField("POLICYID")
    private String policyId;

    @Excel(name = "表类型", orderNum = "1")
    @Schema(description = "数据-表类型")
    @TableField("OBJECTTYPE")
    private CalcObjectTypeEnum objectType;

    @Excel(name = "业务底表名", orderNum = "2")
    @Schema(description = "数据-业务底表名")
    @TableField("TABLENAME")
    private String tableName;

    @Excel(name = "业务底表字段名", orderNum = "3")
    @Schema(description = "数据-业务底表字段名")
    @TableField("FIELDNAME")
    private String fieldName;

    @Excel(name = "业务底表关联字段", orderNum = "4")
    @Schema(description = "数据-业务底表关联字段")
    @TableField("RELEVANCEFIELDNAME")
    private String relevanceFieldName;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

}
