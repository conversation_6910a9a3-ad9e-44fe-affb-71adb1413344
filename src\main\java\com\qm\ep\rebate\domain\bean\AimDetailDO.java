package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("aim_detail")
@Schema(description = "数据:业务目标分解详情表")
public class AimDetailDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-主表ID")
    @TableField("aimId")
    private String aimId;

    @Schema(description = "数据-业务指标")
    @TableField("code")
    private String code;

    @Schema(description = "数据-分解分类")
    @TableField("category")
    private String category;

    @Schema(description = "数据-目标名称")
    @TableField("name")
    private String name;

    @Schema(description = "数据-目标值")
    @TableField("value")
    private String value;

    @Schema(description = "数据-目标值单位")
    @TableField("unit")
    private String unit;
    @Schema(description = "数据-定义")
    @TableField("description")
    private String description;
    @Schema(description = "数据-考核周期")
    @TableField("periodId")
    private String periodId;
    @Schema(description = "数据-考核周期")
    @TableField(exist=false)
    private ManagePeriodDO managePeriod;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
