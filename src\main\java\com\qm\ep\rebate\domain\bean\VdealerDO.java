package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 *
 * 经销商基础信息（服务冗余存储）
 *
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mdac100")
@Schema(description = "数据:经销商基础信息（商务政策冗余存储）")
public class VdealerDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-ID,s_codeid")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-经销商代码")
    @TableField("VDEALER")
    private String vdealer;

    @Schema(description = "数据-经销商名称")
    @TableField("VDEALERNAME")
    private String vdealername;

    @Schema(description = "数据-经销商简称")
    @TableField("VDEALERSHORTNAME")
    private String vdealershortname;

    @Schema(description = "数据-曾用名")
    @TableField("VDEALEROLDNAME")
    private String vdealeroldname;

    @Schema(description = "数据-经销商类型")
    @TableField("VDEALERTYPE")
    private String vdealertype;

    @Schema(description = "数据-建档日期")
    @TableField("DCREATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dcreate;

    @Schema(description = "数据-组织机构代码证号")
    @TableField("VINSTMARK")
    private String vinstmark;

    @Schema(description = "数据-投资母体/公司实际出资人")
    @TableField("VINVESTOR")
    private String vinvestor;

    @Schema(description = "数据-注册资金")
    @TableField("NREGFIN")
    private BigDecimal nregfin;

    @Schema(description = "数据-法人代表")
    @TableField("VCORPORATION")
    private String vcorporation;

    @Schema(description = "数据-总经理")
    @TableField("VMANAGER")
    private String vmanager;

    @Schema(description = "数据-总经理移动电话")
    @TableField("VMOBILE")
    private String vmobile;

    @Schema(description = "数据-所有制代码")
    @TableField("VOWNERSHIP")
    private String vownership;

    @Schema(description = "数据-经销商级别")
    @TableField("VDEALERLEVEL")
    private String vdealerlevel;

    @Schema(description = "数据-公司ID")
    @TableField(value = "NCOMPANYID")
    private String ncompanyid;

    @Schema(description = "数据-停用标识")
    @TableField("VSTOP")
    private String vstop;

    @Schema(description = "数据-停用日期")
    @TableField("DSTOP")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dstop;

    @Schema(description = "数据-停用原因")
    @TableField("VSTOPREASON")
    private String vstopreason;

    @Schema(description = "数据-备注")
    @TableField("VMEMO")
    private String vmemo;

    @Schema(description = "数据-整车销售组织")
    @TableField("NORGAN")
    private String norgan;

    @Schema(description = "数据-服务组织")
    @TableField("NSVCORG")
    private String nsvcorg;

    @Schema(description = "数据-备件组织")
    @TableField("NSPAORG")
    private String nspaorg;

    @Schema(description = "数据-地址")
    @TableField("VADDR")
    private String vaddr;

    @Schema(description = "数据-联系电话")
    @TableField("VTEL")
    private String vtel;

    @Schema(description = "数据-传真")
    @TableField("VFAX")
    private String vfax;

    @Schema(description = "数据-EMAIL")
    @TableField("VEMAIL")
    private String vemail;

    @Schema(description = "数据-经销商属性，是实际经销商还是预留经销商")
    @TableField("VATTR")
    private String vattr;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(description = "数据-国家")
    @TableField("NCOUNTRYID")
    private String ncountryid;

    @Schema(description = "数据-省区")
    @TableField("NPROVINCE")
    private String nprovince;

    @Schema(description = "数据-市")
    @TableField("NCITY")
    private String ncity;

    @Schema(description = "数据-县")
    @TableField("NCOUNTYID")
    private String ncountyid;

    @Schema(description = "数据-02是厂内 01是厂外  数据字典项LOCALORFOREIGN")
    @TableField("VLOCA")
    private String vloca;

    @Schema(description = "数据-成立日期")
    @TableField("DESTABLISH")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date destablish;

    @Schema(description = "数据-联系人")
    @TableField("VLINKMAN")
    private String vlinkman;

    @Schema(description = "数据-启用标识 0 不启用，1 启用")
    @TableField("VSTARTFLAG")
    private String vstartflag;

    @Schema(description = "数据-启用日期")
    @TableField("DSTARTDATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dstartdate;

    @Schema(description = "数据-对经销商档案中经销商特殊标识为真的经销商，省区可以修改")
    @TableField("VMODIFYPROVINCE")
    private String vmodifyprovince;

    @Schema(description = "数据-经营范围(手输)-红旗增加")
    @TableField("VCOPEOFBUSINESS")
    private String vcopeofbusiness;

    @Schema(description = "数据-建店类别(数据字典BUILDCATEGORY:01新建、02改建)-红旗增加")
    @TableField("VBUILDCATEGORY")
    private String vbuildcategory;

    @Schema(description = "数据-建店标准(数据字典BUILDSTANDARD:01/标准店、02/非标准店)-红旗增加")
    @TableField("VBUILDSTANDARD")
    private String vbuildstandard;

    @Schema(description = "数据-维修资质(数据字典：REPAIRQUALIFICATION)-红旗增加")
    @TableField("VREPAIRQUALIFICATION")
    private String vrepairqualification;

    @Schema(description = "数据-经营类型(数据字典BUSINESSTYPEOFDLR：01/4S、02/销售服务店、03/城市展厅店、04/服务店、05/过渡销售店、06/过渡服务店)-红旗增加")
    @TableField("VBUSINESSTYPE")
    private String vbusinesstype;

    @Schema(description = "数据-开通店端 0 不启用，1 启用")
    @TableField("VNEWDMS")
    private String vnewdms;

    @Schema(description = "数据-开业时间")
    @TableField("DOPEN")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dopen;

    @Schema(description = "数据-经销商状态")
    @TableField("VDLRSTATUS")
    private String vdlrstatus;

    @Schema(description = "数据-操作员ID")
    @TableField("NOPERATOR")
    private String noperator;

    @Schema(description = "数据-操作日期")
    @TableField("DOPERATOR")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date doperator;

    @Schema(description = "数据-地址电话")
    @TableField(value = "CADDRESS", exist = false)
    private String cAddress;

    @Schema(description = "数据-银行账户")
    @TableField(value = "CBANK", exist = false)
    private String cbank;

    @Schema(description = "数据-购方税号")
    @TableField(value = "CTAXCODE", exist = false)
    private String cTaxCode;

    @Schema(description = "数据-服务范围")
    @TableField("VSVCRANGE")
    private String vsvcrange;


    @Schema(description = "数据-对应经销商id，用于不分公司查询时使用(51公司占50公司计划资源)")
    @TableField("NRELATEDEALERID")
    private String nrelatedealerid;

}
