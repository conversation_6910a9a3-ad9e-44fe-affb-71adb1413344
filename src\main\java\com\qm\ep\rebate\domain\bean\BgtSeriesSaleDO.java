package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 车系销量预测表
 *  
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("bgt_series_sale")
@Schema(description = "数据:车系销量预测表")
public class BgtSeriesSaleDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "数据-滚动销售额标识")
    @TableField("uniqueKey")
    private String uniqueKey;

    @Schema(description = "数据-年度")
    @TableField("sale_year")
    private String saleYear;

    @Schema(description = "数据-月度")
    @TableField("sale_month")
    private String saleMonth;

    @Schema(description = "数据-车系")
    @TableField("series")
    private String series;

    @Schema(description = "数据-车型")
    @TableField("car_model")
    private String carModel;

    @Schema(description = "数据-平均指导价")
    @TableField("average_price")
    private BigDecimal averagePrice;

    @Schema(description = "数据-销量（aak/std）")
    @TableField("sale_count")
    private Long saleCount;

    @Schema(description = "数据-收入（平均指导价*销量）")
    @TableField("sale_amount")
    private BigDecimal saleAmount;

    @Schema(description = "数据-销售类型（00-aak，01-std）")
    @TableField("sale_type")
    private String saleType;

    @Schema(description = "数据-版本号")
    @TableField("version")
    private String version;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

    @Schema(description = "数据-销量更新时间")
    @TableField("sale_count_update_time")
    private Date saleCountUpdateTime;

    @Schema(description = "数据-平均指导价更新时间")
    @TableField("avg_price_update_time")
    private Date avgPriceUpdateTime;

    @Schema(description = "数据-使用状态")
    @TableField("use_status")
    private String useStatus;


    @Schema(description = "数据-使用状态")
    @TableField("publish_status")
    private String publishStatus;

}
