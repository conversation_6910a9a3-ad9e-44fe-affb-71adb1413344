package com.qm.ep.rebate.domain.bean.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "数据:实体类-数据Workbench 信息 do")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkbenchInfoDO {

    @Schema(description = "数据-主键")
    private Integer id;

    @Schema(description = "数据-业务单位代码")
    private String bizUnitCode;

    @Schema(description = "数据-事件代码")
    private String eventCode;

    @Schema(description = "数据-事件名称")
    private String eventName;

    @Schema(description = "数据-类型")
    private String type;

    @Schema(description = "数据-排序")
    private Integer sort;

    @Schema(description = "数据-事件类型")
    private Integer eventType;

    @Schema(description = "数据-数据KA编码")
    private String kaCode;


}