package com.qm.ep.rebate.domain.bean;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 *
 *
 *  
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("DICT_DETAIL")
@Schema(description = "数据:数据对象")
public class DictChildDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-s_codeid")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Excel(name = "字典代码", orderNum = "1")
    @Schema(description = "数据-数据字典")
    @TableField("VDICT")
    private String vdict;

    @Excel(name = "子项代码", orderNum = "2")
    @Schema(description = "数据-代码")
    @TableField("VDICTCHILD")
    private String vdictchild;

    @Excel(name = "子项名称", orderNum = "3")
    @Schema(description = "数据-名称")
    @TableField("VDICTCHILDNAME")
    private String vdictchildname;

    @Excel(name = "扩展标识", orderNum = "5")
    @Schema(description = "数据-扩展")
    @TableField("VEXTAND")
    private String vextand;

    @Excel(name = "扩展标识1", orderNum = "6")
    @Schema(description = "数据-扩展1")
    @TableField("VEXTAND1")
    private String vextand1;

    @Schema(description = "数据-停用标识 1.停用 0.未停用")
    @TableField("CSTOP")
    private String cstop;

    @Schema(description = "数据-停用日期")
    @TableField("DSTOP")
    private Date dstop;

    @Schema(description = "数据-父项代码")
    @TableField("VPARENTCODE")
    private String vparentcode;

    @Excel(name = "备注", orderNum = "4")
    @Schema(description = "数据-备注")
    @TableField("VREMARK")
    private String vremark;

    @Schema(description = "数据-公司")
    @TableField("NCOMPANY")
    private String ncompany;

    @Schema(description = "数据-预置标识")
    @TableField("VPREASSIGN")
    private String vpreassign;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Version
    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(description = "数据-更新时间戳")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-父项字典代码")
    @TableField(value = "VPARENTDICT", exist = false)
    private String vparentdict;

    @Schema(description = "数据-失败原因")
    @TableField(value = "reseaon", exist = false)
    private String reseaon;
}
