package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-04-10 11:11
 * 云原生基础服务返回user信息
 */
@Schema(description = "数据:实体类-云原生基础服务返回user信息")
@Data
public class CloudNativeUserDTO {

    @Schema(description = "数据-登录名")
    private String loginName;

    @Schema(description = "数据-使")
    private Integer enable;

    @Schema(description = "数据-删除")
    private Integer deleted;

    @Schema(description = "数据-名字")
    private String name;

}
