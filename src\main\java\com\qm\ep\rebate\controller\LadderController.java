package com.qm.ep.rebate.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.qm.ep.rebate.domain.bean.*;
import com.qm.ep.rebate.domain.dto.CalcObjectSourceDTO;
import com.qm.ep.rebate.domain.dto.CommonFormulaItemDTO;
import com.qm.ep.rebate.domain.dto.ValidationResultDTO;
import com.qm.ep.rebate.domain.dto.structure.LadderDTO;
import com.qm.ep.rebate.domain.vo.CalcObjectSourceVO;
import com.qm.ep.rebate.domain.vo.CalcObjectVO;
import com.qm.ep.rebate.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebate.enumerate.SourceTypeEnum;
import com.qm.ep.rebate.enumerate.SubCalcObjectTypeEnum;
import com.qm.ep.rebate.mapper.LadderMainMapper;
import com.qm.ep.rebate.remote.feign.RebateCalcFeignClient;
import com.qm.ep.rebate.service.*;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ladder")
@Tag(name = "阶梯配置")
public class LadderController extends BaseController {
    @Autowired
    private LadderService ladderService;
    @Autowired
    private LadderMainService ladderMainService;
    @Autowired
    private LadderContentService ladderContentService;
    @Autowired
    private LadderConditionsService ladderConditionsService;
    @Autowired
    private LadderMainMapper ladderMainMapper;

    @Resource
    private FormulaService formulaService;
    @Resource
    private FormulaItemService formulaItemService;
    @Resource
    private CalcObjectSourceService calcObjectSourceService;

    @Resource
    private RebateCalcFeignClient rebateCalcFeignClient;

    @Operation(summary = "查询底表列表", description = "[author: 10200571]")
    @PostMapping("/getTableList")
    public JsonResultVo<List<CalcObjectSourceVO>> getTableList(@RequestBody CalcObjectSourceDTO tableDTO){
        JsonResultVo<List<CalcObjectSourceVO>> jsonResultVo = new JsonResultVo<>();
        List<CalcObjectSourceVO> list = ladderMainMapper.getDataSourceForLadder(tableDTO);
        jsonResultVo.setData(list);
        return jsonResultVo;
    }

    @Operation(summary = "查询底表列表", description = "[author: 10200571]")
    @PostMapping("/getDataColumn")
    public JsonResultVo<List<CalcObjectVO>> getDataColumn(@RequestBody List<String> tablenameList){
        List<CalcObjectVO> returnList = new ArrayList<>();
        LinkedHashMap<String, List<BusinessConstructionDO>> basicGroup = ladderMainMapper.getDataColumn(tablenameList)
                .stream().collect(Collectors.groupingBy(BusinessConstructionDO::getTableName, LinkedHashMap::new, Collectors.toList()));
        basicGroup.forEach((name, field)->{
            CalcObjectVO cov = new CalcObjectVO();
            cov.setObjectType(CalcObjectTypeEnum.BASIC);
            cov.setObjectName(name);
            cov.setFields(field.stream().map(BusinessConstructionDO::getFieldName).collect(Collectors.toList()));
            returnList.add(cov);
        });
        JsonResultVo<List<CalcObjectVO>> ret = new JsonResultVo<>();
        ret.setData(returnList);
        return ret;
    }


    @Operation(summary = "查询阶梯配置相关信息", description = "[author: 10200571]")
    @PostMapping("/getLadder")
    public JsonResultVo<LadderDTO> getLadder(@RequestBody LadderMainDO tableDO){
        JsonResultVo<LadderDTO> jsonResultVo = new JsonResultVo<>();
        LadderDTO ladderDTO = new LadderDTO();

        QmQueryWrapper<LadderMainDO> queryMainWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<LadderMainDO> lambdaMainWrapper = queryMainWrapper.lambda();
        lambdaMainWrapper.eq(LadderMainDO::getPolicyId, tableDO.getPolicyId());
        LadderMainDO main = ladderMainService.getOne(lambdaMainWrapper);
        ladderDTO.setMain(main);

        if(!BootAppUtil.isNullOrEmpty(main)){
            LambdaQueryWrapper<CalcObjectSourceDO> sourceWrapper = new QmQueryWrapper<CalcObjectSourceDO>().lambda();
            sourceWrapper.eq(CalcObjectSourceDO::getForPolicyId, tableDO.getPolicyId())
                    .eq(CalcObjectSourceDO::getForType, CalcObjectTypeEnum.LADDER);
            List<CalcObjectSourceDO> sourceList = calcObjectSourceService.list(sourceWrapper);
            main.setCalcObjectSources(sourceList);

            QmQueryWrapper<LadderConditionsDO> queryConditionsWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<LadderConditionsDO> lambdaConditionsWrapper = queryConditionsWrapper.lambda();
            lambdaConditionsWrapper.eq(LadderConditionsDO::getLadderId, main.getId());
            lambdaConditionsWrapper.orderByAsc(LadderConditionsDO::getSort);
            List<LadderConditionsDO> conditions = ladderConditionsService.list(lambdaConditionsWrapper);

            ladderDTO.setContents(formulaService.listByObjectId(main.getId(), CalcObjectTypeEnum.LADDER));
            ladderDTO.setConditions(conditions);
        }

        jsonResultVo.setData(ladderDTO);
        return jsonResultVo;
    }


    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/saveLadder")
    public JsonResultVo<String> getLadder(@RequestBody LadderDTO ladderDTO){
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        // 判断能否组织成有效SQl
        ValidationResultDTO validationResultDTO = ladderService.validateConfig(ladderDTO);
        if(!validationResultDTO.getOk()) {
            throw new QmException(validationResultDTO.getMsg());
        }
        String ladderId;
        if(BootAppUtil.isNullOrEmpty(ladderDTO.getMain().getId())){
            //  新建
            ladderId = IdWorker.getIdStr();
            ladderDTO.getMain().setId(ladderId);
            ladderService.save(ladderDTO);
        }else{
            //  编辑
            ladderId = ladderDTO.getMain().getId();
            ladderMainService.deleteMain(ladderDTO.getMain().getPolicyId());
            formulaService.removeByObjectIds(Collections.singletonList(ladderId), CalcObjectTypeEnum.LADDER);
            ladderConditionsService.deleteConditions(Collections.singletonList(ladderId));
            calcObjectSourceService.removeByForObjectId(ladderId, CalcObjectTypeEnum.LADDER);
            ladderService.save(ladderDTO);
        }
        jsonResultVo.setData(ladderId);
        return jsonResultVo;
    }

    @Operation(summary = "删除", description = "[author: 10200571]")
    @PostMapping("/deleteById")
    public JsonResultVo<String> deleteById(@RequestBody LadderMainDO main){
        JsonResultVo<String> jsonResultVo = new JsonResultVo<>();
        List<String> ladderIds = new ArrayList<>();
        ladderIds.add(main.getId());
        ladderMainService.deleteMain(main.getPolicyId());
        formulaService.removeByObjectIds(ladderIds, CalcObjectTypeEnum.LADDER);
        ladderConditionsService.deleteConditions(ladderIds);
        calcObjectSourceService.removeByForObjectIds(ladderIds, CalcObjectTypeEnum.LADDER);
        rebateCalcFeignClient.deleteByCalcObjectIds(ladderIds);
        return jsonResultVo;
    }

    @Operation(summary = "迁移公式内容数据", description = "[author: 10200571]")
    @PostMapping("/migrateFormulaData")
    public void migrateFormulaData(){
        List<LadderContentDO> formulaContentDOS = ladderContentService.list();
        formulaContentDOS.forEach(f->{
            FormulaDO formulaDO = new FormulaDO();
            formulaDO.setPolicyId(f.getPolicyId());
            formulaDO.setObjectId(f.getLadderId());
            formulaDO.setObjectType(CalcObjectTypeEnum.LADDER);
            formulaDO.setName(f.getName());
            formulaDO.setDecimal(f.getDecimal());
            formulaDO.setRule(f.getRule());
            formulaDO.setFetchType(f.getFetchType());
            formulaDO.setCreateOn(f.getCreateOn());
            formulaDO.setCreateBy(f.getCreateBy());
            formulaDO.setUpdateOn(f.getUpdateOn());
            formulaDO.setUpdateBy(f.getUpdateBy());
            formulaDO.setDtstamp(f.getDtstamp());
            formulaService.save(formulaDO);

            List<CommonFormulaItemDTO> formulaContents = JSONUtils.packingDOListFromJsonStr(f.getContent(), CommonFormulaItemDTO.class);
            formulaContents.forEach(i->{
                FormulaItemDO formulaItemDO = new FormulaItemDO();
                formulaItemDO.setFormulaId(formulaDO.getId());
                formulaItemDO.setValue(i.getValue());
                formulaItemDO.setType(i.getType());
                formulaItemDO.setTableName(i.getTableName());
                formulaItemDO.setTableField(i.getTableField());
                formulaItemDO.setRule(i.getRule());
                formulaItemService.save(formulaItemDO);
            });
        });
    }

    @Operation(summary = "控制器接口", description = "[author: 10200571]")
    @PostMapping("/turnDataSource")
    public JsonResultVo turnDataSource() {
        JsonResultVo<Object> res = new JsonResultVo<>();
        List<LadderMainDO> mainDOList = ladderMainService.list();
        mainDOList.forEach(main -> {
            String policyId = main.getPolicyId();
            String ladderId = main.getId();
            String calcObjects = main.getCalcObjects();
            if(StringUtils.isNotEmpty(calcObjects) && calcObjects.contains("{")){
                JSONArray jsonArray = JSON.parseArray(calcObjects);
                List<CalcObjectSourceDO> sourceDOList = new ArrayList<>();
                for(int i = 0; i < jsonArray.size(); i++){
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String subObjectType = jsonObject.getString("tableType");
                    String objectName = jsonObject.getString("tableName");

                    CalcObjectSourceDO sourceDO = new CalcObjectSourceDO();
                    sourceDO.setForPolicyId(policyId);
                    sourceDO.setForObjectId(ladderId);
                    sourceDO.setForType(CalcObjectTypeEnum.LADDER);
                    sourceDO.setForSourceType(SourceTypeEnum.MAIN);
                    sourceDO.setObjectName(objectName);
                    sourceDO.setObjectType(CalcObjectTypeEnum.BASIC);
                    sourceDO.setSubObjectType(SubCalcObjectTypeEnum.getByCode(subObjectType));
                    sourceDOList.add(sourceDO);
                }

                calcObjectSourceService.saveBatch(sourceDOList);
                List<String> idList = sourceDOList.stream().map(CalcObjectSourceDO::getId).collect(Collectors.toList());
                main.setCalcObjects(CharSequenceUtil.join(",", idList));
                main.setDtstamp(DateUtils.getSysdateTime());
                ladderMainService.updateById(main);
            }
        });
        return res;
    }
}
