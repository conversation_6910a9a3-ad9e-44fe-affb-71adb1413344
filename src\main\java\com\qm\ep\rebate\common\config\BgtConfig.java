package com.qm.ep.rebate.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;


/**
 * 预算分解配置信息
 *
 * <AUTHOR>
 * @date 2024/09/23
 */
@Configuration
@RefreshScope
@Data
public class BgtConfig {

    @Value("${bgt.breakdown.bgtBreakDownUser:wangxin213}")
    private String bgtBreakDownUser;

    @Value("${bgt.breakdown.bgtBreakDownUserName:王鑫}")
    private String bgtBreakDownUserName;

    @Value("${bgt.breakdown.bgtPolicyMaintainer:songwenchao_icss}")
    private String bgtPolicyMaintainer;
    @Value("${bgt.breakdown.adjustHandleSubmitCode:songwanze}")
    private String adjustHandleSubmitCode;
    @Value("${bgt.breakdown.adjustNeedJumpPath:/zhengce_virtual/policySetAudit}")
    private String adjustNeedJumpPath;
    @Value("${bgt.breakdown.isValid:true}")
    private boolean isValid;

    @Value("${bgt.breakdown.validDate:2024-01-01}")
    private String validDate;

    @Value("${bgt.breakdown.eom:songwenchao_icss}")
    private String eom;

    // 财务下发车系点数时，如果以财务下发为准，该值为close,如果以当前商务政策内的车系为准，该值为open
    @Value("${bgt.breakdown.eomSwitch:close}")
    private String eomSwitch;

    @Value("${bgt.eom.targetCode:BZ2411000001}")
    private String eomTargetCode;

}
