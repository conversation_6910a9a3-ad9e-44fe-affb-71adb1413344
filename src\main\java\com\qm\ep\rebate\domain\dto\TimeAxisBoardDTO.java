package com.qm.ep.rebate.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "政策节点跟踪看板")
@Data
public class TimeAxisBoardDTO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "政策ID")
    private String policyId;

    @Schema(description = "政策代码")
    private String policyCode;

    @Schema(description = "政策名称")
    private String policyName;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "节点状态")
    private String nodestate;

    @Schema(description = "逾期时长")
    private String overdue;

    @Schema(description = "创建部门")
    private String department;

    @Schema(description = "创建人")
    private String createby;

    @Schema(description = "政策产品")
    private String series;

    @Schema(description = "政策状态")
    private String stage;

    @Schema(description = "预算金额")
    private BigDecimal budgetAmount;

    @Schema(description = "实际结算金额")
    private BigDecimal actualAmount;

    @Schema(description = "结算差额")
    private BigDecimal difference;

    @Schema(description = "执行开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime dbegin;

    @Schema(description = "执行结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime dend;

    @Schema(description = "政策文件发布")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime publishDate;

    @Schema(description = "政策配置应发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime policyPublishPlan;

    @Schema(description = "实际政策配置发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime policyPublish;

    @Schema(description = "政策应配置时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime policyFinishPlan;

    @Schema(description = "政策配置时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime policyFinish;

    @Schema(description = "政策计算日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime policyCalcPlan;

    @Schema(description = "政策计算完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime policyCalc;

    @Schema(description = "政策应兑付申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime applyEntry;

    @Schema(description = "实际政策兑付申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime applyTime;

    @Schema(description = "财务应兑付完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime enrtyFinish;

    @Schema(description = "实际财务兑付完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime passTime;

    @Schema(description = "政策应分配时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime allocationTimePlan;

    @Schema(description = "实际政策分配时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime allocationTime;

    @Schema(description = "发布时长")
    private long publishLag;

    @Schema(description = "完成时长")
    private long finishLag;

    @Schema(description = "计算时长")
    private long calcLag;

    @Schema(description = "申请时长")
    private long appayLag;

    @Schema(description = "入账时长")
    private long entryLag;

    @Schema(description = "分配时长")
    private long allocationLag;
}
