package com.qm.ep.rebate.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 *  
 * 产品视图
 *  
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Schema(description = "数据:产品视图")
@Data
public class VproductDTO extends JsonParamDto {

    @Schema(description = "数据-序列化ID")
    private static final long serialVersionUID = 1L;
    @Schema(description = "数据-技术项组合ID")
    private String nconfigitemid;
    @Schema(description = "数据-技术项组合实例ID（产品ID）")
    private String nproductid;
    @Schema(description = "数据-技术项组合实例代码（产品代码）")
    private String vproductcode;
    @Schema(description = "数据-产品代码简称")
    private String vproducttext;
    @Schema(description = "数据-产品代码名称")
    private String vproductlongtext;
    @Schema(description = "数据-停用标识")
    private String vstop;
    @Schema(description = "数据-停用日期")
    private Date dstop;
    @Schema(description = "数据-产品内码")
    private String vinnerpdtcode;
    @Schema(description = "数据-内部产品码描述")
    private String vinnerpdtcodetext;
    @Schema(description = "数据-时间戳: ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date dtstamp;
    @Schema(description = "数据-其它")
    private String vothers;
    @Schema(description = "数据-平台ID   ")
    private String nflat;
    @Schema(description = "数据-平台代码")
    private String vflat;
    @Schema(description = "数据-平台名称")
    private String vflattext;
    @Schema(description = "数据-品牌")
    private String nbrand;
    @Schema(description = "数据-品牌代码 ")
    private String vbrand;
    @Schema(description = "数据-品牌名称")
    private String vbrandtext;
    @Schema(description = "数据-产品系列ID    ")
    private String npdtseries;
    @Schema(description = "数据-产品系列代码")
    private String vpdtseries;
    @Schema(description = "数据-产品系列名称")
    private String vpdtseriestext;
    @Schema(description = "数据-车型大类/车辆型号代码")
    private String vvehicletype;
    @Schema(description = "数据-车辆型号ID")
    private String nvehicletype;
    @Schema(description = "数据-车辆型号名称")
    private String vvehicletypetext;
    @Schema(description = "数据-销售车型ID  ")
    private String nsaltype;
    @Schema(description = "数据-销售车型")
    private String vsaltype;
    @Schema(description = "数据-销售车型名称")
    private String vsaltypetext;
    @Schema(description = "数据-车身颜色ID    ")
    private String ncolor;
    @Schema(description = "数据-车身颜色代码")
    private String vcolor;
    @Schema(description = "数据-车身颜色名称")
    private String vcolortext;
    @Schema(description = "数据-内饰+颜色ID")
    private String ncabtype;
    @Schema(description = "数据-内饰+颜色代码")
    private String vcabtype;
    @Schema(description = "数据-内饰+颜色名称")
    private String vcabtypetext;
    @Schema(description = "数据-服务车系ID")
    private String nservicevehiclepdt;
    @Schema(description = "数据-服务车系代码")
    private String vservicevehiclepdt;
    @Schema(description = "数据-服务车系名称")
    private String vservicevehiclepdttext;
    @Schema(description = "数据-畅销类别号ID")
    private String nreadysaletype;
    @Schema(description = "数据-畅销类别代码")
    private String vreadysaletype;
    @Schema(description = "数据-畅销类别描述")
    private String vreadysaletypetext;
    @Schema(description = "数据-经销商ID")
    private String ndealerid;
    @Schema(description = "数据-产品域")
    private String vsalearea;
    @Schema(description = "数据-事务组")
    private String vtransgroup;
    @Schema(description = "数据-计划类型")
    private String vplantype;
    @Schema(description = "数据-销售返利对应产品类型")
    private String vapplyprdt;
    @Schema(description = "数据-主键")
    private String id;
    @Schema(description = "数据-品牌")
    private String brand;
}