package com.qm.ep.rebate.domain.dto.workbench;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "数据:实体类-任务实例 DTO")
@Data
public class TaskInstanceDTO {

    /**
     * 任务实例编码
     */
    @Schema(description = "数据-任务实例编码")
    private String taskInstanceCode;

    /**
     * 接收人编号
     */
    @Schema(description = "数据-接收人编号")
    private String distributeUserCode;

    /**
     * 任务下达时间
     */
    @Schema(description = "数据-任务下达时间")
    private String createTime;

    /**
     * 任务预期时间
     */
    @Schema(description = "数据-任务预期时间")
    private String overdueTime;

    /**
     * 实际完成时间
     */
    @Schema(description = "数据-实际完成时间")
    private String completeTime;

    /**
     * 描述
     */
    @Schema(description = "数据-描述")
    private String description;

    /**
     * 处理系统类型（1:能力中心，0:其他）
     */
    @Schema(description = "数据-处理系统类型（1:能力中心，0:其他）")
    private Integer processingSystemType;

    /**
     * 可选url
     */
    @Schema(description = "数据-可选url")
    private String url;

    /**
     * 能力中心
     */
    @Schema(description = "数据-能力中心")
    private String competence;

    /**
     * 能力中心名称
     */
    @Schema(description = "数据-能力中心名称")
    private String competenceName;

    /**
     * 菜单页面
     */
    @Schema(description = "数据-菜单页面")
    private String menuPage;

    /**
     * 菜单名称
     */
    @Schema(description = "数据-菜单名称")
    private String menuPageName;

    /**
     * 角色名称
     */
    @Schema(description = "数据-角色名称")
    private String roleName;

    /**
     * 角色编码
     */
    @Schema(description = "数据-角色编码")
    private String roleCode;

    /**
     * 任务实例状态（0：待办，1：完成）
     */
    @Schema(description = "数据-任务实例状态（0：待办，1：完成）")
    private Integer status;

    /**
     * 业务主线
     */
    @Schema(description = "数据-业务主线")
    private String bizMainLine;

    /**
     * 标准工时(h)
     */
    @Schema(description = "数据-标准工时(h)")
    private String stdTime;

    /**
     * 业务单元位置START-开始 MIDDLE-中间 END - 结束
     */
    @Schema(description = "数据-业务单元位置START-开始 MIDDLE-中间 END - 结束")
    private String position;

    /**
     * 关闭类型 0：手动，3：事件
     */
    @Schema(description = "数据-关闭类型 0：手动，3：事件")
    private Integer closerType;

    /**
     * 是否允许强制关闭（1：是，0：否）
     */
    @Schema(description = "数据-是否允许强制关闭（1：是，0：否）")
    private boolean enableForceClose;

    /**
     * 是否允许上传文件（1：是，0：否）
     */
    @Schema(description = "数据-是否允许上传文件（1：是，0：否）")
    private boolean enableUploadFile;

    /**
     * 事件类型关闭器：能力中心注册的事件关闭code
     */
    @Schema(description = "数据-事件类型关闭器：能力中心注册的事件关闭code")
    private String eventCode;

    /**
     * 事件类型关闭器：能力中心注册的事件关闭name
     */
    @Schema(description = "数据-事件类型关闭器：能力中心注册的事件关闭name")
    private String eventName;

}
