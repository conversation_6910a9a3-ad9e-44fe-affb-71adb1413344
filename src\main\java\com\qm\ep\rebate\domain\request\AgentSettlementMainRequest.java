package com.qm.ep.rebate.domain.request;

import com.qm.ep.rebate.domain.vo.sal.AgentSettlementDetailVO;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目名称：rebate01-1
 * 类 名 称：AgentSettlementMainRequest
 * 类 描 述：TODO
 * 创建时间：2024/3/6 下午4:59
 * 创 建 人：cuihaochuan
 */
@Data
@Schema(description = "数据:结算单主表")
public class AgentSettlementMainRequest extends JsonParamDto {
    /**
     * id
     */
    @Schema(description = "数据-主键")
    private String id;
    /**
     * 结算单号(经销商代码+YYYYMMDD+3位流水)
     */
    @Schema(description = "数据-结算单号")
    private String settleNo;
    /**
     * 车系名称
     */
    @Schema(description = "数据-车系名称")
    private List<String> series;

    /**
     * 代理商代码
     */
    @Schema(description = "数据-代理商代码")
    private String vdealerCode;
    /**
     * 代理商名称
     */
    @Schema(description = "数据-代理商名称")
    private String vdealerName;

    /**
     * 税率
     */
    @Schema(description = "数据-税率")
    private String taxRate;

    /**
     * 报账标识，0初始，1报账占用，2已报销
     */
    @Schema(description = "数据-报账标识，0初始，1报账占用，2已报销")
    private String sald001Flag;
    /**
     * 结算单状态(0-录入，1-提交)
     */
    @Schema(description = "数据-结算状态")
    private Integer settleStatus;
    /**
     * 结算金额
     */
    @Schema(description = "数据-结算金额")
    private BigDecimal settleAmount;
    /**
     * 结算日期开始
     */
    @Schema(description = "数据-结算开始日期")
    private String startCreateDate;

    /**
     * 结算日期结束
     */
    @Schema(description = "数据-结算结束日期")
    private String endCreateDate;


    @Schema(description = "数据-结算单提交日期")
    private String settleSubmitDate;
    /**
     * 使用状态（0-未使用，1-正使用）
     */
    @Schema(description = "数据-使用状态")
    private Integer useStatus;
    /**
     * 扩展字段
     */
    @Schema(description = "数据-扩展字段")
    private String ext;

    @Schema(description = "数据-结算明细")
    private List<AgentSettlementDetailVO> items;

    @Schema(description = "数据-经销商Id")
    private String companyId;

    @Schema(description = "数据-经销商Code")
    private String ownerCode;

    @Schema(description = "数据-操作人")
    private String operator;
    /**
     * 代理商ID
     */
    @Schema(description = "数据-代理商ID")
    private String mdac100Id;

    @Schema(description = "数据-创建者")
    private String createBy;

    @Schema(description = "数据-创建日期")
    private Date createOn;

    @Schema(description = "数据-更新者")
    private String updateBy;

    @Schema(description = "数据-更新日期")
    private Date updateOn;

    @Schema(description = "数据-时间戳")
    private Date dtstamp;

}
