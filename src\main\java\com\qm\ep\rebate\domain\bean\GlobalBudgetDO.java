package com.qm.ep.rebate.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("global_budget")
@Schema(description = "数据:总体预算编制对象")
public class GlobalBudgetDO implements Serializable {

    @Schema(description = "数据-串行版 UID")
    private static final long serialVersionUID = 1L;

    @Schema(description = "数据-主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "数据-组织代码")
    @TableField("orgCode")
    private String orgCode;

    @Schema(description = "数据-总体预算编制版本")
    @TableField("budgetVersion")
    private String budgetVersion;

    @Schema(description = "数据-业务目标版本")
    @TableField("aimVersion")
    private String aimVersion;

    @Schema(description = "数据-年份")
    @TableField("y")
    private Integer y;

    @Schema(description = "数据-状态")
    @TableField("status")
    private String status;

    @Schema(description = "数据-预算编制模型")
    @TableField("budgetModel")
    private String budgetModel;

    @Schema(description = "数据-预算编制模型名称")
    @TableField(exist = false)
    private String budgetModelName;

    @Schema(description = "数据-预算编制模型计算结果版本")
    @TableField("modelCalcVersion")
    private String modelCalcVersion;

    @Schema(description = "数据-预算编制模型计算结果版本")
    @TableField(exist = false)
    private String modelCalcVersionNum;

    @Schema(description = "数据-是否已插入其他收益")
    @TableField("insertOtherProfit")
    private Boolean insertOtherProfit;

    @Schema(description = "数据-备注")
    @TableField("remark")
    private String remark;

    @Schema(description = "数据-下达时间")
    @TableField(value = "sendDate")
    private Date sendDate;

    @Schema(description = "数据-创建者")
    @TableField(value = "CREATEBY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "数据-创建日期")
    @TableField(value = "CREATEON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "数据-更新者")
    @TableField(value = "UPDATEBY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "数据-更新日期")
    @TableField(value = "UPDATEON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "数据-创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "数据-更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "数据-时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;

}
