package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.DictMainDO;
import com.qm.ep.rebate.domain.dto.DictMainDTO;
import com.qm.ep.rebate.service.DictMainService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.util.RedisUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 前端控制
 *
 *
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@RestController
@RequestMapping("/dictMain")
@Tag(name = "字典管理")
@Slf4j
public class DictMainController extends BaseController {

    @Autowired
    private DictMainService dictMainService;

    @Autowired
    private RedisUtils redisUtils;

    @Operation(summary = "保存", description = "[author: 10200571]")
    @PostMapping("/save")
    public JsonResultVo<DictMainDO> save(@RequestBody DictMainDO dictMainDO) {
        return dictMainService.saveDictMain(dictMainDO);
    }


    @Operation(summary = "查询", description = "[author: 10200571]")
    @PostMapping("/queryTable")
    public JsonResultVo<QmPage<DictMainDO>> queryTable(@RequestBody DictMainDTO tempDTO) {
        JsonResultVo<QmPage<DictMainDO>> resultVo = new JsonResultVo<>();
        QmPage<DictMainDO> qmPage = dictMainService.queryTable(tempDTO);
        resultVo.setData(qmPage);
        return resultVo;
    }
}
