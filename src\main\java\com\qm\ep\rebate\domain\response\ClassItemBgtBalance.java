package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 返利项目预算余额
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Schema(description = "数据:返利项目预算余额")
@Data
public class ClassItemBgtBalance {

    /**
     * 车系
     */
    @Schema(description = "数据-车系")
    private String series;

    /**
     * 返利项目余额（元）
     */
    @Schema(description = "数据-返利项目余额（元）")
    private BigDecimal balance;

    /**
     * 预算分配金额（元）
     */
    @Schema(description = "数据-预算分配金额（元）")
    private BigDecimal amount;

    /**
     * 已占用金额（元）
     */
    @Schema(description = "已占用金额（元）")
    private BigDecimal allocatedAmount;
}
