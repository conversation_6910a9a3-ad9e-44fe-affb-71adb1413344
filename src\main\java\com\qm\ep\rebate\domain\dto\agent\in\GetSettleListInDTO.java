package com.qm.ep.rebate.domain.dto.agent.in;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "查询结算单")
public class GetSettleListInDTO {

    @Schema(description = "数据-开始时间")
    private String begin;

    @Schema(description = "数据-结束时间")
    private String end;

    @Schema(description = "数据-税率")
    private String taxRate;

    @Schema(description = "数据-经销商代码")
    private String vdealerCode;

}
