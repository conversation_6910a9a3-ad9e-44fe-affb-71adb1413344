package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "特殊因子响应")
@Data
public class SpecialFactorResponse {
    @Schema(description = "条数")
    private int specialCount;

    @Schema(description = "入账明细")
    private List<Detail> details;

    @Schema(description = "入账明细")
    @Data
    public static class Detail {
        @Schema(description = "因子名称")
        private String factorName;
        @Schema(description = "经销商代码")
        private String dealerCode;
        @Schema(description = "系列")
        private String series;
        @Schema(description = "数据VIN")
        private String vin;
        @Schema(description = "返利金额")
        private String rebateAmount;
    }

}
