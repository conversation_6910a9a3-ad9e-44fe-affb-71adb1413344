package com.qm.ep.rebate.controller;

import com.qm.ep.rebate.domain.bean.BusinessConstructionDO;
import com.qm.ep.rebate.service.BusinessConstructionService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/businessConstruction")
@Tag(name = "业务结构", description = "[author: 10200571]")
public class BusinessConstructionController extends BaseController {

    @Autowired
    private BusinessConstructionService businessConstructionService;

    @Operation(summary = "获取数据源", description = "[author: 10200571]")
    @GetMapping("/getDataSource")
    public JsonResultVo<String> getDataSource() {
        JsonResultVo<String> result = new JsonResultVo<>();
        LoginKeyDO loginKeyDO = this.getUserInfo();
        result.setDataList(businessConstructionService.getDataSource(loginKeyDO));
        return result;
    }

    @Operation(summary = "获取取值字段", description = "[author: 10200571]")
    @GetMapping("/getDataColumn")
    public JsonResultVo<String> getDataColumn(String tableNames) {
        JsonResultVo<String> result = new JsonResultVo<>();
        result.setDataList(businessConstructionService.getDataColumn(tableNames));
        return result;
    }

    @Operation(summary = "获取数据源字段名和字段ID", description = "[author: 10200571]")
    @GetMapping("/getDataColumnAndId")
    public JsonResultVo<BusinessConstructionDO> getDataColumnAndId(String tableName){
        JsonResultVo<BusinessConstructionDO> result = new JsonResultVo<>();
        result.setDataList(businessConstructionService.getDataColumnAndId(tableName));
        return result;
    }

}
