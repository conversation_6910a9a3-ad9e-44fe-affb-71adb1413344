package com.qm.ep.rebate.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Schema(description = "数据:实体类-基本枚举 vo")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseEnumVO {
    @Schema(description = "数据-法典")
    private String code;
    @Schema(description = "数据-价值")
    private String value;
}

