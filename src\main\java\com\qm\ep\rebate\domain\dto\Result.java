package com.qm.ep.rebate.domain.dto;


import com.qm.ep.rebate.enumerate.ResultCode;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

@Tag(name = "接口功能", description = "[author: 10200571]")
public class Result<T> {
    @Schema(description = "数据-成功")
    private boolean success;
    @Schema(description = "数据-状态码")
    private long code;
    @Schema(description = "数据-提示信息")
    private String message;
    @Schema(description = "数据-返回值")
    private T data;

    private Result() {
    }

    private Result(boolean success, long code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> Result<T> success(T data) {
        return new Result(true, ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    public static <T> Result<T> success(T data, String message) {
        return new Result(true, ResultCode.SUCCESS.getCode(), message, data);
    }

    public static <T> Result<T> failed() {
        return failed(ResultCode.FAILED_Message.getMessage());
    }

    public static <T> Result<T> failed(String message) {
        return new Result(false, ResultCode.FAILED_Message.getCode(), message, (Object)null);
    }

    public static <T> Result<T> failed(Throwable throwable) {
        return new Result(false, ResultCode.FAILED_Exception.getCode(), throwable2String(throwable), (Object)null);
    }

    private static String throwable2String(Throwable throwable) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            throwable.printStackTrace(pw);
            sw.close();
            pw.close();
            return "\r\n" + sw + "\r\n";
        } catch (Exception var3) {
            return "ErrorInfoFromException";
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> vals = new HashMap();
        vals.put("success", this.success);
        vals.put("code", this.code);
        vals.put("message", this.message);
        vals.put("data", this.data);
        return vals;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public long getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public T getData() {
        return this.data;
    }

    public Result<T> setSuccess(final boolean success) {
        this.success = success;
        return this;
    }

    public Result<T> setCode(final long code) {
        this.code = code;
        return this;
    }

    public Result<T> setMessage(final String message) {
        this.message = message;
        return this;
    }

    public Result<T> setData(final T data) {
        this.data = data;
        return this;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof Result)) {
            return false;
        } else {
            Result<?> other = (Result)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (this.isSuccess() != other.isSuccess()) {
                return false;
            } else if (this.getCode() != other.getCode()) {
                return false;
            } else {
                label40: {
                    Object this$message = this.getMessage();
                    Object other$message = other.getMessage();
                    if (this$message == null) {
                        if (other$message == null) {
                            break label40;
                        }
                    } else if (this$message.equals(other$message)) {
                        break label40;
                    }

                    return false;
                }

                Object this$data = this.getData();
                Object other$data = other.getData();
                if (this$data == null) {
                    if (other$data != null) {
                        return false;
                    }
                } else if (!this$data.equals(other$data)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof Result;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        result = result * 59 + (this.isSuccess() ? 79 : 97);
        long $code = this.getCode();
        result = result * 59 + (int)($code >>> 32 ^ $code);
        Object $message = this.getMessage();
        result = result * 59 + ($message == null ? 43 : $message.hashCode());
        Object $data = this.getData();
        result = result * 59 + ($data == null ? 43 : $data.hashCode());
        return result;
    }

    public String toString() {
        return "Result(success=" + this.isSuccess() + ", code=" + this.getCode() + ", message=" + this.getMessage() + ", data=" + this.getData() + ")";
    }
}
